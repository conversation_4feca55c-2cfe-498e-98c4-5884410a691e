package com.dcube.biz.constant.enums;

import lombok.Getter;

/**
 * 数据源类型
 */
@Getter
public enum SourceTypeEnum {

    MySQL("MySQL", "MySQL", "com.mysql.cj.jdbc.Driver", "jdbc:mysql://"),

    Oracle("Oracle", "Oracle", "oracle.jdbc.OracleDriver", "jdbc:oracle:thin:"),

    SQLServer("SQLServer", "SQL Server", "com.microsoft.sqlserver.jdbc.SQLServerDriver", "jdbc:microsoft:sqlserver://"),

    SQLite("SQLite", "SQLite", "org.sqlite.JDBC", "jdbc:sqlite:"),

    Mongodb("Mongodb", "Mongdb", "com.mongo.jdbc.Driver", "mongodb://"),
    Excel("Excel", "Excel", "", ""),
    Text("Text", "Text", "", ""),
    Dat("Dat", "Dat", "", ""),
    DMDBMS("DMDBMS", "DM DBMS", "dm.jdbc.driver.DmDriver", "jdbc:dm://"),
    CLICKHOUSE("CLICKHOUSE", "CLICK HOUSE", "ru.yandex.clickhouse.ClickHouseDriver", "jdbc:clickhouse://"),
    PostgreSQL("PostgreSQL", "PostgreSQL", "org.postgresql.Driver", "jdbc:postgresql://"),
    GaussDB("GaussDB", "GaussDB", "com.huawei.gauss200.jdbc.Driver", "jdbc:gaussdb://"),

    ;

    final String code;
    final String name;

    final String driverClassName;

    final String url;

    SourceTypeEnum(String code, String name, String driverClassName, String url) {
        this.name = name;
        this.code = code;
        this.driverClassName = driverClassName;
        this.url = url;
    }

}
