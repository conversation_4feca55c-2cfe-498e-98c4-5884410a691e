package com.dcube.biz.constant.enums;

import lombok.Getter;

/**
 * @创建人 zhouhx
 * @创建时间 2023/8/25 15:30
 * @描述
 */
@Getter
public enum StorageUnitEnum {

    B(1L,"B", "字节"),

    KB(1024L,"KB", "千字节"),

    MB(1024L*1024L,"MB", "兆字节"),

    GB(1024L*1024L*1024L,"GB", "千兆字节");

    final Long size;
    final String code;
    final String name;

    StorageUnitEnum(Long size, String name, String code) {
        this.size = size;
        this.name = name;
        this.code = code;
    }
}
