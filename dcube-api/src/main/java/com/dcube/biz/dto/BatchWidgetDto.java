package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.DashboardConfigJson;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @创建人 zhouhx
 * @创建时间 2024/5/27 09:39
 * @描述
 */
@Data
public class BatchWidgetDto extends BaseDto {

    private List<WidgetDto> widgets;

    @Schema(description = "仪表板ID", example = "仪表板ID，必传")
    private String dashboardId;

    @Schema(description = "dashboard配置", example = "dashboard配置，必传")
    private DashboardConfigJson dashboardConfig;
}
