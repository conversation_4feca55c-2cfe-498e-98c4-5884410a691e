package com.dcube.biz.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @创建人 zhouhx
 * @创建时间 2023/3/15 11:22
 * @描述
 */
@Data
public class CellDto {

    @Schema(description = "字段编码")
    private String code;

    @Schema(description = "字段类型-oldColumnType")
    private String type;

    @Schema(description = "字段值-单元格中输入的值")
    private String cellValue;

    @Schema(description = "字段值-单元格中原始的值")
    private Object originalValue;

    @Schema(description = "字段值-单元格中显示的值")
    private String displayValue;

    @Schema(description = "数据格式错误信息")
    private String formatException;

//    @Schema(description = "执行计算规则的错误信息")
//    private String computeException;
}
