package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Data
public class DicItemDto extends BaseDto {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 字典ID
     */
    @Schema(description = "字典ID")
    private String dicId;

    /**
     * 存储编码
     */
    @Schema(description = "存储编码")
    private String dicCode;

    /**
     * 显示文字
     */
    @Schema(description = "显示文字")
    private String dicLabel;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;
}
