package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class IndDto extends BaseDto {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 指标名称
     */
    @Schema(description = "指标名称")
    private String indName;

    /**
     * 指标类型（分组或指标实例）
     */
    @Schema(description = "指标类型（分组或指标实例）")
    private String indType;

    /**
     * 显示序号
     */
    @Schema(description = "显示序号")
    private Integer indexNo;

    /**
     * 上级目录ID
     */
    @Schema(description = "上级目录ID")
    private Integer parentId;

    /**
     * 数据格式id
     */
    @Schema(description = "数据格式id")
    private String dataFormatId;

    /**
     * 聚合函数名称
     */
    @Schema(description = "聚合函数名称")
    private String functionName;

    /**
     * 聚合函数表达式
     */
    @Schema(description = "聚合函数表达式")
    private String functionValue;

    /**
     * 加权平均指标ID
     */
    @Schema(description = "加权平均指标ID")
    private String avgParmaIndId;
}
