package com.dcube.biz.dto;

import com.dcube.biz.constant.enums.DimTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@ToString
public class LayoutDimDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "筛选维度ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer id;

    @Schema(description = "维度类型（dim：维度；ind：指标）")
    private DimTypeEnum dimType;
}