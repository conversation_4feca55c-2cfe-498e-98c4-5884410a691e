package com.dcube.biz.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class LayoutFilterDimDTO extends LayoutDimDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "选中维值集合")
    private List<String> filterValues;
}