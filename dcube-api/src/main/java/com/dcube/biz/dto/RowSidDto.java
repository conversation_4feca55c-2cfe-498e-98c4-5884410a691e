package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class RowSidDto extends BaseDto {

    /**
     * 维度ID与维度实例映射集合
     */
    private Map<Integer, Integer> rowDimKeyMap = new HashMap<>();

    /**
     * 指标ID
     */
    private Integer columnKey;

    /**
     * 是否叶子
     */
    private boolean isLeaf = true;
}
