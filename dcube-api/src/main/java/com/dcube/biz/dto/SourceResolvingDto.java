package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.SourceConfigJson;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 数据文件解析Dto
 */
@Data
public class SourceResolvingDto extends BaseDto {

    @Schema(description = "主键ID，必传")
    private String id;

    @Schema(description = "数据源名称，必传")
    private String sourceName;

    @Schema(description = "数据文件类型，必传")
    private String dataFileType;

    @Schema(description = "数据文件目录，Text数据文件必传")
    private String dataFileDir;

    @Schema(description = "目录日期格式，Text数据文件必传")
    private String dirDateFormat;

    @Schema(description = "字段分割符，Text数据文件必传")
    private String splitter;

    @Schema(description = "数据文件后缀，Text数据文件必传")
    private String dataFileSuffix;

    @Schema(description = "ok文件后缀，Text数据文件必传")
    private String okFileSuffix;

    @Schema(description = "Excel文件路径，数据文件格式为Excel时必传，其他格式传空")
    private String filePath;
}
