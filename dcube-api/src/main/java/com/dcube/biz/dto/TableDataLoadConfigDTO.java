package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class TableDataLoadConfigDTO extends BaseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 二维表Id
     */
    @Schema(description = "二维表Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tableId;

    /**
     * 数据视图Id
     */
    @Schema(description = "数据视图Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String viewId;

    /**
     * 二维表列编码
     */
    @Schema(description = "二维表列编码")
    private String tableColumnCode;

    /**
     * 数据视图列编码
     */
    @Schema(description = "数据视图列编码")
    private String viewColumnCode;

}
