package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import com.dcube.biz.json.TableMetaJson;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 二维表
 *
 * <AUTHOR>
 * @date 2023-02-22
 */
@Data
public class TableDto extends BaseDto {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 目录ID
     */
    @Schema(description = "目录ID")
    private String directoryId;

    /**
     * 表名
     */
    @Schema(description = "表名")
    private String tableName;

    /**
     * 建表方式
     */
    @Schema(description = "建表方式")
    private String createMode;

    /**
     * 数据视图ID
     */
    @Schema(description = "数据视图ID")
    private String viewId;

    /**
     * 子表标记
     */
    @Schema(description = "子表标记")
    private String childFlag;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 表格元数据
     */
    @Schema(description = "表格元数据")
    private List<TableMetaJson> tableMetaJson;

    @Schema(description = "父Id")
    private Integer parentId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "祖级列表")
    private String ancestors;

    @Schema(description = "内存数据库表名")
    private String memTableName;

    @Schema(description = "主键名称")
    private String primaryColumnName;

    @Schema(description = "适配db和内存表字段")
    private boolean matchFields = false;

    @Schema(description = "备份表名称")
    private String backupTableName;

    @Schema(description = "父表关联主键废弃，使用parentRelKeyList")
    @Deprecated
    private String parentRelKey;

    @Schema(description = "父表关联主键List")
    private List<String> parentRelKeyList;

    @Schema(description = "备份表版本")
    private String backupVersion;

    @Schema(description = "数据日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date baseDataDt;

    @Schema(description = "备份表版本数据版本")
    private Integer backupVersionDataVersion;

    private List<TableDataLoadConfigDTO> tableDataLoadConfigs;

}
