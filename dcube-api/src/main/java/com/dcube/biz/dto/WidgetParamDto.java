package com.dcube.biz.dto;

import com.dcube.biz.base.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @创建人 zhouhx
 * @创建时间 2023/11/15 11:07
 * @描述
 */
@Data
public class WidgetParamDto extends BaseDto{

    @Schema(description = "视图id", example = "视图Id，必传")
    private String viewId;

    @Schema(description = "数据源类型", example = "数据源类型，必传")
    private String sourceType;

    @Schema(description = "SQL脚本", example = "SQL脚本，必传")
    private String viewScript;

    @Schema(description = "横轴纬度列", example = "横轴纬度列，必传")
    private List<ColumnDto> dimColumns;

    @Schema(description = "图例纬度列", example = "图例纬度列")
    private List<ColumnDto> legendColumns;

    @Schema(description = "指标列", example = "指标列，必传")
    private List<ColumnDto> dataColumns;

    @Schema(description = "筛选维")
    private List<ColumnDto> filterColumns;

    @Schema(description = "分页参数")
    private WidgetPageDto page;

    @Schema(description = "当组件中有变量时，必传")
    private Boolean hasVariable;

}
