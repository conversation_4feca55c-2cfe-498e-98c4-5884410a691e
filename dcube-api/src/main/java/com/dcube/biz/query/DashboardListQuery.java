package com.dcube.biz.query;

import com.dcube.biz.base.BaseQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DashboardListQuery extends BaseQuery {

    @Schema(description = "id")
    private String id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "组织")
    private Long deptId;

    @Schema(description = "状态")
    private String status;

}
