package com.dcube.biz.query;

import com.dcube.biz.base.BaseDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 数据底表数据日期版本查询
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Data
public class DataFoundationDataVersionQuery extends BaseDto {

    @Schema(description = "数据底表ID")
    private Integer tableId;

    @Schema(description = "起始数据日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date baseDataDtStart;

    @Schema(description = "截止数据日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date baseDataDtEnd;
}
