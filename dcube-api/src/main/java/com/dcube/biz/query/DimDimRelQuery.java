package com.dcube.biz.query;

import com.dcube.biz.base.BaseQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2027-07-12
 */
@Data
public class DimDimRelQuery extends BaseQuery {

    /**
     * 维度表ID
     */
    @Schema(description = "维度表ID")
    private Integer tableId;

    /**
     * 源维度目录ID
     */
    @Schema(description = "源维度目录ID")
    private Integer resDimDirectoryId;

    /**
     * 源维度实例ID
     */
    @Schema(description = "源维度实例ID")
    private Integer resInstanceId;

    /**
     * 目标维度目录ID
     */
    @Schema(description = "目标维度目录ID")
    private Integer tarDimDirectoryId;

    /**
     * 目标维度实例ID
     */
    @Schema(description = "目标维度实例ID")
    private Integer tarInstanceId;
}
