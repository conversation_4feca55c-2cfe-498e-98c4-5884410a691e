package com.dcube.biz.query;

import com.dcube.biz.base.BaseQuery;
import com.dcube.biz.constant.enums.DimTypeEnum;
import com.dcube.biz.dto.DimTableDataQueryFilterDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2024-02-12
 */
@Data
@Schema(description = "多维表数据查询实体类")
public class DimTableDataQuery extends BaseQuery {

    @Schema(description = "二维表ID")
    private Integer tableId;

    /**
     * 列维度ID
     */
    @Schema(description = "列维度ID")
    private List<Integer> columnDimIds;

    /**
     * 列维度类型
     */
    @Schema(description = "列维度类型")
    private List<DimTypeEnum> columnDimTypes;

    /**
     * 行维度ID
     */
    @Schema(description = "行维度ID")
    private List<Integer> rowDimIds;

    /**
     * 行维度类型
     */
    @Schema(description = "行维度类型")
    private List<DimTypeEnum> rowDimTypes;

    /**
     * 筛选维
     */
    @Schema(description = "筛选维")
    private List<DimTableDataQueryFilterDto> filterList;

    /**
     * 维度显示
     */
    @Schema(description = "维度显示")
    private List<DimTableInstanceQuery> showDimList;
}
