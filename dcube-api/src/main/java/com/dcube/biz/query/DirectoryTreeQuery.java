package com.dcube.biz.query;

import com.dcube.biz.base.BaseQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Data
public class DirectoryTreeQuery extends BaseQuery {

    @Schema(description = "维度目录名称")
    private String dimDirectoryName;

    @Schema(description = "上级目录ID")
    private Integer parentId;
}
