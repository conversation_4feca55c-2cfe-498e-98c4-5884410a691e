package com.dcube.biz.query;

import com.dcube.biz.base.BaseQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Data
public class GroupInstanceListQuery extends BaseQuery {

    /**
     * 分组ID
     */
    @Schema(description = "分组ID")
    private String groupId;

    /**
     * 分组实例名称
     */
    @Schema(description = "分组名称")
    private String groupName;

    /**
     * 分组实例名称
     */
    @Schema(description = "分组实例名称")
    private String groupInstanceName;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;
}
