package com.dcube.biz.query;

import com.dcube.biz.base.BaseQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Data
public class GroupListQuery extends BaseQuery {

    /**
     * 分组代码
     */
    @Schema(description = "分组代码")
    private String groupCode;

    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String groupName;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;
}
