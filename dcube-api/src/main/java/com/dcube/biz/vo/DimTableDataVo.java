package com.dcube.biz.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * class description
 *
 * <AUTHOR>
 * @date 2024-02-12
 */
@Data
public class DimTableDataVo implements Serializable {

    @Schema(description = "列表头数据集合")
    private List<DimTableHeaderVo> columnHeaders;

    @Schema(description = "行表头数据集合")
    private List<DimTableHeaderVo> rowHeaders;

    @Schema(description = "行数据集合")
    private List<DimRowDataVo> rows;
}
