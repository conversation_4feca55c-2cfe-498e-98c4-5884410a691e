package com.dcube.biz.vo;

import com.dcube.biz.dto.WidgetColumnDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @创建人 zhouhx
 * @创建时间 2023/11/16 14:53
 * @描述
 */
@Data
public class TableTreeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "SQL脚本")
    private String viewScript;

    @Schema(description = "父Id")
    private Integer parentId;

    @Schema(description = "数据源类型")
    private String sourceType;

    @Schema(description = "元数据")
    private String tableMeta;

    @Schema(description = "视图名称")
    private String tableName;

    @Schema(description = "内存表名称")
    private String memTableName;

    @Schema(description = "元数据")
    private List<WidgetColumnDto> metadata;

    private List<TableTreeVo> children;
}
