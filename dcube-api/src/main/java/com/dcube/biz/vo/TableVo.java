package com.dcube.biz.vo;

import com.dcube.biz.base.BaseVo;
import com.dcube.biz.json.TableMetaJson;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 二维表
 *
 * <AUTHOR>
 * @date 2023-02-22
 */
@Data
public class TableVo extends BaseVo {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 目录ID
     */
    @Schema(description = "目录ID")
    private String directoryId;

    /**
     * 表名
     */
    @Schema(description = "表名")
    private String tableName;

    /**
     * 建表方式
     */
    @Schema(description = "建表方式")
    private String createMode;

    /**
     * 数据视图ID
     */
    @Schema(description = "数据视图ID")
    private String viewId;

    /**
     * 子表标记
     */
    @Schema(description = "子表标记")
    private String childFlag;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 表格元数据
     */
    @Schema(description = "表格元数据")
    private List<TableMetaJson> tableMetaJson;

    @Schema(description = "父Id")
    private Integer parentId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "祖级列表")
    private String ancestors;

    @Schema(description = "内存数据库表名")
    private String memTableName;

    @Schema(description = "备份版本名称")
    private String version;

    @Schema(description = "备份表名")
    private String backupTableName;

    /**
     * 表层级
     */
    @Schema(description = "表层级")
    private Integer tableLevel;

    /**
     * 加载的数据视图ID
     */
    @Schema(description = "加载的数据视图ID")
    private String loadedViewId;

    private FlowInfo flowInfo;

    public FlowInfo getFlowInfo() {
        if (flowInfo == null) {
            flowInfo = new FlowInfo();
        }
        return flowInfo;
    }

    public void setFlowInfo(FlowInfo flowInfo) {
        this.flowInfo = flowInfo;
    }

    @Data
    public static class FlowInfo {

        @Schema(description = "流程定义id")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long defineId;

        @Schema(description = "启用流程")
        private Boolean showFlowSubmit = Boolean.FALSE;

        @Schema(description = "批量提交")
        private Boolean batchSubmit = Boolean.FALSE;

        @Schema(description = "流程状态列名称")
        private String flowStateColumnName;

        @Schema(description = "流程状态列编码")
        private String flowStateColumnCode;

    }

}
