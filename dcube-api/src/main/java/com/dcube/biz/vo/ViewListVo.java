package com.dcube.biz.vo;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @创建人 zhouhx
 * @创建时间 2023/11/16 16:08
 * @描述
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
public class ViewListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<TableTreeVo> memView;

    private List<WidgetViewVo> dbView;
}
