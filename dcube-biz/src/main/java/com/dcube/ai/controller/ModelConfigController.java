package com.dcube.ai.controller;

import cn.hutool.core.bean.BeanUtil;
import com.dcube.ai.constants.enums.ModelUseSceneEnum;
import com.dcube.ai.domain.ModelConfig;
import com.dcube.ai.dto.ModelConfigDTO;
import com.dcube.ai.dto.ModelUseSceneInputExampleDTO;
import com.dcube.ai.dto.ModelUseSceneSetDTO;
import com.dcube.ai.service.IModelConfigService;
import com.dcube.ai.service.IModelSceneSettingService;
import com.dcube.ai.vo.ModelConfigVO;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/ai/modelConfig")
@Tag(name = "AI模型配置", description = "AI模型配置")
@PreAuthorize("@ss.hasPermi('system:model:list')")
public class ModelConfigController {

    @Autowired
    private IModelConfigService modelConfigService;
    @Autowired
    private IModelSceneSettingService modelSceneSettingService;

    @Operation(summary = "查询模型场景设置")
    @GetMapping("/queryModelUseSceneSetting")
    public AjaxResult queryModelUseSceneSetting(ModelUseSceneEnum modelUseScene) {
        return AjaxResult.success(modelSceneSettingService.queryModelUseSceneSetting(modelUseScene));
    }

    @Operation(summary = "查询模型配置类型")
    @GetMapping("/queryModelType")
    public AjaxResult queryModelType() {
        return AjaxResult.success(modelConfigService.queryModelType());
    }

    @Operation(summary = "查询模型配置列表")
    @GetMapping("/list")
    public AjaxResult list() {
        List<ModelConfig> list = modelConfigService.list();
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success(list);
        }
        List<ModelConfigVO> modelConfigVOS = BeanUtil.copyToList(list, ModelConfigVO.class);
        modelConfigVOS.forEach(modelConfigVO -> {
            if (modelConfigVO.getModelType() != null) {
                modelConfigVO.setModelTypeName(modelConfigVO.getModelType().getDesc());
            }
        });
        return AjaxResult.success(modelConfigVOS);
    }

    @Operation(summary = "查询模型使用场景")
    @GetMapping("/queryUseScene")
    public AjaxResult queryUseScene() {
        return AjaxResult.success(modelConfigService.queryUseScene());
    }

    @Operation(summary = "设置模型使用场景")
    @PostMapping("/setUseScene")
    @Log(title = "设置模型使用场景", businessType = BusinessType.UPDATE)
    public AjaxResult setUseScene(@RequestBody ModelUseSceneSetDTO dto) {
        modelConfigService.setUseScene(dto);
        return AjaxResult.success();
    }

    /**
     * 新增模型配置
     */
    @Log(title = "新增模型配置", businessType = BusinessType.INSERT)
    @Operation(summary = "新增模型配置")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ModelConfigDTO dto) {
        modelConfigService.add(dto);
        return AjaxResult.success();
    }

    /**
     * 修改模型配置
     */
    @Log(title = "修改模型配置", businessType = BusinessType.UPDATE)
    @Operation(summary = "修改模型配置")
    @PostMapping("/put")
    public AjaxResult edit(@RequestBody ModelConfigDTO dto) {
        modelConfigService.update(dto);
        return AjaxResult.success();
    }

    @Log(title = "删除模型配置", businessType = BusinessType.DELETE)
    @Operation(summary = "删除模型配置")
    @PostMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return AjaxResult.success(modelConfigService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 模型测试
     */
    @Log(title = "模型测试", businessType = BusinessType.OTHER)
    @Operation(summary = "模型测试")
    @PostMapping("/test")
    public AjaxResult test(@RequestBody ModelConfigDTO dto) {
        return modelConfigService.test(dto);
    }

    @Operation(summary = "保存使用场景输入示例")
    @PostMapping("/saveUseSceneInputExample")
    @Log(title = "保存模型输入示例", businessType = BusinessType.UPDATE)
    public AjaxResult saveUseSceneInputExample(@RequestBody ModelUseSceneInputExampleDTO dto) {
        modelConfigService.saveUseSceneInputExample(dto);
        return AjaxResult.success();
    }

}
