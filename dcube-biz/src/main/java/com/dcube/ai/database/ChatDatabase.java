package com.dcube.ai.database;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dcube.ai.constants.enums.ModelUseSceneEnum;
import com.dcube.ai.constants.enums.QuestionTypeEnum;
import com.dcube.ai.domain.AIChatLog;
import com.dcube.ai.service.IAIChatLogService;
import com.dcube.common.exception.ai.AIServiceException;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.common.utils.spring.SpringUtils;
import dev.langchain4j.model.Tokenizer;
import org.apache.commons.collections4.MapUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: yang<PERSON>
 * @Date: 2025/3/1 14:37
 * @Description: 对话数据库
 */
public class ChatDatabase {

    private final static IAIChatLogService aiChatLogService = SpringUtils.getBean(IAIChatLogService.class);

    /**
     * 对话问题和结果缓存，使用表格id和用户id做数据隔离
     */
    private static class TableUserHolder {
        private static final Map<Integer, Map<Long, String>> INSTANCE = new ConcurrentHashMap<>();
    }

    /**
     * 对话问题和结果缓存，使用表格id和用户id做数据隔离
     */
    private static class ChatQuestionHolder {
        private static final Map<String, Map<String, Question>> INSTANCE = new ConcurrentHashMap<>();
    }

    public static void createChat(Integer tableId, Long userId, String chatId) {
        Map<Long, String> map1 = TableUserHolder.INSTANCE.computeIfAbsent(tableId, k -> new ConcurrentHashMap<>());
        String chatIdDel = map1.get(userId);
        if (StringUtils.isNotEmpty(chatIdDel)) {
            if (!StringUtils.equals(chatId, chatIdDel)) {
                // 清除历史数据
                ChatQuestionHolder.INSTANCE.remove(chatIdDel);
            }
        }
        map1.put(userId, chatId);
        ChatQuestionHolder.INSTANCE.computeIfAbsent(chatId, k -> new ConcurrentHashMap<>());
    }

    public static void saveChatQuestion(String chatId, String questionId, String jsonData, ModelUseSceneEnum sceneType, String originalQuestionId, String questionStr, String suggestion, String finalSql, String modelConfig, String prompt, QuestionTypeEnum questionType) {
        saveChatQuestion(chatId, questionId, jsonData, sceneType, Collections.emptyList(), originalQuestionId, questionStr, suggestion, finalSql, modelConfig, prompt, questionType);
    }

    public static void saveChatQuestion(String chatId, String questionId, String jsonData, ModelUseSceneEnum sceneType, List<String> dataInsightQuestionIds, String originalQuestionId, String questionStr, String suggestion, String modelConfig, String prompt, QuestionTypeEnum questionType) {
        saveChatQuestion(chatId, questionId, jsonData, sceneType, dataInsightQuestionIds, originalQuestionId, questionStr, suggestion, null, modelConfig, prompt, questionType);
    }

    public static void saveChatQuestion(String chatId, String questionId, String jsonData, ModelUseSceneEnum sceneType, List<String> dataInsightQuestionIds, String originalQuestionId, String questionStr, String suggestion, String finalSql, String modelConfig, String prompt, QuestionTypeEnum questionType) {
        Map<String, Question> map = ChatQuestionHolder.INSTANCE.computeIfAbsent(chatId, k -> new ConcurrentHashMap<>());
        Question question = new Question();
        question.setQuestionId(questionId);
        question.setSceneType(sceneType);
        question.setDataInsightQuestionIds(dataInsightQuestionIds);
        question.setOriginalQuestionId(originalQuestionId);
        question.setQuestion(questionStr);
        question.setSuggestion(suggestion);
        question.setFinalSql(finalSql);
        if (StringUtils.isNotEmpty(suggestion)) {
            Question originalQuestion;
            if (ModelUseSceneEnum.QUERY_CODE == sceneType) {
                // 添加到原始问题的返工
                originalQuestion = map.get(originalQuestionId);
                if (originalQuestion == null) {
                    throw new AIServiceException("未找到原始问题");
                }
            } else if (ModelUseSceneEnum.DATA_INSIGHT == sceneType) {
                originalQuestion = getChatQuestionDataInsight(chatId, originalQuestionId);
                if (originalQuestion == null) {
                    throw new AIServiceException("未找到原始数据洞察问题");
                }
            } else if (ModelUseSceneEnum.MAKE_CHART == sceneType) {
                originalQuestion = getChatQuestionChart(chatId, originalQuestionId);
                if (originalQuestion == null) {
                    throw new AIServiceException("未找到原始数据洞察问题");
                }
            } else {
                originalQuestion = null;
            }
            if (originalQuestion != null) {
                List<String> reworkQuestionIds = originalQuestion.getReworkQuestionIds();
                if (reworkQuestionIds == null) {
                    reworkQuestionIds = new ArrayList<>();
                    originalQuestion.setReworkQuestionIds(reworkQuestionIds);
                }
                reworkQuestionIds.add(questionId);
            }
        }
        question.setJsonData("");
        int size = map.size();
        map.put(questionId, question);
        AIChatLog aiChatLog = ThreadLocalUtils.get(ChatDatabase.class, "aiChatLog");
        if (aiChatLog == null) {
            aiChatLog = new AIChatLog();
        }
        aiChatLog.setChatId(Long.valueOf(chatId));
        aiChatLog.setQuestionId(Long.valueOf(questionId));
        aiChatLog.setAiResult(jsonData);
        aiChatLog.setPrompt(prompt);
        aiChatLog.setSceneType(sceneType);
        aiChatLog.setQuestionType(questionType);
        aiChatLog.setModelConfig(modelConfig);
        aiChatLog.setIsResult(1);
        aiChatLog.setQuestionOrder(size);
        aiChatLogService.save(aiChatLog);
    }


    public static Question getChatQuestion(String chatId, String questionId) {
        Map<String, Question> map = ChatQuestionHolder.INSTANCE.get(chatId);
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        Question question = map.get(questionId);
        if (question != null) {
            if (StringUtils.isNotEmpty(question.getOriginalQuestionId())) {
                return getChatQuestion(chatId, question.getOriginalQuestionId());
            }
            return question;
        }
        return null;
    }

    public static Question getChatQuestionByQuestionId(String chatId, String questionId) {
        Map<String, Question> map = ChatQuestionHolder.INSTANCE.get(chatId);
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        return map.get(questionId);
    }

    public static int checkChatQuestionAnswer(String chatId, List<String> questionIds, Integer maxSize) {
        Map<String, Question> map = ChatQuestionHolder.INSTANCE.get(chatId);
        if (MapUtils.isEmpty(map)) {
            throw new AIServiceException("未找到会话数据");
        }
        int i = 0, sum = 0;
        Map<String, String> jsonDataMap = getJsonData(chatId, questionIds);
        for (int size = questionIds.size(); i < size; i++) {
            String questionId = questionIds.get(i);
            Question question = map.get(questionId);
            if (question == null) {
                throw new AIServiceException("未找到原问题");
            }
            if (maxSize != null) {
                sum += getTokenLength(MapUtils.getString(jsonDataMap, question.getQuestionId(), ""), null);
                if (sum > maxSize) {
                    return (i - 1);
                }
            }
        }
        return i;
    }

    public static String getChatQuestionAnswer(String chatId, List<String> questionIds, Integer maxSize) {
        int i = checkChatQuestionAnswer(chatId, questionIds, maxSize);
        if (i <= 0) {
            throw new AIServiceException("洞察数据样本超出大模型限制");
        }
        Map<String, Question> map = ChatQuestionHolder.INSTANCE.get(chatId);
        if (MapUtils.isEmpty(map)) {
            throw new AIServiceException("未找到会话数据");
        }
        questionIds = questionIds.subList(0, i);
        Set<String> set = new HashSet<>(questionIds.size());
        Map<String, String> jsonDataMap = getJsonData(chatId, questionIds);
        for (String questionId : questionIds) {
            Question question = map.get(questionId);
            if (question == null) {
                throw new AIServiceException("未找到原问题");
            }
            String string = MapUtils.getString(jsonDataMap, question.getQuestionId());
            if (StringUtils.isNotEmpty(string)) {
                set.add(string);
            }
        }
        return String.join(",", set);
    }

    private static int getTokenLength(String content, Tokenizer tokenizer) {
        if (StringUtils.isEmpty(content)) {
            return 0;
        }
        if (tokenizer == null) {
            return content.length();
        }
        return tokenizer.estimateTokenCountInText(content);
    }

    public static List<String> getChatOriginalQuestionIds(String chatId) {
        Map<String, Question> map = ChatQuestionHolder.INSTANCE.get(chatId);
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyList();
        }
        List<String> questionIds = new ArrayList<>();
        List<Question> questions = map.values().stream().sorted(Comparator.comparingInt(Question::getQuestionOrder).reversed()).collect(Collectors.toList());
        for (Question question : questions) {
            if (StringUtils.isNotEmpty(question.getOriginalQuestionId()) || question.getSceneType() != ModelUseSceneEnum.QUERY_CODE) {
                continue;
            }
            questionIds.add(question.getQuestionId());
        }
        return questionIds;
    }

    public static Question getChatQuestionDataInsight(String chatId, String questionId) {
        Map<String, Question> map = ChatQuestionHolder.INSTANCE.get(chatId);
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        Question question = map.get(questionId);
        while (question != null && StringUtils.isNotEmpty(question.getOriginalQuestionId())) {
            Question question1 = map.get(question.getOriginalQuestionId());
            if (question1 != null && question1.getSceneType() == ModelUseSceneEnum.DATA_INSIGHT) {
                question = question1;
            } else {
                break;
            }
        }
        return question;
    }

    public static Question getChatQuestionChart(String chatId, String questionId) {
        Map<String, Question> map = ChatQuestionHolder.INSTANCE.get(chatId);
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        Question question = map.get(questionId);
        while (question != null && StringUtils.isNotEmpty(question.getOriginalQuestionId())) {
            Question question1 = map.get(question.getOriginalQuestionId());
            if (question1 != null && question1.getSceneType() == ModelUseSceneEnum.MAKE_CHART) {
                question = question1;
            } else {
                break;
            }
        }
        return question;
    }

    public static String getJsonData(String chatId, String questionId) {
        String res = ThreadLocalUtils.get(ChatDatabase.class, "getJsonData_" + chatId + "_" + questionId);
        if (res == null) {
            AIChatLog aiChatLog = aiChatLogService.getOne(Wrappers.<AIChatLog>lambdaQuery()
                    .eq(AIChatLog::getChatId, chatId)
                    .eq(AIChatLog::getQuestionId, questionId)
                    .eq(AIChatLog::getIsResult, "1")
            );
            res = aiChatLog == null || aiChatLog.getAiResult() == null ? "" : aiChatLog.getAiResult();
            ThreadLocalUtils.set(ChatDatabase.class, "getJsonData_" + chatId + "_" + questionId, res);
        }
        return res;
    }

    public static Map<String, String> getJsonData(String chatId, List<String> questionIds) {
        String questionId = questionIds.stream().map(Long::parseLong).sorted(Comparator.comparingLong(o -> o)).map(String::valueOf).collect(Collectors.joining(","));
        Map<String, String> res = ThreadLocalUtils.get(ChatDatabase.class, "getJsonData_" + chatId + "_" + questionId);
        if (res == null) {
            List<AIChatLog> list = aiChatLogService.list(Wrappers.<AIChatLog>lambdaQuery()
                    .eq(AIChatLog::getChatId, chatId)
                    .in(AIChatLog::getQuestionId, questionIds)
                    .eq(AIChatLog::getIsResult, "1")
            );
            res = StreamUtils.toMap(list, aiChatLog -> String.valueOf(aiChatLog.getQuestionId()), AIChatLog::getAiResult);
            ThreadLocalUtils.set(ChatDatabase.class, "getJsonData_" + chatId + "_" + questionId, res);
        }
        return res;

    }


}
