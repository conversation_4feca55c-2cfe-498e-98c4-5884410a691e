package com.dcube.ai.deepseek.client;

import com.dcube.ai.qwen.vo.RequestBodyVo;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * @创建人 zhouhx
 * @创建时间 2025/2/20 13:52
 * @描述
 */
@Slf4j
@Configuration
public class DeepseekClient {


    @Value("${ai.deepseek.baseUrl:}")
    private String baseUrl;

    @Value("${ai.deepseek.apiKey:}")
    private String apiKey;

    public String askQuestion(RequestBodyVo requestBodyVo) {
        StringBuilder response = new StringBuilder();
        try {
            // 将请求体转换为 JSON
            Gson gson = new Gson();
            String jsonInputString = gson.toJson(requestBodyVo);

            // 创建 URL 对象
            URL url = new URL(baseUrl);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();

            // 设置请求方法为 POST
            httpURLConnection.setRequestMethod("POST");
            httpURLConnection.setRequestProperty("Content-Type", "application/json");
            httpURLConnection.setRequestProperty("Accept", "application/json");

            // 若没有配置环境变量，请用百炼API Key将下行替换为：String apiKey = "sk-xxx";
            String auth = "Bearer " + apiKey;
            httpURLConnection.setRequestProperty("Authorization", auth);

            // 启用输入输出流
            httpURLConnection.setDoOutput(true);

            // 写入请求体
            try (OutputStream os = httpURLConnection.getOutputStream()) {
                byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 获取响应码
            int responseCode = httpURLConnection.getResponseCode();
            log.info("正在向DeepSeek提问: " + requestBodyVo.getMessages()[0].getContent());

            // 读取响应体
            try (BufferedReader br = new BufferedReader(new InputStreamReader(httpURLConnection.getInputStream(), StandardCharsets.UTF_8))) {

                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                log.info("回答: " + response);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return response.toString();
    }
}
