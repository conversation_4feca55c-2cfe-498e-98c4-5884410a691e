package com.dcube.ai.dto;

import com.dcube.ai.constants.enums.ModelTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class ModelConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String modelName;
    /**
     * 模型类型
     */
    @Schema(description = "模型类型")
    private ModelTypeEnum modelType;

    /**
     * 模型url地址
     */
    @Schema(description = "模型url地址")
    private String modelUrl;
    /**
     * 模型key
     */
    @Schema(description = "模型key")
    private String modelKey;

    /**
     * 测试问题
     */
    @Schema(description = "测试问题")
    private String testQuestion;

    /**
     * 上下文长度
     */
    @Schema(description = "上下文长度")
    private Integer contextLength;

    /**
     * 最大输入长度
     */
    @Schema(description = "最大输入长度")
    private Integer maxInputLength;

}
