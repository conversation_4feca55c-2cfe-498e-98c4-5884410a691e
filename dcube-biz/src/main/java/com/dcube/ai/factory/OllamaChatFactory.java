package com.dcube.ai.factory;

import dev.langchain4j.model.Tokenizer;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.ollama.OllamaChatModel;
import dev.langchain4j.model.ollama.OllamaStreamingChatModel;
import dev.langchain4j.model.openai.OpenAiTokenizer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OllamaChatFactory extends AIChatFactory {

    @Override
    protected ChatLanguageModel createChatLanguageModel(Double temperature) {
        return OllamaChatModel.builder()
                .baseUrl(baseUrl)
//                .apiKey(apiKey)
                .modelName(modelName)
//                .maxTokens(maxTokens)
                .temperature(temperature)
                .logRequests(true)
                .logResponses(true)
                .build();
    }

    @Override
    protected StreamingChatLanguageModel createStreamingChatLanguageModel(Double temperature) {
        return OllamaStreamingChatModel.builder()
                .baseUrl(baseUrl)
//                .apiKey(apiKey)
                .modelName(modelName)
//                .maxTokens(maxTokens)
                .temperature(temperature)
                .think(false)
                .logRequests(true)
                .logResponses(true)
                .build();
    }

    @Override
    public Tokenizer getTokenizer(String modelName) {
        return new OpenAiTokenizer();
    }

}
