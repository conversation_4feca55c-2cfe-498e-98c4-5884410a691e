package com.dcube.ai.qwen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @创建人 zhouhx
 * @创建时间 2025/2/21 17:22
 * @描述
 */
@Data
public class AIQueryResultVo {

    @Schema(description = "提问")
    private String question;

    @Schema(description = "sql语句")
    private String sql;

    @Schema(description = "数据")
    private List<Map<String,Object>> data;

    @Schema(description = "AI返回内容")
    private String content;

    private String html;
}
