package com.dcube.ai.qwen.vo;

import java.util.HashMap;
import java.util.Map;

/**
 * @创建人 zhouhx
 * @创建时间 2025/2/22 11:56
 * @描述
 */
public class CronExpressionParser {

    private static final Map<Integer, String> MONTHS = new HashMap<>();
    private static final Map<Integer, String> WEEKDAYS = new HashMap<>();

    static {
        MONTHS.put(1, "一月");
        MONTHS.put(2, "二月");
        MONTHS.put(3, "三月");
        MONTHS.put(4, "四月");
        MONTHS.put(5, "五月");
        MONTHS.put(6, "六月");
        MONTHS.put(7, "七月");
        MONTHS.put(8, "八月");
        MONTHS.put(9, "九月");
        MONTHS.put(10, "十月");
        MONTHS.put(11, "十一月");
        MONTHS.put(12, "十二月");

        WEEKDAYS.put(0, "星期日");
        WEEKDAYS.put(1, "星期一");
        WEEKDAYS.put(2, "星期二");
        WEEKDAYS.put(3, "星期三");
        WEEKDAYS.put(4, "星期四");
        WEEKDAYS.put(5, "星期五");
        WEEKDAYS.put(6, "星期六");
    }

    public static String parseCronExpression(String cronExpression) {
        String[] parts = cronExpression.split(" ");
        if (parts.length < 5 || parts.length > 7) {
            throw new IllegalArgumentException("无效的cron表达式");
        }

        String second = parts.length == 7 ? parts[0] : null;
        String minute = parts[parts.length - 5];
        String hour = parts[parts.length - 4];
        String dayOfMonth = parts[parts.length - 3];
        String month = parts[parts.length - 2];
        String dayOfWeek = parts[parts.length - 1];
        String year = parts.length == 7 ? parts[6] : null;

        StringBuilder result = new StringBuilder();

        if (second != null) {
            result.append("每秒 ").append(second).append(" 秒, ");
        }

        result.append("每分钟 ").append(minute).append(" 分, ");
        result.append("每小时 ").append(hour).append(" 时, ");

        if (!dayOfMonth.equals("*") && !dayOfWeek.equals("*")) {
            result.append("每月 ").append(dayOfMonth).append(" 日, 每周 ").append(dayOfWeek).append(" ");
        } else if (!dayOfMonth.equals("*")) {
            result.append("每月 ").append(dayOfMonth).append(" 日 ");
        } else if (!dayOfWeek.equals("*")) {
            result.append("每周 ").append(dayOfWeek).append(" ");
        }

        result.append("每月 ").append(month).append(" 月");

        if (year != null) {
            result.append(", 每年 ").append(year).append(" 年");
        }

        return result.toString();
    }

    private static String parseField(String field, Map<Integer, String> map) {
        if (field.equals("*")) {
            return "每";
        } else if (field.matches("\\d+")) {
            return map.getOrDefault(Integer.parseInt(field), field);
        } else if (field.contains(",")) {
            String[] values = field.split(",");
            StringBuilder sb = new StringBuilder();
            for (String value : values) {
                sb.append(map.getOrDefault(Integer.parseInt(value), value)).append(", ");
            }
            return sb.substring(0, sb.length() - 2);
        } else if (field.contains("-")) {
            String[] range = field.split("-");
            int start = Integer.parseInt(range[0]);
            int end = Integer.parseInt(range[1]);
            StringBuilder sb = new StringBuilder();
            for (int i = start; i <= end; i++) {
                sb.append(map.getOrDefault(i, String.valueOf(i))).append(", ");
            }
            return sb.substring(0, sb.length() - 2);
        } else if (field.contains("/")) {
            String[] parts = field.split("/");
            int start = parts[0].equals("*") ? 0 : Integer.parseInt(parts[0]);
            int interval = Integer.parseInt(parts[1]);
            StringBuilder sb = new StringBuilder();
            for (int i = start; i < 60; i += interval) {
                sb.append(i).append(", ");
            }
            return sb.substring(0, sb.length() - 2);
        } else {
            return field;
        }
    }

    public static void main(String[] args) {
        String cronExpression = "10 10 10 ? 2,3 5,6";
        System.out.println(parseCronExpression(cronExpression));

        cronExpression = "0 0/5 14 * * ?";
        System.out.println(parseCronExpression(cronExpression));

        cronExpression = "0 15 10 ? * 1-5";
        System.out.println(parseCronExpression(cronExpression));

        cronExpression = "0 15 10 ? * 6L";
        System.out.println(parseCronExpression(cronExpression));

        cronExpression = "0 15 10 ? * 2#1";
        System.out.println(parseCronExpression(cronExpression));

        cronExpression = "0 15 10 ? * * 2023";
        System.out.println(parseCronExpression(cronExpression));
    }
}
