package com.dcube.ai.qwen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @创建人 zhouhx
 * @创建时间 2025/2/20 17:13
 * @描述
 */
@Data
public class RequestBodyVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "模式")
    private String model;

    @Schema(description = "提问集合")
    private RequestMsg[] messages;

    @Schema(description = "开启流式输出")
    private Boolean stream;

    private String schema;
    private String requirement;
    private String dialect;
    private Settings settings;

    private Long userId;

    public RequestBodyVo() {
    }

    public RequestBodyVo(String model, RequestMsg[] messages) {
        this.model = model;
        this.messages = messages;
    }

    public static class Settings {
        private boolean strictMode;
        private boolean enableJoinDetection;

        public Settings() {
        }

        // Getter 和 Setter 方法
        public boolean isStrictMode() {
            return strictMode;
        }

        public void setStrictMode(boolean strictMode) {
            this.strictMode = strictMode;
        }

        public boolean isEnableJoinDetection() {
            return enableJoinDetection;
        }

        public void setEnableJoinDetection(boolean enableJoinDetection) {
            this.enableJoinDetection = enableJoinDetection;
        }
    }
}
