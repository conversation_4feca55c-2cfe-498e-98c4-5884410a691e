package com.dcube.ai.qwen.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @创建人 zhouhx
 * @创建时间 2025/2/20 17:11
 * @描述
 */
@Data
public class RequestMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "角色")
    private String role;

    @Schema(description = "问题内容")
    private String content;

    public RequestMsg() {
    }

    public RequestMsg(String role, String content) {
        this.role = role;
        this.content = content;
    }
}
