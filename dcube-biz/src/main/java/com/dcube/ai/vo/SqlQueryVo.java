package com.dcube.ai.vo;

import cn.hutool.setting.Setting;
import com.google.common.collect.Tables;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @创建人 zhouhx
 * @创建时间 2025/2/21 11:09
 * @描述
 */
@Data
public class SqlQueryVo {

    private String dialect;

    private Schema schema;

    private Settings settings;

    private String requirement;

    @Data
    public static class Schema {

        private List<Table> tables;

        public Schema() {}

        @Data
        public static class Table {

            private String tableName;

            private List<Column> columns;

            public Table() {}

            @Data
            public static class Column {

                private String name;

                private String type;

                private String comment;

                public Column() {}
            }
        }
    }

    @Builder
    @Data
    public static class Settings {
        private boolean strictMode;
        private boolean enableJoinDetection;

        public Settings() {
        }

        public Settings(boolean strictMode, boolean enableJoinDetection) {
            this.strictMode = strictMode;
            this.enableJoinDetection = enableJoinDetection;
        }
    }
}
