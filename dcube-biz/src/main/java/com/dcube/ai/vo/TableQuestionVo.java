package com.dcube.ai.vo;

import com.dcube.ai.constants.enums.DataInsightTypeEnum;
import com.dcube.biz.dto.FilterDto;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.List;

/**
 * @创建人 zhouhx
 * @创建时间 2025/6/14 16:07
 * @描述
 */
@Data
public class TableQuestionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer tableId;

    private String chatId;

    private String originalQuestionId;

    private String questionId;

    private String question;

    private String suggestion;

    private DataInsightTypeEnum dataInsightType;

    private Boolean fromDataFoundation;

    private List<FilterDto> filterList;
}
