package com.dcube.biz.cache;

import com.dcube.rule.grid.RuleGraph;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class CubeCache {
    @Value("${cache.cube.max-size:10000000}")
    private Long cacheCubeMaxCacheSize;

    @Bean("cubeRuleCache")
    public Cache<String, Object> cubeRuleCache() {
        Cache<String, Object> cubeRuleCache =
                Caffeine.newBuilder()
                        .maximumSize(cacheCubeMaxCacheSize) // 限制最大缓存条数
                        .expireAfterAccess(1, TimeUnit.DAYS)
                        .build();
        return cubeRuleCache;
    }

    @Bean("ruleGraphCache")
    public Cache<String, RuleGraph> ruleGraphCache() {
        Cache<String, RuleGraph> ruleGraphCache =
                Caffeine.newBuilder()
                        .build();
        return ruleGraphCache;
    }

}
