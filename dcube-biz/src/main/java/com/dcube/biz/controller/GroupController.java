package com.dcube.biz.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.Group;
import com.dcube.biz.dto.GroupDto;
import com.dcube.biz.query.GroupListQuery;
import com.dcube.biz.service.IGroupService;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.enums.BusinessType;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.poi.ExcelUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("group")
@Tag(name = "DCUBE-分组", description = "DCUBE-分组")
public class GroupController extends BaseController {

    @Autowired
    private IGroupService groupService;

    /**
     * 查询分组列表
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group:list')")
    @GetMapping("list")
    @Operation(summary = "查询分组列表", description = "分页参数pageNum和pageSize直接拼接到url上")
    public TableDataInfo list(GroupListQuery query) {
        startPage();
        QueryWrapper<Group> qw = new QueryWrapper<>();
        qw.lambda().eq(StringUtils.isNotEmpty(query.getGroupCode()), Group::getGroupCode, query.getGroupCode());
        qw.lambda().like(StringUtils.isNotEmpty(query.getGroupName()), Group::getGroupName, query.getGroupName());
        qw.lambda().eq(StringUtils.isNotEmpty(query.getStatus()), Group::getStatus, query.getStatus());
        qw.lambda().orderByDesc(Group::getCreateTime);
        List<Group> list = groupService.list(qw);
        return getDataTable(list);
    }

    /**
     * 导出分组列表
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group:export')")
    @Log(title = "分组", businessType = BusinessType.EXPORT)
    @Operation(summary = "导出分组列表")
    @PostMapping("export")
    public void export(HttpServletResponse response, GroupListQuery query) {
        QueryWrapper<Group> qw = new QueryWrapper<>();
        qw.lambda().eq(StringUtils.isNotEmpty(query.getGroupCode()), Group::getGroupCode, query.getGroupCode());
        qw.lambda().like(StringUtils.isNotEmpty(query.getGroupName()), Group::getGroupName, query.getGroupName());
        qw.lambda().eq(StringUtils.isNotEmpty(query.getStatus()), Group::getStatus, query.getStatus());
        qw.lambda().orderByDesc(Group::getCreateTime);
        List<Group> list = groupService.list(qw);
        ExcelUtil<Group> util = new ExcelUtil<>(Group.class);
        util.exportExcel(response, list, "分组");
    }

    /**
     * 获取分组详细信息
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group:query')")
    @Operation(summary = "主键查询")
    @GetMapping("{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(groupService.getById(id));
    }

    /**
     * 新增分组
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group:add')")
    @Log(title = "新增分组", businessType = BusinessType.INSERT)
    @Operation(summary = "新增分组")
    @PostMapping("add")
    public AjaxResult add(@RequestBody GroupDto dto) {
        dto.setId(CharSequenceUtil.EMPTY);
        dto.setStatus(BizConstants.STATUS_ENABLE);
        return toAjax(groupService.saveOrUpdate(dto));
    }

    /**
     * 修改分组
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group:edit')")
    @Log(title = "修改分组", businessType = BusinessType.UPDATE)
    @Operation(summary = "主键修改")
    @PostMapping("put")
    public AjaxResult edit(@RequestBody GroupDto dto) {
        return toAjax(groupService.saveOrUpdate(dto));
    }

    /**
     * 删除分组
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group:remove')")
    @Log(title = "删除分组", businessType = BusinessType.DELETE)
    @Operation(summary = "主键数组删除")
    @PostMapping("remove/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(groupService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 启用
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group:enable')")
    @Log(title = "启用分组", businessType = BusinessType.UPDATE)
    @Operation(summary = "启用")
    @PostMapping("enable")
    public AjaxResult enable(@RequestParam("id") String id) {
        Group domain = groupService.getById(id);
        domain.setStatus(BizConstants.STATUS_ENABLE);
        return toAjax(groupService.updateById(domain));
    }

    /**
     * 禁用
     */
    //@PreAuthorize("@ss.hasPermi('dcube:group:disable')")
    @Log(title = "禁用分组", businessType = BusinessType.UPDATE)
    @Operation(summary = "禁用")
    @PostMapping("disable")
    public AjaxResult disable(@RequestParam("id") String id) {
        Group domain = groupService.getById(id);
        domain.setStatus(BizConstants.STATUS_DISABLED);
        return toAjax(groupService.updateById(domain));
    }

    @GetMapping("option")
    @Operation(summary = "查询分组列表(启用状态，用于页面下拉列表)", description = "")
    public AjaxResult option(GroupListQuery query) {
        QueryWrapper<Group> qw = new QueryWrapper<>();
        qw.lambda().eq(StringUtils.isNotEmpty(query.getGroupCode()), Group::getGroupCode, query.getGroupCode());
        qw.lambda().like(StringUtils.isNotEmpty(query.getGroupName()), Group::getGroupName, query.getGroupName());
        qw.lambda().eq(Group::getStatus, BizConstants.STATUS_ENABLE);
        qw.lambda().orderByDesc(Group::getCreateTime);
        List<Group> list = groupService.list(qw);
        return AjaxResult.success(list);
    }
}
