package com.dcube.biz.controller;

import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.Table;
import com.dcube.biz.dto.SubTableDto;
import com.dcube.biz.dto.UpdateParentTableMetaDto;
import com.dcube.biz.query.SubTableListQuery;
import com.dcube.biz.service.ITableService;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("2dTable/sub")
@Tag(name = "DCUBE-二维表子表", description = "DCUBE-二维表子表")
public class SubTableController extends BaseController {

    @Autowired
    private ITableService tableService;

    @GetMapping("pk_list")
    @Operation(summary = "父表关联列列表", description = "")
    public AjaxResult pkList(@RequestParam("id") Integer id) {
        return AjaxResult.success(tableService.pkList(id, null));
    }

    @PostMapping("add")
    @Operation(summary = "新增二维子表")
    public AjaxResult add(@RequestBody SubTableDto dto) {
        dto.setStatus(BizConstants.STATUS_ENABLE);
        dto.setChildFlag(BizConstants.FLAG_TRUE);
        Table table = tableService.saveOrUpdateWithSub(dto);
        return AjaxResult.success(table.getId());
    }

    @PostMapping("update")
    @Operation(summary = "修改二维子表")
    public AjaxResult update(@RequestBody SubTableDto dto) {
        dto.setChildFlag(BizConstants.FLAG_TRUE);
        tableService.saveOrUpdateWithSub(dto);
        return AjaxResult.success();
    }

    @PostMapping("/listMemData")
    @Operation(summary = "查询二维表子表数据")
    public TableDataInfo listMemData(@RequestBody SubTableListQuery query) {
        return getDataTable(tableService.queryTableData(query, getLoginUser()));
    }

    /**
     * 修改父表入口列
     */
    @Operation(summary = "查询父表入口列列表")
    @GetMapping("/parentPkList")
    public AjaxResult parentPkList(@RequestParam("id") Integer id) {
        return AjaxResult.success(tableService.parentPkList(id));
    }

    /**
     * 修改父表入口列
     */
    @Log(title = "二维表修改父表入口列", businessType = BusinessType.UPDATE)
    @Operation(summary = "修改父表入口列")
    @PostMapping("/updateParentTableMeta")
    public AjaxResult updateParentTableMeta(@RequestBody UpdateParentTableMetaDto dto) {
        Assert.notNull(dto.getId(), "二维表主键不能为空");
        tableService.updateParentTableMeta(dto);
        return AjaxResult.success();
    }

}
