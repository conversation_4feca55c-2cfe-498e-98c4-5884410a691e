package com.dcube.biz.controller;

import com.dcube.biz.dto.TableBackupConfigSaveDTO;
import com.dcube.biz.service.ITableBackupConfigService;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/2dTableBackupConfig")
@Tag(name = "DCUBE-二维表备份配置信息", description = "DCUBE-二维表备份配置信息")
public class TableBackupConfigController extends BaseController {

    @Autowired
    private ITableBackupConfigService tableBackupConfigService;

    @Operation(summary = "根据二维表id查询二维表备份配置信息")
    @GetMapping("/queryByTableId")
    public AjaxResult queryByTableId(@RequestParam("tableId") Long tableId) {
        return AjaxResult.success(tableBackupConfigService.queryByTableId(tableId));
    }

    @Operation(summary = "获取字段类型")
    @GetMapping("/getColumnType")
    public AjaxResult getColumnType() {
        return AjaxResult.success(tableBackupConfigService.getColumnType());
    }

    @Operation(summary = "保存二维表备份配置信息")
    @Log(title = "保存二维表备份配置信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult saveTableBackupConfig(@RequestBody TableBackupConfigSaveDTO tableBackupConfigSaveDto) {
        return AjaxResult.success(tableBackupConfigService.saveTableBackupConfig(tableBackupConfigSaveDto));
    }

}
