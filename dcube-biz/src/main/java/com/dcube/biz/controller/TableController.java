package com.dcube.biz.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.cache.selector.SimpleReadCacheSelector;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.Table;
import com.dcube.biz.domain.TableVersion;
import com.dcube.biz.dto.LoadTableDTO;
import com.dcube.biz.dto.RowDataDto;
import com.dcube.biz.dto.TableDto;
import com.dcube.biz.dto.TableGenDto;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.listener.ExcelTableImpListener;
import com.dcube.biz.query.SubTableListQuery;
import com.dcube.biz.query.TableDataExportQuery;
import com.dcube.biz.query.TableGenQuery;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.service.IExcelHandlerService;
import com.dcube.biz.service.ITableDataLoadConfigService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.service.ITableVersionService;
import com.dcube.biz.util.ExcelUtils;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.util.TableUtils;
import com.dcube.biz.vo.AggregationTableVo;
import com.dcube.biz.vo.ExcelImpVo;
import com.dcube.biz.vo.TableBackupVo;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.annotation.Log;
import com.dcube.common.annotation.RepeatSubmit;
import com.dcube.common.config.properties.GlobalExceptionProperties;
import com.dcube.common.constant.enums.JobOperateTypeEnums;
import com.dcube.common.constant.enums.TaskStatusEnums;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.enums.BusinessType;
import com.dcube.common.enums.TableType;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.report.TaskReport;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.file.FileUploadUtils;
import com.dcube.quartz.util.TaskUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.rmi.ServerException;
import java.util.*;

import static com.dcube.common.utils.EasyExcelUtil.enableFastRead;

@RestController
@RequestMapping("/2dTable")
@Tag(name = "DCUBE-二维表", description = "DCUBE-二维表")
@Slf4j
public class TableController extends BaseController {

    @Autowired
    private ITableService tableService;
    @Autowired
    private ITableVersionService tableVersionService;
    @Autowired
    private IExcelHandlerService excelHandlerService;
    @Autowired
    private ITableDataLoadConfigService tableDataLoadConfigService;
    @Autowired
    private GlobalExceptionProperties globalExceptionProperties;

    /**
     * 查询二维表列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询二维表列表")
    public AjaxResult list(TableListQuery query) {
        return AjaxResult.success(tableService.list(query));
    }

    /**
     * 导出二维表列表
     */
    @Log(title = "导出二维表", businessType = BusinessType.EXPORT)
    @Operation(summary = "导出二维表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody TableListQuery query) {
        query.setCurrentPage(1);
        query.setPageSize(10000);
        List<Map<String, Object>> dataList = tableService.exportTableData(query, null);
        TableVo vo = tableService.get(query.getTableId());
        if (CollectionUtil.isNotEmpty(dataList)) {
            ExcelUtils.createExcel(vo.getTableMetaJson(), dataList, vo.getTableName() + ".xls", "数据记录", response);
        } else {
            throw new ServiceException("导出无记录！");
        }
    }

    /**
     * 获取二维表详细信息
     */
    @Operation(summary = "主键查询")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(tableService.get(id));
    }

    /**
     * 新增二维表
     */
    @Log(title = "二维表", businessType = BusinessType.INSERT)
    @Operation(summary = "新增二维表")
    @RepeatSubmit
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TableDto dto) {
        dto.setStatus(BizConstants.STATUS_ENABLE);
        Table table = tableService.saveOrUpdate(dto);
        return AjaxResult.success(table.getId());
    }

    /**
     * 新增聚合二维表
     */
    @Log(title = "二维表", businessType = BusinessType.INSERT)
    @Operation(summary = "新增聚合二维表")
    @RepeatSubmit
    @PostMapping("/add/aggregation")
    public AjaxResult add(@RequestBody AggregationTableVo aggregationTableVo) {
        Table table = tableService.saveWithAggregation(aggregationTableVo);
        return AjaxResult.success(table.getId());
    }

    /**
     * 修改二维表
     */
    @Log(title = "二维表", businessType = BusinessType.UPDATE)
    @Operation(summary = "主键修改")
    @PostMapping("/put")
    public AjaxResult edit(@RequestBody TableDto dto) {
        tableService.saveOrUpdate(dto);
        return AjaxResult.success();
    }

    @Log(title = "新增数据", businessType = BusinessType.INSERT)
    @Operation(summary = "新增数据")
    @PostMapping("/addData")
    @RepeatSubmit
    public AjaxResult addData(@RequestBody RowDataDto rowData) {
        tableService.addData(rowData);
        return TaskUtil.getTaskIdAjaxResult();
    }

    @Log(title = "删除数据", businessType = BusinessType.DELETE)
    @Operation(summary = "删除数据")
    @PostMapping("/deleteData")
    public AjaxResult deleteData(@RequestBody RowDataDto rowData) {
        tableService.deleteData(rowData);
        return TaskUtil.getTaskIdAjaxResult();
    }

    @Log(title = "修改数据", businessType = BusinessType.UPDATE)
    @Operation(summary = "修改数据")
    @PostMapping("/updateData")
    public AjaxResult updateData(@RequestBody RowDataDto rowData) {
        tableService.updateData(rowData);
        return TaskUtil.getTaskIdAjaxResult();
    }

    /**
     * 删除二维表
     */
    @Log(title = "二维表", businessType = BusinessType.DELETE)
    @Operation(summary = "主键数组删除")
    @PostMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tableService.removeWithExt(ids[0]));
    }

    /**
     * 启用
     */
    @Log(title = "二维表", businessType = BusinessType.UPDATE)
    @Operation(summary = "启用")
    @PostMapping("/enable")
    public AjaxResult enable(@RequestParam("id") String id) {
        Table domain = tableService.getById(id);
        domain.setStatus(BizConstants.STATUS_ENABLE);
        return toAjax(tableService.updateById(domain));
    }

    /**
     * 禁用
     */
    @Log(title = "二维表", businessType = BusinessType.UPDATE)
    @Operation(summary = "禁用")
    @PostMapping("/disable")
    public AjaxResult disable(@RequestParam("id") String id) {
        Table domain = tableService.getById(id);
        domain.setStatus(BizConstants.STATUS_DISABLED);
        return toAjax(tableService.updateById(domain));
    }

    @PostMapping("/listMemData")
    @Operation(summary = "查询二维表数据")
    public TableDataInfo listMemData(@RequestBody TableListQuery query) {
        return getDataTable(tableService.queryTableData(query, getLoginUser()));
    }

    @PostMapping("/avgAndSum")
    @Operation(summary = "平均值与求和")
    public AjaxResult avgAndSum(@RequestBody SubTableListQuery query) {
        return AjaxResult.success(tableService.avgAndSum(query, getLoginUser()));
    }

    @Log(title = "二维表-新增列", businessType = BusinessType.INSERT)
    @Operation(summary = "二维表-新增列")
    @PostMapping("/addColumn")
    public AjaxResult addColumn(@RequestBody TableDto dto) {
        tableService.addColumn(dto);
        return AjaxResult.success();
    }

    @Operation(summary = "二维表-加载数据")
    @PostMapping("loading")
    public AjaxResult loading(@Validated @RequestBody LoadTableDTO loadTableDTO) {
        TableVo vo = tableService.get(loadTableDTO.getId());
        if (!TableType.TABLE.getCode().equals(vo.getType())) {
            return AjaxResult.success(String.format("二维表[%s]不存在。", vo.getTableName()));
        }
        // 保存数据加载配置的映射关系
        tableDataLoadConfigService.save(loadTableDTO.getTableDataLoadConfigs());
        TableDto dto = new TableDto();
        BeanUtils.copyProperties(vo, dto);
        // 保存加载的数据视图id
        if (StringUtils.isNotEmpty(loadTableDTO.getViewId())) {
            tableService.update(Wrappers.<Table>lambdaUpdate().set(Table::getLoadedViewId, loadTableDTO.getViewId()).eq(Table::getId, loadTableDTO.getId()));
            dto.setViewId(loadTableDTO.getViewId());
        }

        if (!MemGridUtils.contains(vo.getMemTableName())) {
            MemGridUtils.createTable(dto);
        }
        boolean isLoaded = MemGridUtils.getLoaded(vo.getMemTableName());
        if (isLoaded) {
            return AjaxResult.success(String.format("二维表[%s]为已加载状态。", vo.getTableName()));
        }
        // 任务信息
        dto.setMatchFields(false);
        dto.setTableDataLoadConfigs(loadTableDTO.getTableDataLoadConfigs());
        // 任务信息
        String taskId = TaskUtil.putTableCache(loadTableDTO.getId(), JobOperateTypeEnums.LOAD_DATA_SOURCE);
        tableService.syncTableData(dto);
        return TaskUtil.getTaskIdAjaxResult(taskId);
    }

    @Operation(summary = "二维表-生成数据")
    @PostMapping("gen")
    public AjaxResult gen(@RequestBody List<TableGenDto> tableGenDtoList) {
        boolean isLock = tableService.getLock().tryLock(tableGenDtoList.get(0).getTableId());
        if (isLock) {
            String taskId = TaskUtil.putTableCache(tableGenDtoList.get(0).getTableId(), JobOperateTypeEnums.GENERATE_DATA);
            tableService.gen(tableGenDtoList);
            return TaskUtil.getTaskIdAjaxResult(taskId);
        } else {
            throw new ServiceException("当前二维表已存在数据生成任务正在执行。");
        }
    }

    @Operation(summary = "二维表-查询生成数据配置")
    @PostMapping("gen_info")
    public AjaxResult genInfo(@RequestBody TableGenQuery query) {
        return AjaxResult.success(tableService.genInfo(query));
    }

    @Operation(summary = "二维表-释放数据")
    @PostMapping("release")
    public AjaxResult release(@RequestParam("id") Integer id) {
        Table table = tableService.getById(id);
        if (!TableType.TABLE.getCode().equals(table.getType())) {
            return AjaxResult.success(String.format("二维表[%s]不存在。", table.getTableName()));
        }
        MemGridUtils.release(table.getMemTableName());
        return AjaxResult.success();
    }

    @Operation(summary = "二维表-加载数据任务状态")
    @PostMapping("loading_state")
    public AjaxResult loadingState(@RequestBody List<String> ids) {
        List<ReportDto> reportList = TaskReport.get(ids);
        return AjaxResult.success(reportList);
    }

    @Deprecated
    @Operation(summary = "二维表-备份数据")
    @PostMapping("backup")
    public AjaxResult backup(@RequestBody TableVo tableVo) {
        // 任务信息
        String taskId = TaskUtil.putTableCache(tableVo.getId(), JobOperateTypeEnums.BACKUP);
        try {
            Table table = tableService.getById(tableVo.getId());
            if (!TableType.TABLE.getCode().equals(table.getType())) {
                String msg = String.format("二维表[%s]不存在。", table.getTableName());
                throw new ServiceException(msg);
            }
            BeanUtils.copyProperties(table, tableVo);
            tableVo.setTableMetaJson(JSON.parseArray(table.getTableMeta(), TableMetaJson.class));
            boolean isVersionExists = tableVersionService.versionExists(table.getId(), tableVo.getVersion());
            if (isVersionExists) {
                String msg = String.format("备份版本名称【%s】已存在。", tableVo.getVersion());
                log.error(msg);
                TaskUtil.updateTableTaskStatus(table.getId(), TaskStatusEnums.ERROR, msg);
                throw new ServiceException(msg);
            }
            //备份数据
            tableVo.setCreateBy(getUsername());
            tableService.backup(tableVo);
        } catch (Exception e) {
            log.error("二维表-备份数据时出现异常！", e);
            TaskUtil.updateTableTaskStatus(tableVo.getId(), TaskStatusEnums.ERROR, globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "系统出现异常，请联系系统管理员！");
            throw new ServiceException("二维表备份数据时出现异常");
        }
        return TaskUtil.getTaskIdAjaxResult(taskId);
    }

    @Operation(summary = "二维表-恢复数据")
    @PostMapping("/recoverByVersion/{tableId}/{versionId}/{baseDataDt}")
    public AjaxResult recoverByVersion(@PathVariable("tableId") Integer tableId, @PathVariable("versionId") Integer versionId, @PathVariable("baseDataDt") Date baseDataDt) {
        // 任务信息
        String taskId = TaskUtil.putTableCache(tableId, JobOperateTypeEnums.LOAD_DATA_VERSION);
        try {
            TableVo vo = tableService.get(tableId);
            if (!TableType.TABLE.getCode().equals(vo.getType())) {
                String msg = String.format("二维表[%s]不存在。", vo.getTableName());
                throw new ServiceException(msg);
            }
            TableVersion tableVersion = tableVersionService.get(versionId);
            if (tableVersion == null) {
                String msg = String.format("二维表备份版本名称[%s]不存在。", versionId);
                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.ERROR, msg);
                throw new ServiceException(msg);
            }
            TableDto dto = new TableDto();
            BeanUtils.copyProperties(vo, dto);
            dto.setTableMetaJson(JSON.parseArray(tableVersion.getTableMeta(), TableMetaJson.class));
            //清空数据
            MemGridUtils.release(vo.getMemTableName());
            //创建内存数据表
            MemGridUtils.createTable(dto);
            // 任务信息
            dto.setMatchFields(true);
            dto.setBackupTableName(tableVersion.getBackupTableName());
            dto.setBackupVersion(tableVersion.getVersion());
            dto.setBackupVersionDataVersion(tableVersion.getDataVersion());
            dto.setBaseDataDt(baseDataDt);
            //更新二维表tableMetadata
            Table updateData = new Table();
            updateData.setId(tableId);
            updateData.setTableMeta(tableVersion.getTableMeta());
            tableService.updateById(updateData);

            tableService.syncTableData(dto);
        } catch (Exception e) {
            log.error("二维表-恢复数据时出现异常！", e);
            TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.ERROR, globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "系统出现异常，请联系系统管理员！");
            throw new ServiceException("二维表恢复数据时出现异常！");
        }
        return TaskUtil.getTaskIdAjaxResult(taskId);
    }

    @Operation(summary = "二维表-生成id")
    @PostMapping("/createIncreId")
    public AjaxResult createIncreId(@RequestBody TableDto dto) {
        return AjaxResult.success(tableService.getIncreId(dto));
    }

//    @Log(title = "二维表-Excel导入-上传zip文件", businessType = BusinessType.IMPORT)
//    @Operation(summary = "二维表-Excel导入-上传zip文件")
//    @PostMapping("/uploadZipFile")
//    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) {
//        return AjaxResult.success(excelHandlerService.uploadFile(file));
//    }

    @Operation(summary = "二维表-Excel导入-解压文件")
    @PostMapping("/unzip")
    public AjaxResult unzip(@RequestBody ExcelImpVo excelImpVo) {
        excelHandlerService.unzipFile(excelImpVo);
        return AjaxResult.success();
    }

    @Operation(summary = "二维表-Excel导入-预览Excel文件")
    @PostMapping("/previewExcel")
    public AjaxResult previewExcel(@RequestBody ExcelImpVo excelImpVo) {
        return AjaxResult.success(excelHandlerService.preview(excelImpVo.getFileName()));
    }

    @Log(title = "二维表-Excel导入-删除Excel文件", businessType = BusinessType.DELETE)
    @Operation(summary = "二维表-Excel导入-删除Excel文件")
    @DeleteMapping("/removeExcels")
    public AjaxResult removeExcels(@RequestBody ExcelImpVo excelImpVo) {
        excelHandlerService.removeFiles(excelImpVo);
        return AjaxResult.success();
    }

    @Log(title = "二维表-Excel导入-合并并导入Excel文件", businessType = BusinessType.INSERT)
    @Operation(summary = "二维表-Excel导入-合并并导入Excel文件")
    @PostMapping("/impExcel")
    public AjaxResult impExcel(@RequestBody ExcelImpVo excelImpVo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        excelHandlerService.impExcel(excelImpVo, loginUser);
        return AjaxResult.success();
    }

    @Log(title = "二维表-Excel导入-导出二维表数据（xlsx文件）", businessType = BusinessType.EXPORT)
    @Operation(summary = "二维表-Excel导入-导出二维表数据（xlsx文件）")
    @PostMapping("/exportData")
    public AjaxResult exportData(HttpServletResponse response, @RequestBody TableDataExportQuery query) {
        excelHandlerService.exportData(response, query);
        return AjaxResult.success();
    }

    @Log(title = "二维表-Excel导入-导出二维表数据（zip文件）", businessType = BusinessType.EXPORT)
    @Operation(summary = "二维表-Excel导入-导出二维表数据（zip文件）")
    @PostMapping("/exportMemData")
    public AjaxResult exportMemData(HttpServletResponse response, @RequestBody TableDataExportQuery query) {
        excelHandlerService.exportMemData(response, query);
        return AjaxResult.success();
    }

    @Log(title = "二维表-Excel导入-下载二维表导入模版", businessType = BusinessType.EXPORT)
    @Operation(summary = "二维表-Excel导入-下载二维表导入模版")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response, @RequestParam("tableId") Integer tableId) {
        try {
            TableVo tableVo = tableService.get(tableId);
            if (Objects.isNull(tableVo)) {
                log.error("二维表不存在");
                throw new ServerException("二维表不存在");
            }
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(tableVo.getTableName(), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            List<List<String>> headList = TableUtils.fetchHeadList(tableVo);
            EasyExcel.write(response.getOutputStream()).head(headList)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet(tableVo.getTableName())
                    .doWrite(Collections.emptyList());
        } catch (Exception e) {
            log.error("文件下载失败");
            throw new ServiceException("文件下载失败");
        }
    }

    @Log(title = "二维表-Excel导入", businessType = BusinessType.EXPORT)
    @Operation(summary = "二维表-Excel导入")
    @PostMapping("/impData")
    public AjaxResult impData(@RequestParam("file") MultipartFile file, @RequestParam("tableId") Integer tableId) throws IOException {
        // 校验文件合法
        FileUploadUtils.validateUploadFile(file, FileUploadUtils.FileType.EXCEL);

        TableVo table = tableService.get(tableId);
        if (Objects.isNull(table)) {
            log.error("二维表不存在");
            throw new ServiceException("二维表不存在");
        }
        //开启极速读取
        SimpleReadCacheSelector simpleReadCacheSelector = enableFastRead();
        ExcelReader excelReader = EasyExcel.read(file.getInputStream()).readCacheSelector(simpleReadCacheSelector).build();
        ReadSheet sheet = EasyExcel.readSheet(0).registerReadListener(new ExcelTableImpListener(table, tableService)).build();
        excelReader.read(sheet);
        excelReader.finish();
        return AjaxResult.success();
    }

    @GetMapping("/pk")
    @Operation(summary = "查询表主键", description = "")
    public AjaxResult pk(@RequestParam("id") Integer id) {
        TableVo vo = tableService.get(id);
        List<TableMetaJson> metaList = vo.getTableMetaJson();
        if (CollectionUtil.isNotEmpty(metaList)) {
            return AjaxResult.success(metaList.get(0));
        }

        return AjaxResult.success();
    }

    @Deprecated
    @Operation(summary = "二维表-备份至数据库-指定字段")
    @PostMapping("backupWithCustom")
    public AjaxResult backupWithCustom(@RequestBody TableBackupVo tableBackupVo) {
        // 任务信息
        String taskId = TaskUtil.putTableCache(tableBackupVo.getId(), JobOperateTypeEnums.BACKUP);
        tableService.backupWithCustom(tableBackupVo);
        return TaskUtil.getTaskIdAjaxResult(taskId);
    }
}
