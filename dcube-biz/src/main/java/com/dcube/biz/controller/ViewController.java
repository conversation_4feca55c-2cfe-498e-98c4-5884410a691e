package com.dcube.biz.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.Source;
import com.dcube.biz.domain.View;
import com.dcube.biz.dto.ViewDto;
import com.dcube.biz.query.ViewListQuery;
import com.dcube.biz.service.ISourceService;
import com.dcube.biz.service.IViewService;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.biz.vo.SourceVO;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.enums.BusinessType;
import com.dcube.common.utils.CryptoUtil;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.file.FileUploadUtils;
import com.dcube.common.utils.poi.ExcelUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("view")
@Tag(name = "DCUBE-数据视图", description = "DCUBE-数据视图")
public class ViewController extends BaseController {

    @Autowired
    private IViewService viewService;
    @Autowired
    private ISourceService sourceService;

    @GetMapping("list")
    @Operation(summary = "查询数据视图列表", description = "分页参数pageNum和pageSize直接拼接到url上")
    @Deprecated
    public TableDataInfo list(ViewListQuery query) {
        startPage();
        LambdaQueryWrapper<View> qw = new LambdaQueryWrapper<>();
        qw.select(View::getId, View::getDirectoryId, View::getSourceId, View::getViewName, View::getStatus,
                View::getCreateBy, View::getUpdateBy, View::getCreateTime, View::getUpdateTime);
        qw.like(StringUtils.isNotEmpty(query.getViewName()), View::getViewName, query.getViewName());
        qw.eq(StringUtils.isNotEmpty(query.getStatus()), View::getStatus, query.getStatus());
        qw.orderByDesc(View::getCreateTime);
        List<View> list = viewService.list(qw);
//        if (CollectionUtil.isNotEmpty(list)) {
//            list.forEach(o -> {
//                if (StringUtils.isNotEmpty(o.getSourceId())) {
//                    Source source = sourceService.getById(o.getSourceId());
//                    if (Objects.nonNull(source)) {
//                        o.setSourceType(source.getSourceType());
//                    }
//                }
//            });
//        }
        return getDataTable(list);
    }

    @PostMapping("query")
    @Operation(summary = "查询数据视图列表", description = "分页参数pageNum和pageSize直接拼接到url上")
    public TableDataInfo query(@RequestBody ViewListQuery query) {
        startPage();
        LambdaQueryWrapper<View> qw = new LambdaQueryWrapper<>();
        qw.select(View::getId, View::getDirectoryId, View::getSourceId, View::getViewName, View::getStatus,
                View::getCreateBy, View::getUpdateBy, View::getCreateTime, View::getUpdateTime);
        qw.like(StringUtils.isNotEmpty(query.getViewName()), View::getViewName, query.getViewName());
        qw.eq(StringUtils.isNotEmpty(query.getStatus()), View::getStatus, query.getStatus());
        qw.orderByDesc(View::getCreateTime);
        List<View> list = viewService.list(qw);
        if (CollectionUtil.isNotEmpty(list)) {
            Set<String> sourceIds = StreamUtils.toSet(list, View::getSourceId);
            if (CollectionUtil.isNotEmpty(sourceIds)) {
                List<Source> sources = sourceService.listByIds(sourceIds);
                Map<String, String> sourceIdTypeMap = StreamUtils.toMap(sources, Source::getId, Source::getSourceType);
                list.forEach(o -> {
                    if (StringUtils.isNotEmpty(o.getSourceId())) {
                        String sourceType = MapUtils.getObject(sourceIdTypeMap, o.getSourceId());
                        o.setSourceType(sourceType);
                    }
                });
                if (StringUtils.isNotEmpty(query.getPublicKey())) {
                    RSA rsa = new RSA(null, query.getPublicKey());
                    list.forEach(view -> {
                        if (StringUtils.isNotEmpty(view.getSourceType())) {
                            view.setSourceType(CryptoUtil.encrypt(rsa, view.getSourceType(), KeyType.PublicKey));
                        }
                    });
                }
            }
        }
        return getDataTable(list);
    }

    @Log(title = "数据视图", businessType = BusinessType.EXPORT)
    @Operation(summary = "导出数据视图列表")
    @PostMapping("export")
    public void export(HttpServletResponse response, ViewListQuery query) {
        QueryWrapper<View> qw = new QueryWrapper<>();
        qw.lambda().like(StringUtils.isNotEmpty(query.getViewName()), View::getViewName, query.getViewName());
        qw.lambda().eq(StringUtils.isNotEmpty(query.getStatus()), View::getStatus, query.getStatus());
        qw.lambda().orderByDesc(View::getCreateTime);
        List<View> list = viewService.list(qw);
        ExcelUtil<View> util = new ExcelUtil<>(View.class);
        util.exportExcel(response, list, "数据视图");
    }

    @Operation(summary = "主键查询")
    @GetMapping("{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(viewService.get(id));
    }

    @Log(title = "数据视图", businessType = BusinessType.INSERT)
    @Operation(summary = "新增数据视图")
    @PostMapping("add")
    public AjaxResult add(@RequestBody ViewDto dto) {
        dto.setId(CharSequenceUtil.EMPTY);
        dto.setStatus(BizConstants.STATUS_ENABLE);
        return AjaxResult.success(viewService.saveOrUpdate(dto));
    }

    @Log(title = "数据视图", businessType = BusinessType.UPDATE)
    @Operation(summary = "主键修改")
    @PostMapping("put")
    public AjaxResult edit(@RequestBody ViewDto dto) {
        return AjaxResult.success(viewService.saveOrUpdate(dto));
    }

    @Log(title = "数据视图", businessType = BusinessType.DELETE)
    @Operation(summary = "主键数组删除")
    @PostMapping("remove/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(viewService.remove(ids));
    }

    @Log(title = "数据视图", businessType = BusinessType.UPDATE)
    @Operation(summary = "启用")
    @PostMapping("enable")
    public AjaxResult enable(@RequestParam("id") String id) {
        View domain = viewService.getById(id);
        domain.setStatus(BizConstants.STATUS_ENABLE);
        return toAjax(viewService.updateById(domain));
    }

    @Log(title = "数据视图", businessType = BusinessType.UPDATE)
    @Operation(summary = "禁用")
    @PostMapping("disable")
    public AjaxResult disable(@RequestParam("id") String id) {
        View domain = viewService.getById(id);
        domain.setStatus(BizConstants.STATUS_DISABLED);
        return toAjax(viewService.updateById(domain));
    }

    @GetMapping("option")
    @Operation(summary = "查询数据视图列表(启用状态，用于页面下拉列表)", description = "")
    public AjaxResult option(ViewListQuery query) {
        QueryWrapper<View> qw = new QueryWrapper<>();
        qw.lambda().like(StringUtils.isNotEmpty(query.getViewName()), View::getViewName, query.getViewName());
        qw.lambda().eq(View::getStatus, BizConstants.STATUS_ENABLE);
        qw.lambda().orderByDesc(View::getCreateTime);
        List<View> list = viewService.list(qw);
        return AjaxResult.success(list);
    }

    @Log(title = "测试视图连接", businessType = BusinessType.OTHER)
    @Operation(summary = "测试视图连接")
    @PostMapping("connection")
    public AjaxResult connection(@RequestBody ViewDto dto) {
        Connection connection = null;
        try {
            if (dto.getSourceConfig() == null) {
                SourceVO sourceVO = sourceService.getSourceVOById(dto.getSourceId(), null);
                dto.setSourceType(sourceVO.getSourceType());
                connection = JdbcUtils.getConnection(sourceVO.getSourceConfig(), false);
            } else {
                connection = JdbcUtils.getConnection(dto.getSourceConfig(), false);
            }
            boolean success = JdbcUtils.isConnected(connection);
            if (success) {
                return AjaxResult.success("数据库连接成功。");
            } else {
                return AjaxResult.success("连接失败,请检查连接配置。");
            }
        } finally {
            JdbcUtils.close(connection);
        }
    }

    @Log(title = "视图元数据(页面配置)", businessType = BusinessType.OTHER)
    @Operation(summary = "视图元数据(页面配置)")
    @PostMapping("load_with_config")
    public AjaxResult load(@RequestBody ViewDto dto) {
        return AjaxResult.success(viewService.load(dto));
    }

    @Log(title = "视图元数据", businessType = BusinessType.OTHER)
    @Operation(summary = "视图元数据")
    @PostMapping("load")
    public AjaxResult load(@RequestParam("id") String id) {
        return AjaxResult.success(viewService.loadById(id));
    }

    @Operation(summary = "加载视图数据", description = "预览数据需要时传整个对象，添加二维表，加载数据，传viewId")
    @PostMapping("loadData")
    public AjaxResult loadData(@RequestBody ViewDto dto) {
        return AjaxResult.success(viewService.loadViewData(dto));
    }

    @Operation(summary = "根据视图Id查询")
    @GetMapping("loadDataById/{id}")
    public AjaxResult loadDataById(@PathVariable("id") String id) {
        return AjaxResult.success(viewService.loadViewDataById(id));
    }

    @Operation(summary = "上传Excel文件")
    @PostMapping("upload")
    public AjaxResult upload(@RequestParam("file") MultipartFile file) throws IOException {
        FileUploadUtils.validateUploadFile(file, FileUploadUtils.FileType.EXCEL);
        return AjaxResult.success(viewService.upload(file));
    }

    @GetMapping("txtViewOption")
    @Operation(summary = "查询Text数据视图列表(启用状态，用于页面下拉列表)", description = "")
    public AjaxResult txtViewOption(ViewListQuery query) {
        List<View> list = new ArrayList<>();
        List<Source> sources = sourceService.queryTextSource();
        if (CollectionUtil.isNotEmpty(sources)) {
            QueryWrapper<View> qw = new QueryWrapper<>();
            qw.lambda().like(StringUtils.isNotEmpty(query.getViewName()), View::getViewName, query.getViewName());
            qw.lambda().eq(View::getStatus, BizConstants.STATUS_ENABLE);
            qw.lambda().in(View::getSourceId, sources.stream().map(Source::getId).collect(Collectors.toList()));
            qw.lambda().orderByDesc(View::getCreateTime);
            list = viewService.list(qw);
            if (CollectionUtil.isNotEmpty(list)) {
                list.forEach(view -> view.setSourceType(sources.stream().filter(source -> source.getId().equals(view.getSourceId())).findFirst().get().getSourceType()));
            }
        }
        return AjaxResult.success(list);
    }

}
