package com.dcube.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

@Data
@TableName("cube_dim_table")
public class DimTable extends Model<DimTable> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer id;

    /**
     * 表格名称
     */
    private String tableName;

    /**
     * 建表方式
     */
    private String createMode;

    /**
     * 数据视图ID
     */
    private String viewId;

    /**
     * 父ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer parentId;

    /**
     * 维度表类型（分组或维度表实例）
     */
    private String type;

    /**
     * 全路径
     */
    private String ancestors;

    /**
     * 内存维度表名
     */
    private String memTableName;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 单元格数
     */
    @TableField(exist = false)
    private Long cellSize = 0L;

    /**
     * 内存大小
     */
    @TableField(exist = false)
    private String memorySize;
}
