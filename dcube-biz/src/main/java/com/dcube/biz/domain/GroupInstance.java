package com.dcube.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
@TableName("cube_group_instance")
public class GroupInstance extends Model<GroupInstance> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 分组实例名称
     */
    private String groupInstanceName;

    /**
     * 分组实例说明
     */
    private String groupInstanceDesc;

    /**
     * 是否字典
     */
    private String dicFlag;

    /**
     * 存储类型
     */
    private String storageType;

    /**
     * 运算规则
     */
    private String operationalRule;

    /**
     * 保留小数
     */
    private Integer decimalPlaces;

    /**
     * 显示格式
     */
    private String showFormat;

    /**
     * 前缀字符
     */
    private String prefixChar;

    /**
     * 后缀字符
     */
    private String suffixChar;

    /**
     * 原数据示例
     */
    private String rawSample;

    /**
     * 显示示例
     */
    private String showSample;

    /**
     * 对齐方式
     */
    private String contentAlign;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
