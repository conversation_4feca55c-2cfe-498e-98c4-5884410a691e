package com.dcube.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

@Data
@TableName("cube_ind")
public class Ind extends Model<Ind> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer id;

    /**
     * 指标名称
     */
    private String indName;

    /**
     * 指标类型（分组或指标实例）
     */
    private String indType;

    /**
     * 显示序号
     */
    private Integer indexNo;

    /**
     * 上级目录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer parentId;

//    /**
//     * 上级目录路径
//     */
//    private String parentAncestors;

    /**
     * 节点层次
     */
    private Integer levelNumber;

    /**
     * 数据格式id
     */
    private String dataFormatId;

    /**
     * 聚合函数名称
     */
    private String functionName;

    /**
     * 聚合函数表达式
     */
    private String functionValue;

    /**
     * 加权平均指标ID
     */
    private String avgParmaIndId;
}
