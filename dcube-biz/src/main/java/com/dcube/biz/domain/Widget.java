package com.dcube.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @创建人 zhouhx
 * @创建时间 2023/11/14 10:17
 * @描述 仪表板
 */
@Data
@TableName("cube_widget")
public class Widget extends Model<Widget> {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    private String name;

    private String viewId;

    private String viewName;

    private String dashboardId;

    private String config;

    private Integer seq;

    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
