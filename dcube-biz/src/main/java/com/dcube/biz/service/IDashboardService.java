package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.constant.enums.DashboardStatusEnum;
import com.dcube.biz.domain.Dashboard;
import com.dcube.biz.dto.DashboardDto;
import com.dcube.biz.vo.DashboardVo;

import java.util.List;
import java.util.Map;

public interface IDashboardService extends IService<Dashboard> {

    Boolean add(DashboardDto dashboard);

    Boolean update(DashboardDto dashboard);

    Dashboard getById(String id);

    DashboardVo getCurrentDashboard(Long deptId, DashboardStatusEnum status);

    Boolean status(String id, DashboardStatusEnum status);

    List<Map<String, Object>> themeList();
}
