package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.DicItem;
import com.dcube.biz.dto.DicItemDto;

import java.util.List;

public interface IDicItemService extends IService<DicItem> {

    Boolean save(List<DicItemDto> dtoList);

    Boolean update(List<DicItemDto> dtoList);

    Boolean saveOrUpdate(DicItemDto dto);

    List<DicItemDto> getByDictId(String dictId);

    List<DicItemDto> getAll();
}
