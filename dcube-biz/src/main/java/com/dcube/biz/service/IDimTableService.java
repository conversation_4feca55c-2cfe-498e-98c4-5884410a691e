package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.DimTable;
import com.dcube.biz.domain.Ind;
import com.dcube.biz.dto.DimTableDataDto;
import com.dcube.biz.dto.DimTableDto;
import com.dcube.biz.dto.LayoutDto;
import com.dcube.biz.query.DimTableDataQuery;
import com.dcube.biz.query.DimTableListQuery;
import com.dcube.biz.vo.DimTableDataVo;
import com.dcube.biz.vo.DimTableInfoVo;
import com.dcube.biz.vo.DimTableVO;
import com.dcube.cube.core.FactTable;
import com.dcube.cube.spi.CubeServer;

import java.util.List;

public interface IDimTableService extends IService<DimTable> {

    void layout(LayoutDto layoutDto);

    Integer saveExt(DimTableDto dimTableDto);

    Integer updateExt(DimTableDto dimTableDto);

    DimTableInfoVo getByIdExt(Integer id);

    DimTableInfoVo getByIdExt(Integer id, Long userId);

    void loading(Integer id);

    DimTableDataVo listTableData(DimTableDataQuery query);

    void addData(DimTableDataDto dimTableDataDto);

    List<DimTable> list(DimTableListQuery query);

    List<DimTableVO> queryList(DimTableListQuery query);

    DimTableInfoVo getByTableName(String tableName);

    void backup(Integer id);

    boolean rename(DimTableDto dto);

    void removeByIds(Integer[] ids);

    double getSummaryInd(CubeServer cubeServer, List<FactTable.Record> recordList, Ind ind, int dimInstanceNum);
}
