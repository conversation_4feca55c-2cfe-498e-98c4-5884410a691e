package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.SourceDetail;
import com.dcube.biz.dto.SourceDetailDto;
import com.dcube.biz.dto.SourceDto;
import com.dcube.biz.dto.SourceResolvingDto;

import java.util.List;

public interface ISourceDetailService extends IService<SourceDetail> {

    List<SourceDetail> queryListBySourceId(String sourceId);

    List<SourceDetailDto> fetchingTextHead(SourceResolvingDto source);

    List<SourceDetailDto> fetchingExcelHead(SourceResolvingDto source);

    Boolean saveSourceDetail(SourceDto source);

    Boolean removeSourceDetail(List<String> sourceIds);

    Boolean dropTableBySource(List<SourceDetail> details);

    Boolean clearTableData(String sourceId);
}
