package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.TableBackupConfig;
import com.dcube.biz.dto.TableBackupConfigSaveDTO;
import com.dcube.biz.vo.TableBackupConfigSaveVO;
import com.dcube.biz.vo.TableBackupConfigVO;

import java.util.List;

/**
 * 二维表备份配置信息表服务
 */
public interface ITableBackupConfigService extends IService<TableBackupConfig> {

    TableBackupConfigVO queryByTableId(Long tableId);

    List<String> getColumnType();

    TableBackupConfigSaveVO saveTableBackupConfig(TableBackupConfigSaveDTO tableBackupConfigSaveDto);
}
