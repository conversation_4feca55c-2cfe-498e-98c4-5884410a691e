package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.Table;
import com.dcube.biz.domain.TableGen;
import com.dcube.biz.dto.*;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.query.SubTableListQuery;
import com.dcube.biz.query.TableGenQuery;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.util.KeyLock;
import com.dcube.biz.vo.AggregationTableVo;
import com.dcube.biz.vo.MemTableDataVo;
import com.dcube.biz.vo.TableBackupVo;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.tran.store.example.ConditionExample;
import com.github.pagehelper.PageInfo;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 二维表服务
 */
public interface ITableService extends IService<Table> {

    KeyLock<Integer> getLock();

    TableVo get(Integer id);

    List<TableVo> getByIds(List<Integer> ids);

    Table saveOrUpdateWithSub(SubTableDto dto);

    Table saveOrUpdate(TableDto dto);

    Table saveWithAggregation(AggregationTableVo aggregationTableVo);

    void addColumn(TableDto table);

    long count(Integer id, String name);

    void syncTableData(TableDto table);

    /**
     * 同步的方式同步数据
     *
     * @param table
     */
    void syncSyncTableData(TableDto table);

    PageInfo<MemTableDataVo> queryTableData(TableListQuery query, LoginUser sysUser);

    List<Map<String, Object>> exportTableData(TableListQuery query, LoginUser loginUser);

    Map<String, Object> avgAndSum(SubTableListQuery query, LoginUser loginUser);

    void addData(RowDataDto row);

    void deleteData(RowDataDto row);

    void updateData(RowDataDto row);

    void gen(List<TableGenDto> tableGenDtoList);

    List<TableGen> genInfo(TableGenQuery tableGenQuery);

    void parseCondition(ConditionExample example, ConditionExample.Criteria criteria, TableListQuery query, List<TableMetaJson> tableMetaJson);

    /**
     * 异步的方式备份
     *
     * @param table
     * @throws SQLException
     */
    @Deprecated
    void backup(TableVo table) throws SQLException;

    /**
     * 同步的方式备份
     *
     * @param table
     * @throws SQLException
     */
    @Deprecated
    void backupSync(TableVo table) throws SQLException;

    Integer getIncreId(TableDto table);

    String getFormatValue(TableMetaJson json, Object value) throws Exception;

    boolean removeWithExt(Long id);

    List<TableVo> getByAncestors(String group);

    List<Object[]> parseDataColl(RowDataDto rowData);

    Table getTableByViewId(String viewId);

    Table save2dTable(TableDto dto);

    List<Table> list(TableListQuery query);

    void backupWithCustom(TableBackupVo tableVo);

    void updateParentTableMeta(UpdateParentTableMetaDto dto);

    List<TableMetaJson> pkList(Integer id, Integer subTableId);

    List<TableMetaJson> parentPkList(Integer id);

    void addTableVersion(Integer tableId, String memTableName, String backupTableName, String taleMeta, String createUserName, String version, Date baseDataDt);

    void addTableVersion(Integer tableId, String memTableName, String backupTableName, String taleMeta, String createUserName, String version, Integer dataVersion, Date baseDataDt);
}
