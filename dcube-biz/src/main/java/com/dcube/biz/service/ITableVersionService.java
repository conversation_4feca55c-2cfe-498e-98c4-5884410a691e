package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.TableVersion;
import com.dcube.biz.vo.TableVersionSelectVo;

import java.util.List;

/**
 * 二维表版本服务
 */
public interface ITableVersionService extends IService<TableVersion> {

    TableVersion get(Integer id);

    boolean insert(TableVersion tableVersion);

    List<TableVersionSelectVo> queryByTableId(Integer tableId);

    List<TableVersionSelectVo> getTableVersionSelect(List<TableVersion> list);

    boolean versionExists(Integer tableId, String version);

    boolean deleteVersions(List<TableVersion> todoDeletes);
}
