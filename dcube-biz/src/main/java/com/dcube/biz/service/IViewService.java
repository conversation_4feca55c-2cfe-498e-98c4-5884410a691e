package com.dcube.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.domain.Source;
import com.dcube.biz.domain.View;
import com.dcube.biz.dto.CryptoDTO;
import com.dcube.biz.dto.TextViewDto;
import com.dcube.biz.dto.ViewDto;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.vo.SimpleViewVo;
import com.dcube.biz.vo.ViewVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface IViewService extends IService<View> {

    ViewVo get(String id);

    Boolean saveOrUpdate(ViewDto dto);

    Boolean remove(String[] ids);

    Map<String, Object> loadViewData(ViewDto dto);

    void checkSqlInvalid(String viewScript);

    Map<String, Object> loadViewDataById(String id);

    long count(String id, String name);

    List<TableMetaJson> load(ViewDto dto);

    List<TableMetaJson> loadById(String id);

    Map<String, Object> upload(MultipartFile file) throws IOException;

    List<SimpleViewVo> queryList(String viewName, String status);

    Source getSourceByViewId(String viewId);

}
