package com.dcube.biz.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.biz.constant.enums.SourceTypeEnum;
import com.dcube.biz.domain.Widget;
import com.dcube.biz.dto.BatchReportDto;
import com.dcube.biz.dto.BatchWidgetDto;
import com.dcube.biz.dto.WidgetDto;
import com.dcube.biz.dto.WidgetParamDto;
import com.dcube.biz.query.WidgetListQuery;
import com.dcube.biz.query.WidgetViewListQuery;
import com.dcube.biz.vo.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface IWidgetService extends IService<Widget> {

    String saveOrUpdate(WidgetDto widget);

    void batchSaveOrUpdate(BatchWidgetDto batchDto);

    void batchSaveOrUpdateReport(BatchReportDto batchDto);

    WidgetVo get(String id);

    Boolean removeByDashboardIds(String[] dashboardIds);

    Boolean removeWidget(String id);

    List<WidgetDto> queryByDashboardId(String dashboardId);

    ViewListVo queryList(WidgetViewListQuery query);

    WidgetDataVo preview(WidgetParamDto widgetParamDto);

    List<TableTreeVo> queryTableList(WidgetViewListQuery query);

    DashboardVo getWidgetsByDashboardId(WidgetListQuery query);

    List<Map<String, Object>> aggregationTypeList();
}
