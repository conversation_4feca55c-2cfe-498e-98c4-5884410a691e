package com.dcube.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.base.PageDto;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.DataFoundationTable;
import com.dcube.biz.domain.Source;
import com.dcube.biz.domain.TableVersion;
import com.dcube.biz.domain.View;
import com.dcube.biz.dto.*;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.mapper.DataFoundationTableMapper;
import com.dcube.biz.mapper.TableVersionMapper;
import com.dcube.biz.query.DataFoundationDataVersionQuery;
import com.dcube.biz.query.DataFoundationTableQuery;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.service.*;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.vo.DataFoundationTableVo;
import com.dcube.biz.vo.MemTableDataVo;
import com.dcube.biz.vo.SourceVO;
import com.dcube.biz.vo.TableVersionSelectVo;
import com.dcube.common.enums.TableType;
import com.dcube.common.enums.ValueType;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.exception.ai.TableStructureException;
import com.dcube.common.utils.StringUtils;
import com.dcube.tran.store.example.ConditionExample;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.ClickHouseRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.sql.Connection;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @创建人 zhouhx
 * @创建时间 2025/05/02 16:15
 * @描述 数据底表服务
 */
@Service
@Slf4j
public class DataFoundationTableServiceImpl extends ServiceImpl<DataFoundationTableMapper, DataFoundationTable> implements IDataFoundationTableService {

    @Autowired
    private ISourceService sourceService;

    @Autowired
    private IViewService viewService;

    @Autowired
    private ITableService tableService;

    @Autowired
    private IGroupInstanceService groupInstanceService;

    @Autowired
    private TableVersionMapper tableVersionMapper;

    @Autowired
    private ITableVersionService tableVersionService;

    private final Gson gson = new Gson();


    @Override
    public DataFoundationTableVo get(Integer id) {
        DataFoundationTableVo tableVo = new DataFoundationTableVo();
        DataFoundationTable table = getById(id);
        BeanUtils.copyProperties(table, tableVo);
        if (TableType.TABLE.getCode().equals(table.getType())) {
            tableVo.setTableMetaJson(JSON.parseArray(table.getTableMeta(), TableMetaJson.class));
        }
        return tableVo;
    }

    @Override
    public List<TableMetaJson> preview(DataFoundationTableViewDto viewDto) {
        if (Objects.isNull(viewDto)) {
            throw new ServiceException("底表参数不能为空。");
        }
        if (StringUtils.isEmpty(viewDto.getSqlScript())) {
            throw new ServiceException("sql脚本不能为空。");
        }
        validateDataFoundationExists();// 验证是否配置底表数据源
        SourceVO dataFoundationSource = sourceService.getDataFoundationSource();//获取底表数据源
        viewService.checkSqlInvalid(viewDto.getSqlScript());//验证sql是否合法，只能是Select语句

        ViewDto dto = new ViewDto();
        dto.setViewScript(viewDto.getSqlScript());
        Source sourceDomain = sourceService.getById(dataFoundationSource.getId());
        if (Objects.nonNull(sourceDomain)) {
            dto.setSourceType(sourceDomain.getSourceType());
            if (StringUtils.isNotBlank(sourceDomain.getSourceConfig())) {
                dto.setSourceConfig(gson.fromJson(sourceDomain.getSourceConfig(), SourceConfigJson.class));
            }
        }
        return JdbcUtils.loadSqlFieldList(dto);
    }

    @Override
    public Map<String, Object> loadData(DataFoundationTableViewDto viewDto) {
        if (Objects.isNull(viewDto)) {
            throw new ServiceException("底表参数不能为空。");
        }
        if (StringUtils.isEmpty(viewDto.getSqlScript())) {
            throw new ServiceException("sql脚本不能为空。");
        }
        validateDataFoundationExists();// 验证是否配置底表数据源
        SourceVO dataFoundationSource = sourceService.getDataFoundationSource();//获取底表数据源
        viewService.checkSqlInvalid(viewDto.getSqlScript());//验证sql是否合法，只能是Select语句
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(2);
        long totalCount = 0;
        List<Map<String, Object>> data = null;
        List<TableMetaJson> tableMetaJsonList = null;
        if (Objects.nonNull(dataFoundationSource.getSourceConfig()) && StringUtils.isNotEmpty(dataFoundationSource.getSourceType())) {
            Source source = sourceService.getById(dataFoundationSource.getId());
            AbstractRepository resRepository = RepositoryFactory.getRepository(dataFoundationSource.getSourceType(), gson.fromJson(source.getSourceConfig(), SourceConfigJson.class), null);
            PageDto pageDto = new PageDto();
            pageDto.setCurrentPage(1);
            pageDto.setPageSize(10);
            totalCount = resRepository.executeCountSql("select count(0) from(" + viewDto.getSqlScript() + ") tmp");
            data = resRepository.listPage(viewDto.getSqlScript(), pageDto);

            ViewDto dto = new ViewDto();
            dto.setViewScript(viewDto.getSqlScript());
            dto.setSourceType(dataFoundationSource.getSourceType());
            if (StringUtils.isNotBlank(source.getSourceConfig())) {
                dto.setSourceConfig(gson.fromJson(source.getSourceConfig(), SourceConfigJson.class));
                tableMetaJsonList = JdbcUtils.loadSqlFieldList(dto);
            }
        }
        result.put("result", data);
        result.put("totalCount", totalCount);
        result.put("tableMeta", tableMetaJsonList);
        return result;
    }

    @Transactional
    @Override
    public DataFoundationTable saveOrUpdate(DataFoundationTableDto dto) {
        DataFoundationTable domain = new DataFoundationTable();
        BeanUtils.copyProperties(dto, domain);
        // 验证名称
        String oldTableName = "";
        if (domain.getId() != null) {
            DataFoundationTable table = super.getById(domain.getId());
            Assert.notNull(table, "当前底表不存在");
            oldTableName = table.getTableName();
        }
        if (domain.getId() == null || !StringUtils.equals(oldTableName, domain.getTableName())) {
            long count = this.count(new QueryWrapper<DataFoundationTable>().lambda()
                    .eq(DataFoundationTable::getTableName, domain.getTableName()));
            if (count > 0) {
                log.error("底表名称[{}]已存在。", domain.getTableName());
                throw new ServiceException(String.format("底表名称[%s]已存在。", domain.getTableName()));
            }
        }
        List<GroupInstanceDto> groupInstants = groupInstanceService.queryByStorageTypes(new ValueType[]{ValueType.DATE, ValueType.VARCHAR, ValueType.INTEGER, ValueType.DOUBLE});
        if (domain.getId() == null) { //新增
            if (dto.getParentId() == null) { //添加分组
                //顶级节点
                domain.setParentId(0);
                domain.setAncestors("0");
                domain.setTableLevel(0);
            } else { //添加数据底表
                DataFoundationTable parentTable = getParentTable(dto.getParentId());
                if (parentTable != null) {
                    domain.setAncestors(parentTable.getAncestors() + "," + dto.getParentId());
                    if (StringUtils.equals(dto.getType(), TableType.TABLE.getCode())) {
                        //判断是否有设置了数据底座的数据源
                        validateDataFoundationExists();
                        // 新增table
                        domain.setTableLevel(parentTable.getTableLevel() + 1);
                        List<TableMetaJson> tableMeta = dto.getTableMetaJson();
                        if (CollectionUtil.isNotEmpty(tableMeta)) {
                            for (TableMetaJson tableMetaJson : tableMeta) {
                                String storageType = ValueType.BIGINT.name().equals(tableMetaJson.getNewColumnType()) || ValueType.DECIMAL.name().equals(tableMetaJson.getNewColumnType()) ? ValueType.DOUBLE.name() : ValueType.valueOf(tableMetaJson.getNewColumnType()).name();
                                if (ValueType.TIMESTAMP.name().equals(tableMetaJson.getNewColumnType())) {
                                    storageType = ValueType.DATE.name();
                                }
                                String finalStorageType = storageType;
                                Optional<GroupInstanceDto> groupInstance = groupInstants.stream().filter(o -> o.getStorageType().equals(finalStorageType) && o.getDecimalPlaces().intValue() == 4 && StringUtils.isEmpty(o.getSuffixChar())).findFirst();
                                if (groupInstance.isPresent()) {
                                    tableMetaJson.setDataFormatId(groupInstance.get().getId());
                                    tableMetaJson.setDataFormat(groupInstance.get());
                                } else {
                                    String finalStorageType1 = storageType;
                                    groupInstance = groupInstants.stream().filter(o -> o.getStorageType().equals(finalStorageType1)).findFirst();
                                    if (groupInstance.isPresent()) {
                                        tableMetaJson.setDataFormatId(groupInstance.get().getId());
                                        tableMetaJson.setDataFormat(groupInstance.get());
                                    }
                                }
                            }
                        }
                        domain.setTableMeta(JSONUtil.toJsonStr(dto.getTableMetaJson()));
                    } else {
                        // 新增的分组tableLevel为0
                        domain.setTableLevel(0);
                    }
                }
            }
            this.save(domain);
        } else { //编辑
            //判断是否有设置了数据底座的数据源
            if (StringUtils.equals(dto.getType(), TableType.TABLE.getCode())) {
                validateDataFoundationExists();
                domain.setTableMeta(JSONUtil.toJsonStr(dto.getTableMetaJson()));
            }
            DataFoundationTable parentTable = getParentTable(dto.getParentId());
            if (parentTable != null) {
                domain.setTableLevel(parentTable.getTableLevel() + 1);
                domain.setAncestors(parentTable.getAncestors() + "," + dto.getParentId());
            }
            this.updateById(domain);
        }
        return domain;
    }

    @Override
    public void setColumn(DataFoundationTableDto table) {
        if (table == null) {
            log.error("参数不能为空。");
            throw new ServiceException("参数不能为空。");
        }
        if (CollectionUtil.isNotEmpty(table.getTableMetaJson())) {
            DataFoundationTable tableDomain = new DataFoundationTable();
            BeanUtils.copyProperties(table, tableDomain);
            tableDomain.setTableMeta(JSONUtil.toJsonStr(table.getTableMetaJson()));
            this.updateById(tableDomain);
        }
    }

    @Override
    public long count(Integer id, String name) {
        return this.count(new QueryWrapper<DataFoundationTable>().lambda()
                .ne(id != null, DataFoundationTable::getId, id)
                .eq(DataFoundationTable::getTableName, name));
    }

    @Override
    public List<DataFoundationTable> list(DataFoundationTableQuery query) {
        QueryWrapper<DataFoundationTable> qw = new QueryWrapper<>();
        qw.lambda().like(StringUtils.isNotEmpty(query.getTableName()), DataFoundationTable::getTableName, query.getTableName());
        qw.lambda().eq(StringUtils.isNotEmpty(query.getStatus()), DataFoundationTable::getStatus, query.getStatus());
        qw.lambda().orderByAsc(DataFoundationTable::getTableName);
        qw.lambda().orderByDesc(DataFoundationTable::getCreateTime);
        List<DataFoundationTable> list = this.list(qw);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        SourceVO dataFoundationSource = sourceService.getDataFoundationSource();//获取底表数据源
        Source source = sourceService.getById(dataFoundationSource.getId());
        AbstractRepository resRepository = RepositoryFactory.getRepository(dataFoundationSource.getSourceType(), gson.fromJson(source.getSourceConfig(), SourceConfigJson.class), null);
        for (DataFoundationTable table : list) {
            if (TableType.TABLE.getCode().equals(table.getType())) {
                if (Objects.nonNull(dataFoundationSource.getSourceConfig()) && StringUtils.isNotEmpty(dataFoundationSource.getSourceType())) {
                    Long totalCount = resRepository.executeCountSql("select count(0) from(" + table.getSqlScript() + ") tmp");
                    table.setTotalSize(totalCount);
                }
            }
        }
        List<DataFoundationTable> sortedTables = list.stream().sorted(Comparator.comparing(DataFoundationTable::getId)).collect(Collectors.toList());
        list = updateParent(sortedTables);
        list = list.stream()
                .sorted(Comparator.comparing(DataFoundationTable::getTableName)
                        .thenComparing(Comparator.comparing(DataFoundationTable::getCreateTime).reversed()))
                .collect(Collectors.toList());
        return list;
    }

    public Boolean testConnection(DataFoundationTableViewDto viewDto) {
        if (Objects.isNull(viewDto)) {
            throw new ServiceException("底表参数不能为空。");
        }
        validateDataFoundationExists();// 验证是否配置底表数据源
        SourceVO dataFoundationSource = sourceService.getDataFoundationSource();//获取底表数据源
        if (Objects.isNull(dataFoundationSource)) {
            log.error("底表数据源不存在。");
            throw new ServiceException("底表数据源不存在。");
        }
        Connection connection = null;
        try {
            connection = JdbcUtils.getConnection(dataFoundationSource.getSourceConfig(), false);
            boolean success = JdbcUtils.isConnected(connection);
            if (success) {
                return true;
            } else {
                return false;
            }
        } finally {
            JdbcUtils.close(connection);
        }
    }

    @Override
    public boolean remove(Integer id) {
        if (Objects.isNull(id)) {
            log.error("参数不能为空。");
            throw new ServiceException("参数不能为空。");
        }
        DataFoundationTable table = this.getById(id);
        if (Objects.isNull(table)) {
            log.error("数据底表不存在。");
            throw new ServiceException("数据底表不存在。");
        }
        List<Integer> idList = new ArrayList<>(Collections.singletonList(table.getId()));
        List<DataFoundationTable> tableList = new ArrayList<>();
        if (TableType.GROUP.getCode().equals(table.getType())) {
            List<DataFoundationTable> list = this.list(new QueryWrapper<DataFoundationTable>().lambda().like(DataFoundationTable::getAncestors, table.getParentId() + "," + table.getId()));
            if (CollectionUtil.isNotEmpty(list)) {
                idList.addAll(list.stream().map(DataFoundationTable::getId).collect(Collectors.toSet()));
                tableList.addAll(list.stream().filter(x -> TableType.TABLE.getCode().equals(x.getType())).collect(Collectors.toList()));
            }
        }
        return this.removeByIds(idList);
    }

    @Override
    public PageInfo<MemTableDataVo> queryTableData(DataFoundationTableQuery query) {
        if(!validateQueryCondition(query)){
            return PageInfo.emptyPageInfo();
        }
        List<MemTableDataVo> result = Lists.newArrayList();
        long count = 0;
        DataFoundationTableVo tableVo = this.get(query.getTableId());
        if (tableVo == null) {
            log.error("数据底表[{}]不存在。", query.getTableName());
            throw new ServiceException(String.format("数据底表[%s]不存在。", query.getTableName()));
        }
        SourceVO dataFoundationSource = sourceService.getDataFoundationSource();//获取底表数据源
        if (Objects.nonNull(dataFoundationSource)) {
            Source source = sourceService.getById(dataFoundationSource.getId());
            ClickHouseRepository tarRepository = (ClickHouseRepository) RepositoryFactory.getRepository(dataFoundationSource.getSourceType(), gson.fromJson(source.getSourceConfig(), SourceConfigJson.class), null);
            ConditionExample example = new ConditionExample();
            ConditionExample.Criteria criteria = example.createCriteria();
            example.setCurrentPage(query.getCurrentPage());
            example.setPageSize(query.getPageSize());
            tableService.parseCondition(example, criteria, BeanUtil.copyProperties(query, TableListQuery.class), tableVo.getTableMetaJson());
            List<FilterDto> requiredConditions = buildRequiredConditions(query.getFilterList());
            count = tarRepository.count(tableVo.getSqlScript(), example,requiredConditions);
            if (count > 0) {
                List<Map<String, Object>> data = tarRepository.listByPage(tableVo.getSqlScript(), example,requiredConditions);
                if (CollectionUtil.isNotEmpty(data)) {
                    MemTableDataVo tableData = new MemTableDataVo();
                    List<RowDto> rows = Lists.newArrayList();
                    for (Map<String, Object> map : data) {
                        RowDto rowDto = new RowDto();
                        List<CellDto> cells = Lists.newArrayList();
                        for (TableMetaJson column : tableVo.getTableMetaJson()) {
                            CellDto cellDto = new CellDto();
                            cellDto.setCode(column.getCode());
                            cellDto.setType(column.getOldColumnType());
                            cellDto.setOriginalValue(map.get(column.getCode()));
                            String displayValue = "";
                            try {
                                //数据字典
                                if (Objects.nonNull(column.getDataFormat()) && "Y".equals(column.getDataFormat().getDicFlag())) {
                                    if (Objects.nonNull(cellDto.getOriginalValue())) {
                                        Optional<DicItemDto> dictItem = column.getDicItems().stream().filter(o -> o.getDicCode().equals(cellDto.getOriginalValue())).findFirst();
                                        if (dictItem.isPresent()) {
                                            displayValue = dictItem.get().getDicLabel();
                                        }
                                    }
                                } else {
                                    displayValue = tableService.getFormatValue(column, cellDto.getOriginalValue());
                                    if (ValueType.DECIMAL.name().equals(cellDto.getType()) && displayValue.startsWith(".")) {
                                        //修复小数点开头的数字，如0.0204，显示为.0204的问题
                                        displayValue = "0" + displayValue;
                                    }
                                }
                                cellDto.setDisplayValue(displayValue);
                            } catch (Exception e) {
                                cellDto.setFormatException(e.getMessage());
                            }
                            cells.add(cellDto);
                        }
                        rowDto.setCells(cells);
                        rows.add(rowDto);
                    }
                    tableData.setRows(rows);
                    result.add(tableData);
                }
            }
        }
        PageInfo<MemTableDataVo> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(count);
        return pageInfo;
    }

    @Override
    public Map<String, Object> avgAndSum(DataFoundationTableQuery query) {
        if(!validateQueryCondition(query)){
            return null;
        }
        if (StringUtils.isEmpty(query.getColumnCode())) {
            return null;
        }
        DataFoundationTableVo tableVo = this.get(query.getTableId());
        if (tableVo == null) {
            log.error(String.format("数据底表[%s]不存在。", query.getTableName()));
            throw new ServiceException(String.format("数据底表[%s]不存在。", query.getTableName()));
        }
        String columnType = StringUtils.EMPTY;
        for (TableMetaJson curJson : tableVo.getTableMetaJson()) {
            if (!query.getColumnCode().equals(curJson.getCode())) {
                continue;
            }
            // 为兼容数据格式 优先赋值存储格式
            if (curJson.getDataFormat() != null) {
                columnType = curJson.getDataFormat().getStorageType();
            } else if (StringUtils.isNotEmpty(curJson.getNewColumnType())) {
                columnType = curJson.getNewColumnType();
            } else {
                columnType = "VARCHAR";
            }
            break;
        }

        Class cl = MemGridUtils.getColumnType(columnType);
        // 非数字类型不参与计算
        if (Integer.class != cl && Double.class != cl) {
            return null;
        }
        SourceVO dataFoundationSource = sourceService.getDataFoundationSource();//获取底表数据源
        if (Objects.isNull(dataFoundationSource)) {
            log.error(String.format("数据底表[%s]不存在。", query.getTableName()));
            throw new ServiceException(String.format("数据底表[%s]不存在。", query.getTableName()));
        }
        Source source = sourceService.getById(dataFoundationSource.getId());
        if (Objects.isNull(source)) {
            return null;
        }
        ClickHouseRepository tarRepository = (ClickHouseRepository) RepositoryFactory.getRepository(dataFoundationSource.getSourceType(), gson.fromJson(source.getSourceConfig(), SourceConfigJson.class), null);
        ConditionExample example = new ConditionExample();
        ConditionExample.Criteria criteria = example.createCriteria();

        // 转换查询条件
        tableService.parseCondition(example, criteria, BeanUtil.copyProperties(query, TableListQuery.class), tableVo.getTableMetaJson());
        List<FilterDto> requiredConditions = buildRequiredConditions(query.getFilterList());
        List<Map<String, Object>> data = tarRepository.avgAndSum(tableVo.getSqlScript(), query.getColumnCode(), example,requiredConditions);
        if (CollectionUtil.isNotEmpty(data)) {
            Map<String, Object> map = data.get(0);
            for (TableMetaJson column : tableVo.getTableMetaJson()) {
                if (query.getColumnCode().equalsIgnoreCase(column.getCode())) {
                    try {
                        map.put("a", tableService.getFormatValue(column, map.get("a")));
                        map.put("s", tableService.getFormatValue(column, map.get("s")));
                    } catch (Exception e) {
                        log.error("avgAndSum is error ::: ", e);
                    }
                    break;
                }
            }
        }
        return CollectionUtil.isEmpty(data) ? null : data.get(0);
    }

    /**
     * 校验数据底座数据源是否存在
     */
    private void validateDataFoundationExists() {
        SourceVO dataFoundationSource = sourceService.getDataFoundationSource();
        if (Objects.isNull(dataFoundationSource)) {
            log.error("数据底座数据源未配置，请先完成配置。");
            throw new ServiceException("数据底座数据源未配置，请先完成配置。");
        }
    }

    private DataFoundationTable getParentTable(Integer parentId) {
        QueryWrapper<DataFoundationTable> qw = new QueryWrapper<>();
        qw.lambda().eq(parentId != null, DataFoundationTable::getId, parentId);
        return this.getOne(qw);
    }

    public List<DataFoundationTable> updateParent(List<DataFoundationTable> tables) {
        // 优化1: 使用快速访问结构
        int maxId = tables.stream().mapToInt(DataFoundationTable::getId).max().orElse(0);
        DataFoundationTable[] tableArray = new DataFoundationTable[maxId + 1];
        List<DataFoundationTable>[] childrenArray = new List[maxId + 1];

        // 优化2: 无锁并行初始化
        tables.parallelStream().forEach(table -> {
            tableArray[table.getId()] = table;
            if (table.getParentId() != 0) {
                synchronized (childrenArray) {
                    if (childrenArray[table.getParentId()] == null) {
                        childrenArray[table.getParentId()] = new ArrayList<>(4);
                    }
                    childrenArray[table.getParentId()].add(table);
                }
            }
            if (TableType.TABLE.getCode().equals(table.getType())) {
                table.setTotalSize(table.getTotalSize() != null ? table.getTotalSize() : 0L);
            }
        });

        // 优化3: 使用数组队列
        int[] queue = new int[tables.size()];
        boolean[] processed = new boolean[maxId + 1];
        int head = 0, tail = 0;

        // 初始化叶子节点
        for (DataFoundationTable table : tables) {
            int id = table.getId();
            if (childrenArray[id] == null || childrenArray[id].isEmpty()) {
                queue[tail++] = id;
                processed[id] = true;
            }
        }

        // 优化4: 层级处理优化
        while (head < tail) {
            int currentId = queue[head++];
            DataFoundationTable current = tableArray[currentId];

            if (TableType.GROUP.getCode().equals(current.getType())) {
                List<DataFoundationTable> children = childrenArray[currentId];
                long total = (children != null) ?
                        children.stream().mapToLong(c -> c.getTotalSize() != null ? c.getTotalSize() : 0L).sum() : 0L;
                current.setTotalSize(total);
            }

            int parentId = current.getParentId();
            if (parentId != 0 && !processed[parentId]) {
                boolean allProcessed = true;
                List<DataFoundationTable> siblings = childrenArray[parentId];
                if (siblings != null) {
                    for (DataFoundationTable sibling : siblings) {
                        if (!processed[sibling.getId()]) {
                            allProcessed = false;
                            break;
                        }
                    }
                }
                if (allProcessed) {
                    queue[tail++] = parentId;
                    processed[parentId] = true;
                }
            }
        }

        return tables;
    }

    @Override
    public Map<String, Object> loadViewData(LoadViewDto dto) {
        if (Objects.isNull(dto.getId())) {
            log.error("数据底表id不能为空。");
            throw new ServiceException("数据底表id不能为空。");
        }
        DataFoundationTable table = getById(dto.getId());
        if (Objects.isNull(table)) {
            log.error("数据底表不存在。");
            throw new ServiceException("数据底表不存在。");
        }

        SourceVO dataFoundationSource = sourceService.getDataFoundationSource();
        String sql = buildRequiredConditionSql(dto, table, dataFoundationSource, table.getSqlScript());
        DataFoundationTableViewDto viewDto = new DataFoundationTableViewDto();
        viewDto.setSqlScript(sql);
        Map<String, Object> data = loadData(viewDto);

        dataFoundationSource.setSourceConfig(null);
        data.put("sqlScript", sql);
        data.put("dataSource", dataFoundationSource);//数据源
        return data;
    }

    @Transactional
    @Override
    public Boolean generateView(GenerateViewDto dto) {
        if (Objects.isNull(dto)) {
            log.error("参数不能为空。");
            throw new ServiceException("参数不能为空。");
        }
        if (StringUtils.isEmpty(dto.getViewName()) || StringUtils.isEmpty(dto.getSourceId())) {
            log.error("视图名称和数据源不能为空。");
            throw new ServiceException("视图名称和数据源不能为空。");
        }
        //判断视图名称是否已存在
        long count = viewService.count(null, dto.getViewName());
        if (count > 0) {
            log.error("视图名称已存在。");
            throw new ServiceException("视图名称已存在。");
        }
        View view = new View();
        BeanUtil.copyProperties(dto, view, "id");
        view.setStatus(BizConstants.STATUS_ENABLE);
        view.setViewScript(dto.getSqlScript());
        view.setViewMeta(JSONUtil.toJsonStr(dto.getTableMetaJson()));
        return viewService.save(view);
    }

    @Override
    public List<TableVersionSelectVo> queryDataVersion(DataFoundationDataVersionQuery query) {
        if (Objects.isNull(query.getTableId())) {
            throw new ServiceException("数据底表Id不能为空。");
        }
        DataFoundationTableVo tableVo = this.get(query.getTableId());
        if (tableVo == null) {
            throw new ServiceException("未查询到数据底表。");
        }
        if (Objects.isNull(query.getBaseDataDtStart()) || Objects.isNull(query.getBaseDataDtEnd())) {
            throw new ServiceException("起始数据日期和截止数据日期参数不能为空。");
        }
        String tableName = getTableName(tableVo.getSqlScript());//数据底表名称
        //  查询数据版本
        List<TableVersion> tableVersions = tableVersionMapper.selectList(new LambdaQueryWrapper<TableVersion>()
                .eq(TableVersion::getBackupTableName, tableName)
                .and(wrapper -> wrapper.ge(TableVersion::getBaseDataDt, query.getBaseDataDtStart())
                .le(TableVersion::getBaseDataDt, query.getBaseDataDtEnd())));

        return tableVersionService.getTableVersionSelect(tableVersions);
    }

    public String getTableStructure(Integer tableId) {
        DataFoundationTableVo tableVo = this.get(tableId);
        if (tableVo == null) {
            throw new ServiceException("未查询到数据底表");
        }
        List<TableMetaJson> tableMetaJson = tableVo.getTableMetaJson();
        if (CollectionUtil.isEmpty(tableMetaJson)) {
            throw new ServiceException("表不存在或没有字段");
        }
        if (StringUtils.isEmpty(tableVo.getSqlScript())) {
            throw new ServiceException("表不存在或没有字段");
        }
        try {
            JSONObject tableStructure = new JSONObject();
            tableStructure.put("tableName", tableVo.getTableName());
            tableStructure.put("tableCode", getTableName(tableVo.getSqlScript()));
            JSONArray columnStructures = new JSONArray();
            tableStructure.put("columns", columnStructures);
            tableMetaJson.forEach(metaJson -> {
                GroupInstanceDto fieldDataFormat = metaJson.getDataFormat();
                JSONObject columnStructure = new JSONObject();
                columnStructure.put("code", metaJson.getCode());
                columnStructure.put("name", metaJson.getName());
                columnStructure.put("type", Objects.nonNull(fieldDataFormat) ? fieldDataFormat.getStorageType() : metaJson.getNewColumnType());
                columnStructures.add(columnStructure);
            });
            return tableStructure.toJSONString();
        } catch (Exception e) {
            log.error("获取数据底表结构失败：", e);
            throw new TableStructureException("获取数据底表结构失败: " + e.getMessage());
        }
    }

    public String buildRequiredConditionSql(LoadViewDto dto, DataFoundationTable table, SourceVO dataFoundationSource, String sql) {
        if(CollectionUtil.isNotEmpty(dto.getFilterList())){
            ConditionExample example = new ConditionExample();
            ConditionExample.Criteria criteria = example.createCriteria();
            TableListQuery query = new TableListQuery();
            query.setFilterList(dto.getFilterList());
            tableService.parseCondition(example, criteria, query, gson.fromJson(table.getTableMeta(), new TypeToken<List<TableMetaJson>>() {}.getType()));
//            List<FilterDto> requiredConditions = buildRequiredConditions(query.getFilterList());

            Source source = sourceService.getById(dataFoundationSource.getId());
            ClickHouseRepository tarRepository = (ClickHouseRepository) RepositoryFactory.getRepository(dataFoundationSource.getSourceType(), gson.fromJson(source.getSourceConfig(), SourceConfigJson.class), null);
            sql = tarRepository.getSelectSql(table.getSqlScript(), example, query.getFilterList());
        }
        return sql;
    }

    public boolean validateQueryCondition(DataFoundationTableQuery query){
        if (Objects.isNull(query)) {
            log.error("查询参数不能为空");
            return false;
        }

        List<FilterDto> filterList = query.getFilterList();
        if (CollectionUtil.isEmpty(filterList)) {
            log.error("过滤条件不能为空");
            return false;
        }

        Set<String> providedFields = filterList.stream()
                .map(FilterDto::getColumnCode)
                .collect(Collectors.toSet());

        Set<String> requiredFields = new HashSet<>(Arrays.asList(
                "baseDataDtStart",
                "baseDataDtEnd",
                "dateInterval"
        ));
        requiredFields.remove("version");
        if (!providedFields.containsAll(requiredFields)) {
            Map<String, String> fieldChineseMap = new HashMap<>();
            fieldChineseMap.put("baseDataDtStart", "起始数据日期");
            fieldChineseMap.put("baseDataDtEnd", "截止数据日期");
//            fieldChineseMap.put("version", "数据版本");
            fieldChineseMap.put("dateInterval", "日期区间");

            Set<String> missingFields = requiredFields.stream()
                    .filter(field -> !providedFields.contains(field))
                    .map(fieldChineseMap::get)
                    .collect(Collectors.toSet());

            String missingFieldNames = String.join("、", missingFields);
            log.error("以下必填字段缺失: {}", missingFieldNames);
            return false;
        }
        return true;
    }

    public List<FilterDto> buildRequiredConditions(List<FilterDto> filterList){
        //过滤filterList中columnCode包含requiredFields的字段集合
        Set<String> requiredFields = new HashSet<>(Arrays.asList(
                "baseDataDtStart",
                "baseDataDtEnd",
                "version",
                "dateInterval"
        ));
        return filterList.stream()
                .filter(filter -> requiredFields.contains(filter.getColumnCode()))
                .collect(Collectors.toList());
    }

    public static String getTableName(String sql) {
        // 正则表达式匹配表名
        String regex = "(?i)from\\s+(\\w+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(sql);

        if (matcher.find()) {
            return matcher.group(1);
        } else {
            throw new ServiceException("No table name found in the SQL script.");
        }
    }


}
