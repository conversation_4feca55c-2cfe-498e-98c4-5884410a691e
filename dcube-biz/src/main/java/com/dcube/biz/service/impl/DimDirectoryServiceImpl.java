package com.dcube.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.domain.DimDirectory;
import com.dcube.biz.domain.DimInstance;
import com.dcube.biz.mapper.DimDirectoryMapper;
import com.dcube.biz.service.IDimDirectoryService;
import com.dcube.biz.service.IDimInstanceService;
import com.dcube.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

@Service
public class DimDirectoryServiceImpl extends ServiceImpl<DimDirectoryMapper, DimDirectory> implements IDimDirectoryService {

    @Autowired
    private IDimInstanceService dimInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DimDirectory saveOrUpdateExt(DimDirectory entity) {
        // 验证名称或代码
        long count = this.count(entity);
        if (count > 0) {
            log.error(String.format("维度目录名称[%s]已存在。", entity.getDimDirectoryName()));
            throw new ServiceException(String.format("维度目录名称[%s]已存在。", entity.getDimDirectoryName()));
        }

        // 兼容设置默认根根代码
        if (entity.getParentId() == null || entity.getParentId() == 0) {
            entity.setParentId(BizConstants.TREE_ROOT_ID_INT);
        }

        if (entity.getId() == null || entity.getId() <= 0) {
            this.save(entity);
        } else {
            this.updateById(entity);
        }
        return entity;
    }

    private long count(DimDirectory entity) {
        return this.count(new QueryWrapper<DimDirectory>().lambda()
                .ne(entity.getId() != null, DimDirectory::getId, entity.getId())
                .eq(DimDirectory::getDimDirectoryName, entity.getDimDirectoryName()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Integer[] ids) {
        // 删除维度实例
        List<Integer> list = Arrays.asList(ids);
        dimInstanceService.remove(Wrappers.<DimInstance>lambdaQuery().in(DimInstance::getDimDirectoryId, list));

        return this.removeByIds(list);
    }

}
