package com.dcube.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.cache.selector.SimpleReadCacheSelector;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.domain.Table;
import com.dcube.biz.dto.GroupInstanceDto;
import com.dcube.biz.dto.TableDto;
import com.dcube.biz.excel.convertor.CustomNumberConverter;
import com.dcube.biz.excel.convertor.TimestampConverter;
import com.dcube.biz.excel.style.AutoExcelStyleStrategy;
import com.dcube.biz.listener.ExcelFastImpListener;
import com.dcube.biz.listener.ExcelFastReadListener;
import com.dcube.biz.mapper.TableMapper;
import com.dcube.biz.query.TableDataExportQuery;
import com.dcube.biz.query.TableListQuery;
import com.dcube.biz.service.IExcelHandlerService;
import com.dcube.biz.service.IGroupInstanceService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.service.IViewService;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.util.TableUtils;
import com.dcube.biz.vo.ExcelImpVo;
import com.dcube.biz.vo.ProcessVo;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.config.DCubeConfig;
import com.dcube.common.constant.Constants;
import com.dcube.common.core.domain.entity.SysUser;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.core.redis.RedisCache;
import com.dcube.common.enums.ValueType;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.file.FileUploadUtils;
import com.dcube.common.utils.file.FileUtils;
import com.dcube.common.utils.file.ZipFileUtils;
import com.dcube.framework.netty.MsgService;
import com.dcube.system.service.ISysUserService;
import com.dcube.tran.store.example.ConditionExample;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.dcube.common.utils.EasyExcelUtil.enableFastRead;

/**
 * @创建人 zhouhx
 * @创建时间 2023/12/4 11:00
 * @描述
 */
@Slf4j
@Service
public class ExcelHandlerServiceImpl extends ServiceImpl<TableMapper, Table> implements IExcelHandlerService {


    private static final Set<String> filterFileSuffix = Sets.newHashSet("xlsx", "xls");

    private static final String UNZIP_FILE_PATH_SUFFIX = "/UNZIP";
    @Autowired
    public RedisCache redisCache;

    @Autowired
    public IViewService viewService;

    @Autowired
    public ITableService tableService;

    @Autowired
    private MsgService msgService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IGroupInstanceService groupInstanceService;

    public String uploadFile(MultipartFile file) {
        try {
            return FileUploadUtils.upload(DCubeConfig.getUploadPath(), file, new String[]{"zip", "rar"});
        } catch (Exception e) {
            throw new ServiceException(String.format("文件上传失败，原因：%s", ExceptionUtil.getSimpleMessage(e)));
        }
    }

    @Async
    @Override
    public void unzipFile(ExcelImpVo excelImpVo) {
        if (StringUtils.isEmpty(excelImpVo.getFileName())) {
            throw new ServiceException("文件路径不能为空。");
        }
        try {
            msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo("正在解压文件...", BigDecimal.ZERO, excelImpVo.getTimestamp(), false)), excelImpVo.getUserId());
            File unzipInputFile = new File(excelImpVo.getFileName());
            File unzipOutputDir = new File(unzipInputFile.getParent() + UNZIP_FILE_PATH_SUFFIX);
            String outputFile = ZipFileUtils.unzipAndFilterFile(unzipInputFile, unzipOutputDir, filterFileSuffix);
            //删除zip文件
            FileUtil.del(unzipInputFile);
            String unzipFilePath = outputFile;
            File[] files = FileUtils.lsFiles(unzipFilePath);
            if (ArrayUtil.isEmpty(files)) {
                msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo("文件格式有误，请检查文件。", BigDecimal.ZERO, excelImpVo.getTimestamp(), null, false)), excelImpVo.getUserId());
            }
            HashMap<Object, Object> result = new HashMap<>();
            List<Map<String, Object>> fileList = new ArrayList();
            for (File file : files) {
                Map<String, Object> data = new HashMap<>();
                data.put("fileSize", MemGridUtils.getMemSize(FileUtil.size(file)));
                data.put("filePath", file.getPath());
                data.put("fileName", file.getName());
                data.put("tableName", FileUtils.getNameNotSuffix(file.getName()));
                fileList.add(data);
            }
            result.put("fileList", fileList);
            result.put("unZipFilePath", unzipFilePath);
            msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo("解压文件成功", new BigDecimal(1), excelImpVo.getTimestamp(), JSONUtil.toJsonStr(result), false)), excelImpVo.getUserId());
        } catch (IOException e) {
            try {
                msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("文件解压失败，原因：%s", ExceptionUtil.getSimpleMessage(e)), excelImpVo.getTimestamp(), false)), excelImpVo.getUserId());
            } catch (Exception ex) {
                throw new ServiceException("文件解压失败，原因：" + ExceptionUtil.getSimpleMessage(e));
            }
        }
    }

    @Override
    public Map<String, Object> preview(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            throw new ServiceException("文件路径不能为空。");
        }
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("file path:{}", fileName);
            String suffix = FileUtil.getSuffix(fileName).toUpperCase();
            log.info("file suffix:{}", suffix);
            //开启极速读取
            SimpleReadCacheSelector simpleReadCacheSelector = enableFastRead();
            ExcelReader excelReader = EasyExcel.read(fileName).readCacheSelector(simpleReadCacheSelector).build();
            ReadSheet sheet = EasyExcel.readSheet(0).registerReadListener(new ExcelFastReadListener(result, Constants.PREVIEW_COUNT)).build();
            excelReader.read(sheet);
            excelReader.finish();
        } catch (Exception e) {
            throw new ServiceException("文件预览失败：原因：" + ExceptionUtil.getSimpleMessage(e));
        }
        return result;
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void impExcel(ExcelImpVo excelImpVo, LoginUser loginUser) {
        if (Objects.isNull(excelImpVo)) {
            throw new ServiceException("文件路径不能为空。");
        }
        SecurityUtils.setLoginUser(loginUser);
        try {
            List<GroupInstanceDto> groupInstants = groupInstanceService.queryByStorageTypes(new ValueType[]{ValueType.DATE, ValueType.VARCHAR, ValueType.INTEGER, ValueType.DOUBLE});
            msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("正在处理%s个文件...", excelImpVo.getFileNames().length), BigDecimal.ZERO, excelImpVo.getTimestamp(), false)), excelImpVo.getUserId());
            List<String> tableNames = Lists.newArrayList();
            List<TableDto> syncTables = Lists.newArrayList();
            AtomicInteger failCounter = new AtomicInteger(0);
            for (int i = 0; i < excelImpVo.getFileNames().length; i++) {
                Map<String, Object> result = new HashMap<>();
                String fileName = excelImpVo.getFileNames()[i];
                log.info("file path:{}", fileName);
                String suffix = FileUtil.getSuffix(fileName).toUpperCase();
                log.info("file suffix:{}", suffix);
                msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("正在处理第%s个文件", "" + (i + 1)), BigDecimal.valueOf(NumberUtil.div(i + 1, excelImpVo.getFileNames().length, 2)), excelImpVo.getTimestamp(), false)), excelImpVo.getUserId());
                //开启极速读取
                SimpleReadCacheSelector simpleReadCacheSelector = enableFastRead();
                ExcelReader excelReader = EasyExcel.read(fileName).readCacheSelector(simpleReadCacheSelector).build();
                String originalFileName = FileUtils.getNameNotSuffix(fileName);
                String memTableName = TableUtils.getRandomName(Constants.TABLE_PREFIX, originalFileName, Constants.TABLE_RANDOM_LENGTH);
                ReadSheet sheet = EasyExcel.readSheet(0).registerReadListener(new ExcelFastImpListener(failCounter, syncTables, tableNames, originalFileName, result, excelImpVo.getTimestamp(), redisCache, memTableName, excelImpVo.isMerge(), excelImpVo.getUserId(), excelImpVo.getParentId(), tableService, msgService, groupInstants)).build();
                excelReader.read(sheet);
                excelReader.finish();
                //删除临时文件
//                FileUtil.del(fileName);
            }
            if (failCounter.get() == 0 && CollectionUtil.isNotEmpty(syncTables)) {
                for (TableDto table : syncTables) {
                    msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("正在创建二维表：%s ...", table.getTableName()), BigDecimal.ZERO, excelImpVo.getTimestamp(), false)), excelImpVo.getUserId());
                    Table tableData = tableService.save2dTable(table);
                    if (Objects.nonNull(tableData)) {
                        //更新createBy,设置表格权限
                        SysUser user = sysUserService.selectUserById(excelImpVo.getUserId());
                        if (Objects.nonNull(user)) {
                            String username = user.getUserName();
                            Table updateEntity = new Table();
                            updateEntity.setCreateBy(username);
                            updateEntity.setUpdateBy(username);
                            updateEntity.setId(tableData.getId());
                            tableService.updateById(updateEntity);
                        }
                    }
                    msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("二维表：%s， 创建成功。", table.getTableName()), BigDecimal.ONE, excelImpVo.getTimestamp(), false)), excelImpVo.getUserId());
                }
                String tableNameList = CollectionUtil.join(tableNames, ",");
                msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("全部%s个文件已处理完成", excelImpVo.getFileNames().length), BigDecimal.ONE, excelImpVo.getTimestamp(), null, JSONUtil.toJsonStr(String.format("已创建二维表：%s", tableNameList)), false)), excelImpVo.getUserId());
            }
        } catch (Exception e) {
            log.error("二维表-Excel导入时出现异常", e);
            throw new RuntimeException(e);
        } finally {
            SecurityUtils.remove();
        }
    }

    @Override
    public void removeFiles(ExcelImpVo excelImpVo) {
        if (Objects.isNull(excelImpVo) || ArrayUtil.isEmpty(excelImpVo.getFileNames())) {
            throw new ServiceException("文件路径不能为空。");
        }
        for (String fileName : excelImpVo.getFileNames()) {
            FileUtils.deleteFile(fileName);
        }
    }

    @Async
    @Override
    public void exportData(HttpServletResponse response, TableDataExportQuery query) {
        TableVo tableVo = tableService.get(query.getTableId());
        if (Objects.isNull(tableVo)) {
            log.error(String.format("二维表[%s]不存在。", tableVo.getTableName()));
            msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("二维表[%s]不存在。", tableVo.getTableName()), query.getTimestamp(), false)), query.getUserId());
            throw new ServiceException(String.format("二维表[%s]不存在。", tableVo.getTableName()));
        }
        AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, "grid." + tableVo.getMemTableName().toLowerCase());
        ConditionExample example = new ConditionExample();

        TableListQuery queryVo = new TableListQuery();
        queryVo.setFilterList(query.getFilterList());
        queryVo.setSortList(query.getSortList());
        tableService.parseCondition(example, example.createCriteria(), queryVo, tableVo.getTableMetaJson());
        long totalCount = tarRepository.count(example);
        if (totalCount == 0) {
            log.error(String.format("二维表[%s]数据为空。", tableVo.getTableName()));
            msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("二维表[%s]数据为空。", tableVo.getTableName()), query.getTimestamp(), false)), query.getUserId());
            throw new ServiceException(String.format("二维表[%s]数据为空。", tableVo.getTableName()));
        }
        List<List<String>> headList = TableUtils.fetchHeadList(tableVo);
        //最多导出2000000数据
        totalCount = totalCount >= Constants.MAX_EXPORT_COUNT ? Constants.MAX_EXPORT_COUNT : totalCount;
        //每一个sheet存放8w条数据
        Integer sheetDataRows = Constants.ROWS_PER_SHEET;
        //每次写入的数据量5000,每页查询5000
        Integer chunkSize = Constants.CHUNK_SIZE;
        //计算需要的excel数量
        Integer sheetNum = Math.toIntExact(totalCount % sheetDataRows == 0 ? (totalCount / sheetDataRows) : (totalCount / sheetDataRows + 1));
        //计算一般情况下每一个Excel需要写入的次数(一般情况不包含最后一个excel,因为最后一个excel不确定会写入多少条数据)
        Integer oneSheetWriteCount = sheetDataRows / chunkSize;
        //计算最后一个excel需要写入的次数
        Integer lastSheetWriteCount = Math.toIntExact(totalCount % sheetDataRows == 0 ? oneSheetWriteCount : (totalCount % sheetDataRows % chunkSize == 0 ? (totalCount / sheetDataRows / chunkSize) : (totalCount / sheetDataRows / chunkSize + 1)));
        String finalFileName;
        String zipFileName = tableVo.getTableName() + ".zip";
        String targetFileName = DCubeConfig.getDownloadPath() + zipFileName;
        try {
            //必须放到循环外，否则会刷新流
            File tempFile = File.createTempFile(tableVo.getTableName(), ".xlsx");
            ExcelWriter excelWriter = EasyExcel.write(tempFile).build();
            for (int i = 0; i < sheetNum; i++) {
                WriteSheet sheet = new WriteSheet();
                sheet.setSheetName(tableVo.getTableName() + "_" + i);
                sheet.setSheetNo(i + 1);

                //循环写入次数: j的自增条件是当不是最后一个Sheet的时候写入次数为正常的每个Sheet写入的次数,如果是最后一个就需要使用计算的次数lastSheetWriteCount
                int idx = (i != sheetNum - 1 ? oneSheetWriteCount : lastSheetWriteCount);
                if (totalCount <= sheetDataRows) {
                    idx = NumberUtil.ceilDiv((int) totalCount, chunkSize);
                    oneSheetWriteCount = Math.toIntExact(totalCount / chunkSize);
                }
                for (int j = 0; j < idx; j++) {
                    List<List<Object>> dataList = new ArrayList<>();
                    //分页查询一次20w
                    example.setCurrentPage(j + 1 + oneSheetWriteCount * i);
                    example.setPageSize(chunkSize);
                    List<Map<String, Object>> data = tarRepository.listPage(example);
                    if (CollectionUtil.isNotEmpty(data)) {
                        data.forEach(map -> {
                            List<Object> cellData = new ArrayList<>();
                            map.forEach((key, value) -> {
                                cellData.add(value);
                            });
                            dataList.add(cellData);
                        });
                        WriteSheet writeSheet = EasyExcel.writerSheet(i, tableVo.getTableName() + "_" + (i + 1)).head(headList)
                                .registerConverter(new TimestampConverter())
                                .registerConverter(new CustomNumberConverter())
                                .registerWriteHandler(new AutoExcelStyleStrategy()).build();
                        //写数据
                        excelWriter.write(dataList, writeSheet);
                        BigDecimal process = NumberUtil.div(new BigDecimal(example.getSkip() >= totalCount ? totalCount : example.getSkip()), new BigDecimal(totalCount), 2, RoundingMode.HALF_UP);
                        msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo("正在导出数据...", process, query.getTimestamp(), false)), query.getUserId());
                    }
                }
            }
            excelWriter.finish();

            // 合并所有临时文件到一个压缩文件中
            File zipFile = new File(zipFileName);
            try (ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream(new FileOutputStream(zipFile))) {
                try (FileInputStream fis = new FileInputStream(tempFile)) {
                    ZipArchiveEntry entry = new ZipArchiveEntry(tempFile.getName());
                    zipOut.putArchiveEntry(entry);
                    IOUtils.copy(fis, zipOut);
                    zipOut.closeArchiveEntry();
                }
                tempFile.delete(); // 删除临时文件
            }
            File targetZipFileName = new File(targetFileName);
            FileUtil.move(zipFile, targetZipFileName, true);
            finalFileName = targetZipFileName.getAbsolutePath();

            msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("%s条数据已导出成功。", totalCount), BigDecimal.ONE, query.getTimestamp(), null, JSONUtil.toJsonStr(finalFileName), false)), query.getUserId());
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Async
    @Override
    public void exportMemData(HttpServletResponse response, TableDataExportQuery query) {
        TableVo tableVo = tableService.get(query.getTableId());
        if (Objects.isNull(tableVo)) {
            log.error(String.format("二维表[%s]不存在。", tableVo.getTableName()));
            msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("二维表[%s]不存在。", tableVo.getTableName()), query.getTimestamp(), false)), query.getUserId());
            throw new ServiceException(String.format("二维表[%s]不存在。", tableVo.getTableName()));
        }
        String finalFileName;
        String zipFileName = tableVo.getTableName() + "_" + query.getTimestamp() + ".zip";
        String targetFileName = DCubeConfig.getDownloadPath() + zipFileName;
        List<List<String>> headList = TableUtils.fetchHeadList(tableVo);
        AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, "grid." + tableVo.getMemTableName().toLowerCase());
        ConditionExample example = new ConditionExample();

        TableListQuery queryVo = new TableListQuery();
        queryVo.setFilterList(query.getFilterList());
        queryVo.setSortList(query.getSortList());
        tableService.parseCondition(example, example.createCriteria(), queryVo, tableVo.getTableMetaJson());
        long totalCount = tarRepository.count(example);
        if (totalCount == 0) {
            log.error(String.format("二维表[%s]数据为空。", tableVo.getTableName()));
            msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("二维表[%s]数据为空。", tableVo.getTableName()), query.getTimestamp(), false)), query.getUserId());
            throw new ServiceException(String.format("二维表[%s]数据为空。", tableVo.getTableName()));
        }
        //最多导出2000000数据
        totalCount = totalCount >= Constants.MAX_EXPORT_COUNT ? Constants.MAX_EXPORT_COUNT : totalCount;
        //每一个excel存放5w条数据
        Integer excelDataRows = Constants.ROWS_PER_EXCEL;
        //每次写入的数据量2000,每页查询2000
        Integer chunkSize = Constants.CHUNK_SIZE;

        //计算需要的excel数量
        Integer excelNum = Math.toIntExact(totalCount % excelDataRows == 0 ? (totalCount / excelDataRows) : (totalCount / excelDataRows + 1));
        //计算一般情况下每一个Excel需要写入的次数(一般情况不包含最后一个excel,因为最后一个excel不确定会写入多少条数据)
        Integer oneExcelWriteCount = excelDataRows / chunkSize;
        //计算最后一个excel需要写入的次数
        Integer lastExcelWriteCount = Math.toIntExact(totalCount % excelDataRows == 0 ? oneExcelWriteCount : (totalCount % excelDataRows % chunkSize == 0 ? (totalCount / excelDataRows / chunkSize) : (totalCount / excelDataRows / chunkSize + 1)));

        List<File> excelFiles = new ArrayList<>();
        try {
            for (int i = 0; i < excelNum; i++) {
                // 创建临时文件
                File excelFile = new File(DCubeConfig.getDownloadPath() + "/" + tableVo.getTableName() + "_" + (i + 1) + ".xlsx");
                File tempFile = File.createTempFile(tableVo.getTableName() + "_" + (i + 1), ".xlsx");

                //循环写入次数: j的自增条件是当不是最后一个Sheet的时候写入次数为正常的每个Sheet写入的次数,如果是最后一个就需要使用计算的次数lastSheetWriteCount
                int idx = (i != excelNum - 1 ? oneExcelWriteCount : lastExcelWriteCount);
                if (totalCount <= excelDataRows) {
                    idx = NumberUtil.ceilDiv((int) totalCount, chunkSize);
                    oneExcelWriteCount = Math.toIntExact(totalCount / chunkSize);
                }
                List<List<Object>> dataList = new ArrayList<>();
                for (int j = 0; j < idx; j++) {
                    //分页查询一次20w
                    example.setCurrentPage(j + 1 + oneExcelWriteCount * i);
                    example.setPageSize(chunkSize);
                    List<Map<String, Object>> data = tarRepository.listPage(example);
                    if (CollectionUtil.isNotEmpty(data)) {
                        data.stream().forEach(map -> {
                            List<Object> cellData = new ArrayList<>();
                            map.forEach((key, value) -> {
                                cellData.add(value);
                            });
                            dataList.add(cellData);
                        });
                        EasyExcel.write(tempFile).head(headList).registerWriteHandler(new AutoExcelStyleStrategy()).sheet(tableVo.getTableName()).doWrite(dataList);
                        BigDecimal process = NumberUtil.div(new BigDecimal(example.getSkip() >= totalCount ? totalCount : example.getSkip()), new BigDecimal(totalCount), 2, RoundingMode.HALF_UP);
                        msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo("正在导出数据...", process, query.getTimestamp(), false)), query.getUserId());
                    }
                }
                FileUtil.move(tempFile, excelFile, true);
                excelFiles.add(excelFile);

            }
            if (excelNum == 1) {
                finalFileName = excelFiles.get(0).getName();
            } else {
                // 合并所有临时文件到一个压缩文件中
                File zipFile = new File(zipFileName);
                try (ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream(new FileOutputStream(zipFile))) {
                    for (File file : excelFiles) {
                        try (FileInputStream fis = new FileInputStream(file)) {
                            ZipArchiveEntry entry = new ZipArchiveEntry(file.getName());
                            zipOut.putArchiveEntry(entry);
                            IOUtils.copy(fis, zipOut);
                            zipOut.closeArchiveEntry();
                        }
                        file.delete(); // 删除临时文件
                    }
                }
                File targetZipFileName = new File(targetFileName);
                FileUtil.move(zipFile, targetZipFileName, true);
                finalFileName = targetZipFileName.getName();
            }

        } catch (IOException e) {
            throw new ServiceException(e);
        }
        msgService.pushToUser(JSONUtil.toJsonStr(new ProcessVo(String.format("%s条数据已导出成功。", totalCount), BigDecimal.ONE, query.getTimestamp(), null, JSONUtil.toJsonStr(finalFileName), false)), query.getUserId());
    }

}
