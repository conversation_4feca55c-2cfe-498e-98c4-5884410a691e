package com.dcube.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.enums.DataFileTypeEnum;
import com.dcube.biz.constant.enums.DatafundationFlagEnum;
import com.dcube.biz.constant.enums.SourceTypeEnum;
import com.dcube.biz.domain.Source;
import com.dcube.biz.domain.SourceDetail;
import com.dcube.biz.dto.*;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.mapper.SourceMapper;
import com.dcube.biz.query.SourceListQuery;
import com.dcube.biz.service.ISourceDetailService;
import com.dcube.biz.service.ISourceService;
import com.dcube.biz.vo.SourceConfigJsonVO;
import com.dcube.biz.vo.SourceVO;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.CryptoUtil;
import com.dcube.common.utils.StringUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class SourceServiceImpl extends ServiceImpl<SourceMapper, Source> implements ISourceService {

    @Autowired
    private ISourceDetailService sourceDetailService;

    private final Gson gson = new Gson();

    @Autowired
    private CryptoUtil cryptoUtil;

    @Override
    public List<Source> queryList(SourceListQuery sourceListQuery) {
        LambdaQueryWrapper<Source> qw = new LambdaQueryWrapper<>();
        qw.select(Source::getId, Source::getSourceName, Source::getSourceType, Source::getDataFileType, Source::getDataFileDir, Source::getDirDateFormat, Source::getSplitter,
                Source::getDataFileSuffix, Source::getOkFileSuffix, Source::getFilePath, Source::getCreateBy, Source::getCreateTime,
                Source::getUpdateTime, Source::getUpdateBy);
        qw.like(StringUtils.isNotEmpty(sourceListQuery.getSourceName()), Source::getSourceName, sourceListQuery.getSourceName());
        qw.eq(StringUtils.isNotEmpty(sourceListQuery.getSourceType()), Source::getSourceType, sourceListQuery.getSourceType());
        qw.orderByDesc(Source::getCreateTime);
        List<Source> list = this.list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        if (StringUtils.isNotEmpty(sourceListQuery.getPublicKey())) {
            RSA rsa = new RSA(null, sourceListQuery.getPublicKey());
            list.forEach(source -> source.setSourceType(CryptoUtil.encrypt(rsa, source.getSourceType(), KeyType.PublicKey)));
        }

        return list;
    }

    @Override
    public List<SourceVO> queryListVO(SourceListQuery sourceListQuery) {
        LambdaQueryWrapper<Source> qw = new LambdaQueryWrapper<>();
        qw.select(Source::getId, Source::getSourceName, Source::getSourceType, Source::getDataFileType, Source::getDataFileDir, Source::getDirDateFormat, Source::getSplitter,
                Source::getDataFileSuffix, Source::getOkFileSuffix, Source::getFilePath, Source::getCreateBy, Source::getCreateTime,
                Source::getUpdateTime, Source::getUpdateBy,Source::getDataFoundationFlag);
        qw.like(StringUtils.isNotEmpty(sourceListQuery.getSourceName()), Source::getSourceName, sourceListQuery.getSourceName());
        qw.eq(StringUtils.isNotEmpty(sourceListQuery.getSourceType()), Source::getSourceType, sourceListQuery.getSourceType());
        qw.orderByDesc(Source::getCreateTime);
        List<Source> list = this.list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        if (StringUtils.isNotEmpty(sourceListQuery.getPublicKey())) {
            RSA rsa = new RSA(null, sourceListQuery.getPublicKey());
            list.forEach(source -> source.setSourceType(CryptoUtil.encrypt(rsa, source.getSourceType(), KeyType.PublicKey)));
        }

        return BeanUtil.copyToList(list, SourceVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSource(SourceAddDto source) {
        if (source == null) {
            log.error("数据源参数不能为空。");
            throw new ServiceException("数据源参数不能为空。");
        }
        long count = this.count(null, source.getSourceName());
        if (count > 0) {
            log.error(String.format("数据源名称[%s]已存在。", source.getSourceName()));
            throw new ServiceException(String.format("数据源名称[%s]已存在。", source.getSourceName()));
        }
        Source domain = BeanUtil.copyProperties(source, Source.class);
        domain.setSourceType(cryptoUtil.decryptStr(domain.getSourceType(), KeyType.PrivateKey));
        domain.setSourceConfig(gson.toJson(source.getSourceConfig()));
        return this.save(domain);
    }


    @Override
    public List<SourceDetailDto> resolving(SourceResolvingDto source) {
        if (Objects.isNull(source)) {
            log.error("数据源参数不能为空。");
            throw new ServiceException("数据源参数不能为空。");
        }
        long count = this.count(source.getId(), source.getSourceName());
        if (count > 0) {
            log.error(String.format("数据源名称[%s]已存在。", source.getSourceName()));
            throw new ServiceException(String.format("数据源名称[%s]已存在。", source.getSourceName()));
        }
        //根据数据文件类型判断
        if (DataFileTypeEnum.Text.getCode().equals(source.getDataFileType())) {
            //Text
            return sourceDetailService.fetchingTextHead(source);
        } else if (DataFileTypeEnum.Excel.getCode().equals(source.getDataFileType())) {
            //Excel
            return sourceDetailService.fetchingExcelHead(source);
        } else if (DataFileTypeEnum.Dat.getCode().equals(source.getDataFileType())) {
            //Dat文件
            return sourceDetailService.fetchingTextHead(source);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSource(SourceDto source) {
        if (Objects.isNull(source)) {
            log.error("数据源参数不能为空。");
            throw new ServiceException("数据源参数不能为空。");
        }
        long count = this.count(source.getId(), source.getSourceName());
        if (count > 0) {
            log.error(String.format("数据源名称[%s]已存在。", source.getSourceName()));
            throw new ServiceException(String.format("数据源名称[%s]已存在。", source.getSourceName()));
        }
        Source domain = BeanUtil.copyProperties(source, Source.class);
        domain.setSourceType(cryptoUtil.decryptStr(domain.getSourceType(), KeyType.PrivateKey));
        domain.setSourceConfig(gson.toJson(source.getSourceConfig()));
        sourceDetailService.saveSourceDetail(source);
        //保存主表数据
        return this.updateById(domain);
    }

    @Override
    public SourceDto getSourceById(String id) {
        Source domain = this.getById(id);
        if (domain != null) {
            SourceDto dto = new SourceDto();
            BeanUtils.copyProperties(domain, dto);
            dto.setSourceConfig(gson.fromJson(domain.getSourceConfig(), SourceConfigJson.class));
            List<SourceDetailDto> detailList = new ArrayList<>();
            List<SourceDetail> details = sourceDetailService.queryListBySourceId(id);
            if (CollectionUtils.isNotEmpty(details)) {
                for (SourceDetail detail : details) {
                    SourceDetailDto detailDto = new SourceDetailDto();
                    BeanUtils.copyProperties(detail, detailDto);
                    List<TableMetaJson> tableMeta = gson.fromJson(detail.getTableMeta(), new TypeToken<List<TableMetaJson>>() {
                    }.getType());
                    detailDto.setTableMeta(tableMeta);
                    detailDto.setFieldCount(tableMeta.size());
                    detailList.add(detailDto);
                }
                dto.setDetails(detailList);
            }
            return dto;
        }
        return null;
    }

    @Override
    public SourceVO getSourceVOById(String id, CryptoDTO cryptoDTO) {
        Source domain = this.getById(id);
        if (domain != null) {
            SourceVO dto = new SourceVO();
            BeanUtils.copyProperties(domain, dto);
            SourceConfigJson sourceConfigJson = gson.fromJson(domain.getSourceConfig(), SourceConfigJson.class);
            SourceConfigJsonVO sourceConfigJsonVO = new SourceConfigJsonVO();
            sourceConfigJsonVO.setUrl(sourceConfigJson.getUrl());
            sourceConfigJsonVO.setUsername(sourceConfigJson.getUsername());
            sourceConfigJsonVO.setPassword(sourceConfigJson.getPassword());
            sourceConfigJsonVO.setDriverClass(sourceConfigJson.getDriverClass());
            sourceConfigJsonVO.setProperties(sourceConfigJson.getProperties());
            dto.setSourceConfig(sourceConfigJsonVO);
            if (cryptoDTO != null && StringUtils.isNotEmpty(cryptoDTO.getPublicKey())) {
                RSA rsa = new RSA(null, cryptoDTO.getPublicKey());
                sourceConfigJsonVO.setUrl(CryptoUtil.encrypt(rsa, sourceConfigJsonVO.getUrl(), KeyType.PublicKey));
                sourceConfigJsonVO.setUsername(CryptoUtil.encrypt(rsa, sourceConfigJsonVO.getUsername(), KeyType.PublicKey));
                sourceConfigJsonVO.setPassword(CryptoUtil.encrypt(rsa, sourceConfigJsonVO.getPassword(), KeyType.PublicKey));
                sourceConfigJsonVO.setDriverClass(CryptoUtil.encrypt(rsa, sourceConfigJsonVO.getDriverClass(), KeyType.PublicKey));
                dto.setSourceType(CryptoUtil.encrypt(rsa, dto.getSourceType(), KeyType.PublicKey));
            }
            List<SourceDetailDto> detailList = new ArrayList<>();
            List<SourceDetail> details = sourceDetailService.queryListBySourceId(id);
            if (CollectionUtils.isNotEmpty(details)) {
                for (SourceDetail detail : details) {
                    SourceDetailDto detailDto = new SourceDetailDto();
                    BeanUtils.copyProperties(detail, detailDto);
                    List<TableMetaJson> tableMeta = gson.fromJson(detail.getTableMeta(), new TypeToken<List<TableMetaJson>>() {
                    }.getType());
                    detailDto.setTableMeta(tableMeta);
                    detailDto.setFieldCount(tableMeta.size());
                    detailList.add(detailDto);
                }
                dto.setDetails(detailList);
            }
            return dto;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdate(SourceDto dto) {
        Source domain = new Source();
        BeanUtils.copyProperties(dto, domain);
        // 验证名称
        long count = this.count(domain.getId(), domain.getSourceName());
        if (count > 0) {
            log.error(String.format("数据源名称[%s]已存在。", domain.getSourceName()));
            throw new ServiceException(String.format("数据源名称[%s]。", domain.getSourceName()));
        }

        if (CharSequenceUtil.isBlank(domain.getId())) {
            this.save(domain);
        } else {
            this.updateById(domain);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(String[] ids) {
        sourceDetailService.removeSourceDetail(Arrays.asList(ids));
        return this.removeByIds(Arrays.asList(ids));
    }

    @Override
    public List<Map<String, Object>> sourceTypeList() {
        List<Map<String, Object>> list = new ArrayList<>();
        List<SourceTypeEnum> sourceTypes = CollectionUtil.toList(SourceTypeEnum.values());
        sourceTypes.forEach(sourceType -> {
            Map<String, Object> map = new HashMap<>();
            map.put("code", sourceType.getCode());
            map.put("name", sourceType.getName());
            map.put("driverClassName", sourceType.getDriverClassName());
            map.put("url", sourceType.getUrl());
            list.add(map);
        });
        return list;
    }

    @Override
    public List<Source> queryTextSource() {
        QueryWrapper<Source> qw = new QueryWrapper<>();
        qw.lambda().eq(Source::getDataFileType, DataFileTypeEnum.Text.getCode());
        qw.lambda().orderByDesc(Source::getCreateTime);
        return this.list(qw);
    }

    /**
     * 设为底座
     * @param id
     * @return
     */
    @Override
    public Boolean setDataFoundation(String id) {
        Source domain = this.getById(id);
        if(Objects.isNull(domain)){
            log.error("数据源不存在。");
            throw new ServiceException("数据源不存在。");
        }
        this.getBaseMapper().update(new LambdaUpdateWrapper<Source>().set(Source::getDataFoundationFlag, DatafundationFlagEnum.NO.getCode()).eq(Source::getDataFoundationFlag, DatafundationFlagEnum.YES.getCode()));
        return this.getBaseMapper().update(new LambdaUpdateWrapper<Source>().set(Source::getDataFoundationFlag, DatafundationFlagEnum.YES.getCode()).eq(Source::getId, id))>0;
    }

    /**
     * 获取数据底座数据源
     * @return
     */
    @Override
    public SourceVO getDataFoundationSource() {
        Source source = getBaseMapper().selectOne(new QueryWrapper<Source>().lambda()
                .eq(Source::getDataFoundationFlag, DatafundationFlagEnum.YES.getCode()).last("limit 1"));
        if(Objects.nonNull(source)){
            SourceVO sourceVO = new SourceVO();
            BeanUtils.copyProperties(source, sourceVO);
            SourceConfigJson sourceConfigJson = gson.fromJson(source.getSourceConfig(), SourceConfigJson.class);
            SourceConfigJsonVO sourceConfigJsonVO = new SourceConfigJsonVO();
            sourceConfigJsonVO.setUrl(sourceConfigJson.getUrl());
            sourceConfigJsonVO.setUsername(sourceConfigJson.getUsername());
            sourceConfigJsonVO.setPassword(sourceConfigJson.getPassword());
            sourceConfigJsonVO.setDriverClass(sourceConfigJson.getDriverClass());
            sourceConfigJsonVO.setProperties(sourceConfigJson.getProperties());
            sourceVO.setSourceConfig(sourceConfigJsonVO);
            return sourceVO;
        }
        return null;
    }

    private long count(String id, String name) {
        return this.count(new QueryWrapper<Source>().lambda()
                .ne(CharSequenceUtil.isNotBlank(id), Source::getId, id)
                .eq(Source::getSourceName, name));
    }
}
