package com.dcube.biz.service.impl;

import com.dcube.biz.domain.Table;
import com.dcube.biz.dto.EstimationAddDto;
import com.dcube.biz.query.TableMemoryMngListQuery;
import com.dcube.biz.service.ITableMemoryMngService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.util.MemUsageUtils;
import com.dcube.biz.vo.TableMemoryMngVO;
import com.dcube.common.constant.enums.TaskStatusEnums;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.utils.StringUtils;
import com.dcube.quartz.util.TaskUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class TableMemoryMngServiceImpl implements ITableMemoryMngService {

    @Autowired
    private ITableService tableService;

    @Override
    public List<TableMemoryMngVO> list(TableMemoryMngListQuery query) {
        query.setIgnorePermission(Boolean.TRUE);
        List<Table> list = tableService.list(query);

        return list.stream()
                .map(table -> {
                    TableMemoryMngVO tableMemoryMngVO = new TableMemoryMngVO();
                    BeanUtils.copyProperties(table, tableMemoryMngVO);
                    ReportDto reportDto = TaskUtil.getTableTask(table.getId());
                    if (reportDto != null && !(reportDto.getStatus() == TaskStatusEnums.COMPLETE || reportDto.getStatus() == TaskStatusEnums.ERROR)) {
                        tableMemoryMngVO.setTaskId(reportDto.getTaskId());
                        tableMemoryMngVO.setTaskName(reportDto.getTaskName());
                        tableMemoryMngVO.setTaskType(reportDto.getType());
                        if (reportDto.getType() != null) {
                            tableMemoryMngVO.setTaskTypeName(reportDto.getType().getName());
                        }
                    }
                    EstimationAddDto dto = MemUsageUtils.get(tableMemoryMngVO.getId());
                    if (dto != null) {
                        tableMemoryMngVO.setIncreaseMemoryEstimate(MemGridUtils.getMemSize(dto.getEstimationAddAmount()));
                    }
                    return tableMemoryMngVO;
                }).filter(tableMemoryMngVO -> {
                    // 过滤是否有进程运行
                    if (Boolean.TRUE.equals(query.getTableHasTaskRun())) {
                        if (StringUtils.isEmpty(tableMemoryMngVO.getTaskId())) {
                            return false;
                        }
                    }

                    // 过滤内存占用
                    if (query.getMemorySizeGt() != null) {
                        return tableMemoryMngVO.getMemorySizeLong() != null && tableMemoryMngVO.getMemorySizeLong() > (query.getMemorySizeGt() * query.getStorageUnit().getSize());
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }
}
