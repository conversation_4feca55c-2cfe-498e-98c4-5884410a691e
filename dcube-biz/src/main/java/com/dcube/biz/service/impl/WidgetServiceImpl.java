package com.dcube.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.base.PageDto;
import com.dcube.biz.constant.enums.AggregationEnum;
import com.dcube.biz.constant.enums.OrderByEnum;
import com.dcube.biz.constant.enums.StringQueryTypeEnum;
import com.dcube.biz.domain.Dashboard;
import com.dcube.biz.domain.Table;
import com.dcube.biz.domain.Widget;
import com.dcube.biz.dto.*;
import com.dcube.biz.json.DashboardConfigJson;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.json.WidgetConfigJson;
import com.dcube.biz.mapper.WidgetMapper;
import com.dcube.biz.query.WidgetListQuery;
import com.dcube.biz.query.WidgetViewListQuery;
import com.dcube.biz.service.IDashboardService;
import com.dcube.biz.service.ITableService;
import com.dcube.biz.service.IViewService;
import com.dcube.biz.service.IWidgetService;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.vo.*;
import com.dcube.common.core.domain.entity.Permission;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import com.dcube.system.service.IPermissionService;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.dcube.common.utils.SecurityUtils.getLoginUser;

/**
 * @创建人 zhouhx
 * @创建时间 2023/11/14 10:36
 * @描述
 */
@Slf4j
@Transactional
@Service
public class WidgetServiceImpl extends ServiceImpl<WidgetMapper, Widget> implements IWidgetService {

    @Autowired
    private IViewService viewService;
    @Autowired
    private IDashboardService dashboardService;
    @Autowired
    private ITableService tableService;
    @Autowired
    private IPermissionService permissionService;

    private final Gson gson = new Gson();


    @Override
    @Transactional
    public String saveOrUpdate(WidgetDto widget) {
        if (StringUtils.isEmpty(widget.getDashboardId())) {
            log.info("dashboardId为空。");
            throw new ServiceException("请先选择仪表板。");
        }
        Widget domain = new Widget();
        BeanUtils.copyProperties(widget, domain);
        if(Objects.nonNull(widget.getConfigJson())){
            if(StringUtils.isEmpty(widget.getId())){
                widget.getConfigJson().setCode(IdUtil.fastSimpleUUID());
            }
            domain.setName(StringUtils.isNotEmpty(widget.getConfigJson().getTitle())?widget.getConfigJson().getTitle():widget.getName());
            domain.setViewId(widget.getConfigJson().getViewId());
            domain.setConfig(JSONUtil.toJsonStr(widget.getConfigJson()));
        }
        super.saveOrUpdate(domain);
        return domain.getId();
    }

    @Override
    @Transactional
    public void batchSaveOrUpdate(BatchWidgetDto batchDto) {
        if (StringUtils.isEmpty(batchDto.getDashboardId())) {
            log.info("dashboardId为空。");
            throw new ServiceException("请先选择仪表板。");
        }
        if(CollectionUtil.isNotEmpty(batchDto.getWidgets())){
            //先删除
            removeByDashboardIds(new String[]{batchDto.getDashboardId()});
            //再保存
            batchDto.getWidgets().forEach(widgetDto -> {
                Widget domain = new Widget();
                BeanUtils.copyProperties(widgetDto, domain);
                if(Objects.nonNull(widgetDto.getConfigJson())){
                    widgetDto.getConfigJson().setCode(IdUtil.fastSimpleUUID());
                    domain.setName(StringUtils.isNotEmpty(widgetDto.getConfigJson().getTitle())?widgetDto.getConfigJson().getTitle():widgetDto.getName());
                    domain.setViewId(widgetDto.getConfigJson().getViewId());
                    domain.setDashboardId(batchDto.getDashboardId());
                    domain.setConfig(JSONUtil.toJsonStr(widgetDto.getConfigJson()));
                }
                super.saveOrUpdate(domain);
            });
            //更新dashboard config
            Dashboard dashboard = new Dashboard();
            dashboard.setId(batchDto.getDashboardId());
            dashboard.setConfig(gson.toJson(batchDto.getDashboardConfig()));
            dashboardService.updateById(dashboard);//更新dashboard
        }
    }

    @Override
    public void batchSaveOrUpdateReport(BatchReportDto batchDto) {
        if (StringUtils.isEmpty(batchDto.getDashboardId())) {
            log.info("dashboardId为空。");
            throw new ServiceException("请先选择仪表板。");
        }

        //先删除
        removeByDashboardIds(new String[]{batchDto.getDashboardId()});
        if(CollectionUtil.isNotEmpty(batchDto.getWidgets())){
            //再保存
            batchDto.getWidgets().forEach(widgetDto -> {
                Widget domain = new Widget();
                BeanUtils.copyProperties(widgetDto, domain);
                domain.setDashboardId(batchDto.getDashboardId());

                if(Objects.nonNull(widgetDto.getConfigJson())){
                    widgetDto.getConfigJson().setCode(IdUtil.fastSimpleUUID());
                    domain.setName(StringUtils.isNotEmpty(widgetDto.getConfigJson().getTitle())?widgetDto.getConfigJson().getTitle():widgetDto.getName());
                    domain.setViewId(widgetDto.getConfigJson().getViewId());

                    domain.setConfig(JSONUtil.toJsonStr(widgetDto.getConfigJson()));
                }
                super.saveOrUpdate(domain);
            });
            //更新dashboard config
            Dashboard dashboard = new Dashboard();
            dashboard.setId(batchDto.getDashboardId());
            dashboard.setConfig(gson.toJson(batchDto.getConfig()));
            dashboardService.updateById(dashboard);//更新dashboard
        }
    }

    @Override
    public WidgetVo get(String id) {
        Widget widget = this.getById(id);
        WidgetVo widgetVo = new WidgetVo();
        BeanUtils.copyProperties(widget,widgetVo);
        widgetVo.setConfigJson(gson.fromJson(widget.getConfig(), WidgetConfigJson.class));
        return widgetVo;
    }

    @Override
    public Boolean removeByDashboardIds(String[] dashboardIds) {
        List<Widget> widgets = this.lambdaQuery().in(Widget::getDashboardId,dashboardIds).list();
        if(CollectionUtil.isNotEmpty(widgets)){
            return this.removeByIds(widgets);
        }
        return true;
    }

    @Override
    public Boolean removeWidget(String id) {
        return this.removeById(id);
    }

    @Override
    public List<WidgetDto> queryByDashboardId(String dashboardId) {
        List<Widget> widgets = this.lambdaQuery().eq(Widget::getDashboardId, dashboardId).list();
        List<WidgetDto> result = BeanUtil.copyToList(widgets, WidgetDto.class);
        if(CollectionUtil.isNotEmpty(result)){
            result.forEach(o -> {
                o.setConfigJson(gson.fromJson(o.getConfig(), WidgetConfigJson.class));
                o.setConfig(null);
            });
        }
        return result;
    }

    @Override
    public ViewListVo queryList(WidgetViewListQuery query) {
        return ViewListVo.builder()
                .dbView(retrieveViewsFromSource(query))
                .memView(retrieveViewsFrom2dTable(query))
                .build();
    }

    private List<TableTreeVo> retrieveViewsFrom2dTable(WidgetViewListQuery query) {
        return queryTableList(query);
    }

    private List<WidgetViewVo> retrieveViewsFromSource(WidgetViewListQuery query) {
        List<SimpleViewVo> result = viewService.queryList(query.getViewName(), query.getStatus());
        if (CollectionUtil.isNotEmpty(result)) {
            List<WidgetViewVo> views = BeanUtil.copyToList(result, WidgetViewVo.class, new CopyOptions().setIgnoreProperties("sourceConfig", "metadata"));
            result.stream().forEach(o -> {
                int index = result.indexOf(o);
                if (StringUtils.isNotBlank(o.getViewMeta())) {
                    List<TableMetaJson> metaJsons = gson.fromJson(o.getViewMeta(), new TypeToken<List<TableMetaJson>>() {
                    }.getType());
                    List<WidgetColumnDto> metadata = Lists.newArrayList();
                    metaJsons.stream().forEach(t -> {
                        WidgetColumnDto column = new WidgetColumnDto();
                        column.setName(t.getCode());
                        column.setType(t.getNewColumnType());
                        metadata.add(column);
                    });
                    views.get(index).setMetadata(metadata);
                }
            });
            return views;
        }
        return null;
    }

    @Override
    public WidgetDataVo preview(WidgetParamDto widgetParamDto) {
        String sql = "";
        if (Objects.nonNull(widgetParamDto)) {
            //非变量行纬不能为空
            if (Objects.nonNull(widgetParamDto.getHasVariable()) && !widgetParamDto.getHasVariable() && CollectionUtil.isEmpty(widgetParamDto.getDimColumns())) {
                log.info("纬度列为空。");
                throw new ServiceException("请添加一个纬度列");
            }
//            if (CollectionUtil.isEmpty(widgetParamDto.getDataColumns())) {
//                log.info("指标列为空。");
//                throw new ServiceException("请添加一个指标列");
//            }
            List<ColumnDto> dimColumns = mergeColumns(widgetParamDto.getDimColumns(),widgetParamDto.getLegendColumns());
            if(Objects.nonNull(widgetParamDto.getHasVariable()) && widgetParamDto.getHasVariable() && CollectionUtil.isEmpty(widgetParamDto.getDimColumns())){
                dimColumns = widgetParamDto.getDataColumns();
                widgetParamDto.setDataColumns(null);
            }
            SourceConfigJson sourceConfig = null;
            if (!JdbcUtils.DB_TYPE_MEMORY.equalsIgnoreCase(widgetParamDto.getSourceType())) {
                ViewVo view = viewService.get(widgetParamDto.getViewId());
                sourceConfig = view.getSourceConfig();
            }
            if (CollectionUtil.isNotEmpty(dimColumns)) {
                sql = retrieveSql(widgetParamDto.getHasVariable(),widgetParamDto.getSourceType(), widgetParamDto.getViewScript(), dimColumns, widgetParamDto.getDataColumns(),widgetParamDto.getFilterColumns());
                log.info("execute sql:{}", sql);
                AbstractRepository repository = RepositoryFactory.getRepository(widgetParamDto.getSourceType(), sourceConfig, null);
                String countSql = String.format("SELECT COUNT(1) FROM(%s) _TEMP", sql);
                long totalRows = 0;
                try {
                    totalRows = repository.executeCountSql(countSql);
                }catch (Exception e){
                    log.error("数据加载有误：{}",e.getMessage());
                    return null;
                }
                if (totalRows > 0) {
                    PageDto page = new PageDto();
                    page.setCurrentPage(widgetParamDto.getPage().getCurrentPage());
                    page.setPageSize(widgetParamDto.getPage().getPageSize());
                    List<Map<String, Object>> data = repository.listPage(sql, page);
                    if(CollectionUtil.isNotEmpty(data)){
                        //横轴纬度列排序
                        if(CollectionUtil.isNotEmpty(widgetParamDto.getDimColumns()) && StringUtils.isNotEmpty(widgetParamDto.getDimColumns().get(0).getOrderBy())){//转数字排序
                            //转数字升序
                            //转数字降序
                            appendOrder(data,widgetParamDto.getDimColumns().get(0).getName(), true, widgetParamDto.getDimColumns().get(0).getOrderBy().equals(OrderByEnum.TO_NUMBER_ASC.getCode()));
                        }
                        //图例纬度列排序
                        if(CollectionUtil.isNotEmpty(widgetParamDto.getLegendColumns()) && StringUtils.isNotEmpty(widgetParamDto.getLegendColumns().get(0).getOrderBy())){//转数字排序
                            //转数字升序
                            //转数字降序
                            appendOrder(data,widgetParamDto.getLegendColumns().get(0).getName(), true, widgetParamDto.getLegendColumns().get(0).getOrderBy().equals(OrderByEnum.TO_NUMBER_ASC.getCode()));
                        }
                        //组件中有变量时
                        if(data.size()>1 && Objects.nonNull(widgetParamDto.getHasVariable()) && widgetParamDto.getHasVariable()){
                            data = data.stream().limit(1).collect(Collectors.toList());
                        }
                    }
                    return WidgetDataVo.builder()
                            .sqlScript(sql).totalRows(totalRows)
                            .totalPage(PageUtil.totalPage(totalRows, widgetParamDto.getPage().getPageSize()))
                            .dimColumns(widgetParamDto.getDimColumns())
                            .legendColumns(widgetParamDto.getLegendColumns())
                            .dataColumns(widgetParamDto.getDataColumns())
                            .filterColumns(widgetParamDto.getFilterColumns())
                            .rows(toList(data))
                            .build();
                }
            }
        }
        return WidgetDataVo.builder().dimColumns(widgetParamDto.getDimColumns()).sqlScript(sql)
                .dataColumns(widgetParamDto.getDataColumns()).build();
    }

    public String retrieveSql(Boolean hasVariable,String sourceType, String sqlScript, List<ColumnDto> dimColumns, List<ColumnDto> dataColumns, List<ColumnDto> filterColumns) {
        StringBuffer sql = new StringBuffer();
        log.info("original SQL:{}", sqlScript);
        StringBuffer selectSql = new StringBuffer();
        StringBuffer groupBySql = new StringBuffer();
        StringBuffer whereSql = new StringBuffer();
        StringBuffer orderBySql = new StringBuffer();
        for (ColumnDto column : dimColumns) {
            Class cls = MemGridUtils.getColumnType(column.getType());
            if (StringUtils.isNotEmpty(column.getOrderBy())) {
                if(column.getOrderBy().equalsIgnoreCase(OrderByEnum.ASC.getCode()) || column.getOrderBy().equalsIgnoreCase(OrderByEnum.DESC.getCode())){
                    orderBySql.append(column.getName()).append(" ").append(column.getOrderBy()).append(",");
                }
            }
            groupBySql.append(column.getName()).append(",");
            switch (cls.getSimpleName()) {
                case "String":
                case "Date":
                    selectSql.append(column.getName()).append(",");
                    break;
                case "Integer":
                case "Double":
                    if (StringUtils.isEmpty(column.getAggregation())) {
                        selectSql.append(AggregationEnum.SUM.getCode()).append("(").append(column.getName()).append(")").append(",");
                        break;
                    } else {
                        AggregationEnum aggregationEnum = AggregationEnum.getByCode(column.getAggregation());
                        switch (aggregationEnum) {
                            case DISTINCT:
                                selectSql.append(AggregationEnum.COUNT.getCode()).append("(").append(AggregationEnum.DISTINCT.getCode()).append(" ").append(column.getName()).append(")").append(",");
                                break;
                            default:
                                selectSql.append(aggregationEnum.getCode()).append("(").append(column.getName()).append(")").append(",");
                                break;
                        }
                    }
                    break;
                default:
                    log.error("Unsupported type: " + cls.getSimpleName());
            }
        }
        if(CollectionUtil.isNotEmpty(dataColumns)){
            for (ColumnDto column : dataColumns) {
                Class cls = MemGridUtils.getColumnType(column.getType());
                if (StringUtils.isNotEmpty(column.getOrderBy())) {
//                if (!JdbcUtils.DB_TYPE_MEMORY.equalsIgnoreCase(sourceType)) {
                    if(column.getOrderBy().equalsIgnoreCase(OrderByEnum.ASC.getCode()) || column.getOrderBy().equalsIgnoreCase(OrderByEnum.DESC.getCode())){
                        orderBySql.append(column.getName()).append(" ").append(column.getOrderBy()).append(",");
                    }
//                }
                }
                switch (cls.getSimpleName()) {
                    case "String":
                    case "Date":
                        if (StringUtils.isEmpty(column.getAggregation())) {
                            selectSql.append("COUNT").append("(").append(column.getName()).append(")").append(",");
                            break;
                        } else {
                            if (AggregationEnum.DISTINCT.getCode().equals(column.getAggregation())) {
                                selectSql.append(AggregationEnum.COUNT.getCode()).append("(").append(AggregationEnum.DISTINCT.getCode()).append(" ").append(column.getName()).append(")").append(",");
                                break;
                            } else {
                                selectSql.append(AggregationEnum.COUNT.getCode()).append("(").append(column.getName()).append(")").append(",");
                                break;
                            }
                        }
                    case "Integer":
                    case "Double":
                        if (StringUtils.isEmpty(column.getAggregation())) {
                            selectSql.append(AggregationEnum.SUM.getCode()).append("(").append(column.getName()).append(") AS ").append(column.getName()).append(",");
                            break;
                        } else {
                            AggregationEnum aggregationEnum = AggregationEnum.getByCode(column.getAggregation());
                            switch (aggregationEnum) {
                                case DISTINCT:
                                    selectSql.append(AggregationEnum.COUNT.getCode()).append("(").append(AggregationEnum.DISTINCT.getCode()).append(" ").append(column.getName()).append(") AS ").append(column.getName()).append(",");
                                    break;
                                default:
                                    selectSql.append(aggregationEnum.getCode()).append("(").append(column.getName()).append(") AS ").append(column.getName()).append(",");
                                    break;
                            }
                        }
                    default:
                        log.error("Unsupported type: " + cls.getSimpleName());
                }
            }
        }
        if(Objects.nonNull(hasVariable) && hasVariable && CollectionUtil.isEmpty(filterColumns)){
            //变量把dimColumns作为查询条件
            filterColumns = dimColumns;
        }
        //where 语句
        if(CollectionUtil.isNotEmpty(filterColumns)){
            for (ColumnDto column : filterColumns) {
                Class cls = MemGridUtils.getColumnType(column.getType());
                switch (cls.getSimpleName()) {
                    case "String":
                    case "Date":
                        if (StringUtils.isNotEmpty(column.getQueryValue())) {
                            if(StringUtils.isEmpty(column.getQueryType())){//默认精确查询
                                whereSql.append(column.getName()).append(" = ").append("'").append(column.getQueryValue()).append("'").append(" AND ");
                                break;
                            }else{
                                StringQueryTypeEnum queryTypeEnum = StringQueryTypeEnum.getByCode(column.getQueryType());
                                switch (queryTypeEnum) {
                                    case LIKE:
                                        whereSql.append(column.getName()).append(" LIKE ").append("'%").append(column.getQueryValue()).append("%'").append(" AND ");
                                        break;
                                    default:
                                        whereSql.append(column.getName()).append(" = ").append("'").append(column.getQueryValue()).append("'").append(" AND ");
                                        break;
                                }
                            }
                        }
                        break;
                    case "Integer":
                    case "Double":
                        if (StringUtils.isNotEmpty(column.getQueryValue())) {
                            whereSql.append(column.getName()).append("=").append(column.getQueryValue()).append(" AND ");
                        }
                        break;
                    default:
                        log.error("Unsupported type: " + cls.getSimpleName());
                }
            }
        }
        if (selectSql.length() > 0) {
            selectSql.deleteCharAt(selectSql.length() - 1);
        }
        if (groupBySql.length() > 0) {
            groupBySql.deleteCharAt(groupBySql.length() - 1);
        }
        if (orderBySql.length() > 0) {
            orderBySql.deleteCharAt(orderBySql.length() - 1);
        }
        if (whereSql.length() > 0) {
            whereSql.delete(whereSql.length() - 4, whereSql.length());
        }
        log.info("select sql:{}", selectSql);
        log.info("groupBySql sql:{}", groupBySql);
        log.info("orderBySql sql:{}", orderBySql);
        return sql
                .append("SELECT ")
                .append(selectSql)
                .append(" FROM ")
                .append(" ( ")
                .append(sqlScript)
                .append(" ) _TMP ")
                .append(StringUtils.isNotEmpty(whereSql)?("WHERE "+whereSql):"")
                .append(" GROUP BY ")
                .append(groupBySql)
                .append(StringUtils.isNotEmpty(orderBySql) ? " ORDER BY " : "")
                .append(orderBySql)
                .toString();
    }


    private List<List<Object>> toList(List<Map<String, Object>> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            List<List<Object>> objectList = new ArrayList<>();
            for (Map<String, Object> map : list) {
                List<Object> mapEntries = new ArrayList<>();
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    mapEntries.add(entry.getValue());
                }
                objectList.add(mapEntries);
            }
            return objectList;
        }
        return null;
    }

    @Override
    public List<TableTreeVo> queryTableList(WidgetViewListQuery query) {
        List<TableTreeVo> tableTree = new ArrayList<>();
        //查询所有
        // 二维表数据权限
        List<Integer> tableIdPermissions = new ArrayList<>(Collections.singletonList(-1));
        List<Permission> userTablePermission = permissionService.selectUserTableList(getLoginUser().getUserId(), getLoginUser().getUsername());
        if (CollectionUtil.isNotEmpty(userTablePermission)) {
            tableIdPermissions.addAll(userTablePermission.stream().filter(x -> x.getDataScope() > 0).map(Permission::getTableId).collect(Collectors.toList()));
        }

        List<Table> allTables = tableService.lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getViewName()), Table::getTableName, query.getViewName())
                .eq(StringUtils.isNotEmpty(query.getStatus()), Table::getStatus, query.getStatus())
                .in(CollectionUtil.isNotEmpty(tableIdPermissions),Table::getId, tableIdPermissions)
                .orderByAsc(Table::getTableName)
                .orderByDesc(Table::getCreateTime)
                .list();
        if (CollectionUtil.isNotEmpty(allTables)) {
            tableTree = BeanUtil.copyToList(allTables, TableTreeVo.class);
        }
        List<TableTreeVo> finalTableTree = tableTree;
        List<TableTreeVo> result = tableTree.stream()
                //找出所有一级
                .filter(table -> table.getParentId() == 0)
                //设置一级子节点
                .map(table -> {
                    List<TableTreeVo> children = getChildren(table, finalTableTree);
                    table.setChildren(children.size() > 0 ? children : null);
                    return table;
                })
                //排序
                .collect(Collectors.toList());
        return CollectionUtil.isEmpty(result) ? finalTableTree : result;
    }

    private List<TableTreeVo> getChildren(TableTreeVo treeDTO, List<TableTreeVo> all) {
        return all.stream()
                .filter(item -> Objects.equals(item.getParentId(), treeDTO.getId()))
                .map(item -> {
                    List<TableTreeVo> children = getChildren(item, all);
                    if (StringUtils.isNotEmpty(item.getTableMeta())) {
                        item.setViewScript("SELECT * FROM grid." + item.getMemTableName());
                        List<TableMetaJson> metaJsons = gson.fromJson(item.getTableMeta(), new TypeToken<List<TableMetaJson>>() {
                        }.getType());
                        List<WidgetColumnDto> columns = new ArrayList<>();
                        metaJsons.stream().forEach(t -> {
                            WidgetColumnDto column = new WidgetColumnDto();
                            column.setName(t.getCode());
                            column.setDesc(t.getName());
                            column.setType(Objects.nonNull(t.getDataFormat()) ? t.getDataFormat().getStorageType() : t.getNewColumnType());
                            columns.add(column);
                        });
                        item.setSourceType(JdbcUtils.DB_TYPE_MEMORY);
                        item.setMetadata(columns);
                        item.setTableMeta(StringUtils.EMPTY);
                    }
                    item.setChildren(children.size() > 0 ? children : null);
                    return item;
                }).collect(Collectors.toList());
    }

    @Override
    public DashboardVo getWidgetsByDashboardId(WidgetListQuery query) {
        QueryWrapper<Widget> qw = new QueryWrapper<>();
        qw.lambda().eq(StringUtils.isNotEmpty(query.getDashboardId()), Widget::getDashboardId, query.getDashboardId());
        qw.lambda().eq(StringUtils.isNotEmpty(query.getName()),Widget::getName,query.getName());
        qw.lambda().orderByDesc(Widget::getSeq);
        List<Widget> widgets = this.list(qw);
        List<WidgetDto> result = Lists.newArrayList();
        if(CollectionUtil.isNotEmpty(widgets)){
            widgets.stream().forEach(o->{
                WidgetDto widget = new WidgetDto();
                BeanUtils.copyProperties(o,widget);
                widget.setConfigJson(gson.fromJson(o.getConfig(), WidgetConfigJson.class));
                result.add(widget);
            });
        }
        Dashboard dashboard = dashboardService.getById(query.getDashboardId());
        DashboardVo dashboardVo = new DashboardVo();
        dashboardVo.setWidgets(result);
        dashboardVo.setDashboardConfig(Objects.nonNull(dashboardVo)?gson.fromJson(dashboard.getConfig(), DashboardConfigJson.class):null);
        return dashboardVo;
    }

    public List<Map<String, Object>> aggregationTypeList() {
        List<Map<String,Object>> list = new ArrayList<>();
        List<AggregationEnum> aggregationTypes = CollectionUtil.toList(AggregationEnum.values());
        aggregationTypes.stream().forEach(sourceType -> {
            Map<String,Object> map = new HashMap<>();
            map.put("code",sourceType.getCode());
            map.put("name",sourceType.getName());
            list.add(map);
        });
        return list;
    }

    public static List<ColumnDto> mergeColumns(List<ColumnDto> dimColumns, List<ColumnDto> legendColumns) {
        if (legendColumns == null || legendColumns.isEmpty()) {
            return dimColumns;
        }

        // 创建一个Set来存储legendColumns的name，以便快速检查冲突
        Set<String> legendNames = legendColumns.stream()
                .map(ColumnDto::getName)
                .collect(Collectors.toSet());

        // 合并dimColumns中不与legendColumns冲突的列
        List<ColumnDto> result = dimColumns.stream()
                .filter(dimColumn -> !legendNames.contains(dimColumn.getName()))
                .collect(Collectors.toList());

        // 将legendColumns添加到结果的末尾
        result.addAll(legendColumns);

        return result;
    }

    public static void appendOrder(List<Map<String, Object>> list,String orderField,boolean isLeftExtract,boolean isAscending){
        // 前台传入的参数
        list.sort((m1, m2) -> {
                    String val1 = (String) m1.get(orderField);
                    String val2 = (String) m2.get(orderField);

                    Double num1 = extractDouble(val1, isLeftExtract);
                    Double num2 = extractDouble(val2, isLeftExtract);

                    return isAscending ? Double.compare(num1, num2) : Double.compare(num2, num1);
                });
    }

    private static Double extractDouble(String str, boolean isLeft) {
        Pattern pattern = isLeft ? Pattern.compile("^[-+]?\\d*\\.?\\d+") : Pattern.compile("([-+]?\\d*\\.?\\d+)$");
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            return Double.parseDouble(matcher.group());
        } else {
            return Double.MAX_VALUE; // 如果没有找到数字，则返回一个较大的值，保证在排序时位于末尾
        }
    }
}
