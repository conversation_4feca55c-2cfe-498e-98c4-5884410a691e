package com.dcube.biz.task;

import cn.hutool.core.bean.BeanUtil;
import com.dcube.biz.constant.enums.DataFileTypeEnum;
import com.dcube.biz.domain.Source;
import com.dcube.biz.domain.TableBackupVersion;
import com.dcube.biz.domain.TableGen;
import com.dcube.biz.domain.TableVersion;
import com.dcube.biz.dto.TableDto;
import com.dcube.biz.dto.TableGenDto;
import com.dcube.biz.query.TableGenQuery;
import com.dcube.biz.service.*;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.config.properties.GlobalExceptionProperties;
import com.dcube.common.constant.enums.TaskStatusEnums;
import com.dcube.common.enums.TableType;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.bean.BeanUtils;
import com.dcube.quartz.constants.enums.JobExecuteStatusEnums;
import com.dcube.quartz.constants.enums.JobTypeEnums;
import com.dcube.quartz.domain.CubeJob;
import com.dcube.quartz.domain.CubeJobDetail;
import com.dcube.quartz.exception.TaskExecutionException;
import com.dcube.quartz.service.ICubeJobDetailService;
import com.dcube.quartz.util.AbstractQuartzJob;
import com.dcube.quartz.util.TaskUtil;
import com.dcube.rule.grid.service.IRuleService;
import com.dcube.rule.grid.service.IRuleServiceHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 事件任务调度
 */
@Component("dCubeEventTask")
@Slf4j
@RequiredArgsConstructor
public class DCubeEventTask {
    private final ICubeJobDetailService jobDetailService;
    private final ITableService tableService;
    private final ITableVersionService tableVersionService;
    private final IRuleService ruleService;
    private final IRuleServiceHelper ruleServiceHelper;
    private final IViewService viewService;
    private final IFileTransferService fileTransferService;
    private final ITableBackupVersionService tableBackupVersionService;
    @Autowired
    private GlobalExceptionProperties globalExceptionProperties;

    public void execute() {
        CubeJob cubeJob = AbstractQuartzJob.getCubeJob();
        if (cubeJob == null) {
            return;
        }
        Integer jobId = cubeJob.getJobId();
        log.info("事件任务调度执行有参方法：{}", jobId);
        AbstractQuartzJob.updateJobExecuteStatus(jobId, JobExecuteStatusEnums.EXECUTING);
        try {
            CubeJobDetail cubeJobDetail = new CubeJobDetail();
            cubeJobDetail.setJobId(jobId);
            List<CubeJobDetail> jobDetails = jobDetailService.selectCubeJobDetailList(cubeJobDetail);
            if (CollectionUtils.isEmpty(jobDetails)) {
                return;
            }

            Map<JobTypeEnums, List<CubeJobDetail>> jobTypeListMap = jobDetails.stream().collect(Collectors.groupingBy(CubeJobDetail::getJobType));
            List<CubeJobDetail> tableJobDetails = MapUtils.getObject(jobTypeListMap, JobTypeEnums.TABLE, Collections.emptyList());
            if (CollectionUtils.isNotEmpty(tableJobDetails)) {
                for (CubeJobDetail jobDetail : tableJobDetails) {
                    Integer tableId = jobDetail.getTableId();
                    TableDto tableDto;
                    TableVo tableVo;
                    switch (jobDetail.getJobOperateType()) {
                        case LOAD_DATA_SOURCE:
                            TaskUtil.putJobCache(jobId, tableId, jobDetail.getJobOperateType());
                            try {
                                //同步Text文件到表中
                                Source source = viewService.getSourceByViewId(jobDetail.getDataSource());
                                if (Objects.nonNull(source)) {
                                    //查询数据文件类型是Text的,同步到表
                                    if (StringUtils.isNotEmpty(source.getDataFileType()) && (DataFileTypeEnum.Text.getCode().equals(source.getDataFileType()) || DataFileTypeEnum.Dat.getCode().equals(source.getDataFileType()))) {
                                        log.info("同步数据文件到表,数据视图ID:{}", jobDetail.getDataSource());
                                        fileTransferService.transfer(source.getId(), null);
                                    }
                                }
                                tableVo = ruleService.getTableVoById(tableId);
                                tableDto = new TableDto();
                                BeanUtils.copyProperties(tableVo, tableDto);
                                if (!MemGridUtils.contains(tableVo.getMemTableName())) {
                                    MemGridUtils.createTable(tableDto);
                                }
                                boolean isLoaded = MemGridUtils.getLoaded(tableVo.getMemTableName());
                                if (!isLoaded) {
                                    tableDto.setMatchFields(false);
                                    tableService.syncSyncTableData(tableDto);
                                }
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.COMPLETE, null);
                            } catch (Exception e) {
                                log.error("任务执行时出现异常：", e);
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.ERROR, globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "系统出现异常，请联系系统管理员！");
                                throw new TaskExecutionException(globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "任务执行时出现异常，请联系系统管理员！");
                            }
                            break;
                        case LOAD_DATA_VERSION:
                            TaskUtil.putJobCache(jobId, tableId, jobDetail.getJobOperateType());
                            try {
                                tableVo = ruleService.getTableVoById(tableId);
                                tableDto = new TableDto();
                                BeanUtils.copyProperties(tableVo, tableDto);
                                TableVersion tableVersion = tableVersionService.getById(jobDetail.getDataVersion());
                                if (Objects.nonNull(tableVersion)) {
                                    tableDto.setMatchFields(true);
                                    tableDto.setBackupTableName(tableVersion.getBackupTableName());
                                    tableService.syncSyncTableData(tableDto);
                                }
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.COMPLETE, null);
                            } catch (Exception e) {
                                log.error("任务执行时出现异常：", e);
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.ERROR, globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "系统出现异常，请联系系统管理员！");
                                throw new TaskExecutionException(globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "任务执行时出现异常，请联系系统管理员！");
                            }
                            break;
                        case RELEASE_MEMORY:
                            TaskUtil.putJobCache(jobId, tableId, jobDetail.getJobOperateType());
                            try {
                                tableVo = ruleService.getTableVoById(tableId);
                                if (TableType.TABLE.getCode().equals(tableVo.getType())) {
                                    MemGridUtils.release(tableVo.getMemTableName());
                                }
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.COMPLETE, null);
                            } catch (Exception e) {
                                log.error("任务执行时出现异常：", e);
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.ERROR, globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "系统出现异常，请联系系统管理员！");
                                throw new TaskExecutionException(globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "任务执行时出现异常，请联系系统管理员！");
                            }
                            break;
                        case BACKUP:
                            TaskUtil.putJobCache(jobId, tableId, jobDetail.getJobOperateType());
                            try {
                                TableBackupVersion tableBackupVersion = new TableBackupVersion();
                                tableBackupVersion.setTableId(Long.valueOf(tableId));
                                tableBackupVersion.setSuffixType(jobDetail.getSuffixType());
                                tableBackupVersion.setIsOverwrite(jobDetail.getIsOverwrite());
                                tableBackupVersion.setVersionName(jobDetail.getDataVersionName());
                                tableBackupVersionService.backup(tableBackupVersion);
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.COMPLETE, null);
                            } catch (Exception e) {
                                log.error("任务执行时出现异常：", e);
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.ERROR, globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "系统出现异常，请联系系统管理员！");
                                throw new TaskExecutionException(globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "任务执行时出现异常，请联系系统管理员！");
                            }
                            break;
                        case DELETE_BACKUP:
                            TaskUtil.putJobCache(jobId, tableId, jobDetail.getJobOperateType());
                            try {
                                TableVersion tableVersion = tableVersionService.getById(jobDetail.getDataVersion());
                                if (Objects.nonNull(tableVersion)) {
                                    tableVersionService.deleteVersions(Collections.singletonList(tableVersion));
                                }
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.COMPLETE, null);
                            } catch (Exception e) {
                                log.error("任务执行时出现异常：", e);
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.ERROR, globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "系统出现异常，请联系系统管理员！");
                                throw new TaskExecutionException(globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "任务执行时出现异常，请联系系统管理员！");
                            }
                            break;
                        case GENERATE_DATA:
                            TaskUtil.putJobCache(jobId, tableId, jobDetail.getJobOperateType());
                            try {
                                TableGenQuery tableGenQuery = new TableGenQuery();
                                tableGenQuery.setTableId(tableId);
                                tableGenQuery.setGenTypeList(Arrays.asList("GROUP_DISTINCT", "CONDITION"));
                                List<TableGen> tableGens = tableService.genInfo(tableGenQuery);
                                if (CollectionUtils.isNotEmpty(tableGens)) {
                                    tableService.gen(BeanUtil.copyToList(tableGens, TableGenDto.class));
                                }
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.COMPLETE, null);
                            } catch (Exception e) {
                                log.error("任务执行时出现异常：", e);
                                TaskUtil.updateTableTaskStatus(tableId, TaskStatusEnums.ERROR, globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "系统出现异常，请联系系统管理员！");
                                throw new TaskExecutionException(globalExceptionProperties.isShowDetail() ? e.getLocalizedMessage() : "任务执行时出现异常，请联系系统管理员！");
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
            List<CubeJobDetail> ruleJobDetails = MapUtils.getObject(jobTypeListMap, JobTypeEnums.RULE);
            if (CollectionUtils.isNotEmpty(ruleJobDetails)) {
                List<Integer> tableIds = tableJobDetails.stream().map(CubeJobDetail::getTableId).collect(Collectors.toList());
                ruleServiceHelper.executeSync(tableIds, null);
            }
        } finally {
            AbstractQuartzJob.updateJobExecuteStatus(jobId, JobExecuteStatusEnums.TO_EXECUTE);
            TaskUtil.removeJobCache(jobId);
        }
    }


}
