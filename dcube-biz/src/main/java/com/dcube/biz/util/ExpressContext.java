package com.dcube.biz.util;

import com.dcube.common.config.properties.GlobalExceptionProperties;
import com.dcube.common.utils.spring.SpringUtils;
import com.ql.util.express.IExpressContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@SuppressWarnings("serial")
public class ExpressContext extends HashMap<String, Object> implements IExpressContext<String, Object> {
    private final ApplicationContext applicationContext;

    public ExpressContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public ExpressContext(Map<String, Object> properties, ApplicationContext applicationContext) {
        super(properties);
        this.applicationContext = applicationContext;
    }

    /**
     * 抽象方法：根据名称从属性列表中提取属性值
     */
    @Override
    public Object get(Object name) {
        Object result;
        result = super.get(name);
        try {
            if (result == null && this.applicationContext != null
                    && this.applicationContext.containsBean((String) name)) {
                // 如果在Spring容器中包含bean，则返回String的Bean
                result = this.applicationContext.getBean((String) name);
            }
        } catch (Exception e) {
            log.error("根据名称从属性列表中提取属性值时出现异常：", e);
            boolean showDetail = SpringUtils.getBean(GlobalExceptionProperties.class).isShowDetail();
            if (showDetail) {
                throw new RuntimeException(e);
            } else {
                throw new RuntimeException("系统出现异常，请联系系统管理员！");
            }
        }
        return result;
    }

    @Override
    public Object put(String name, Object object) {
        return super.put(name, object);
    }
}
