package com.dcube.biz.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.dcube.biz.dto.ViewDto;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.vo.SourceConfigJsonVO;
import com.dcube.common.constant.Constants;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.CurrencyUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.grid.MemorySchema;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.calcite.jdbc.CalciteConnection;
import org.apache.calcite.schema.Schema;
import org.apache.calcite.schema.SchemaPlus;
import org.apache.ibatis.type.JdbcType;
import ru.yandex.clickhouse.response.ClickHouseResultSet;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Data
public class JdbcUtils {

    /**
     * 数据库类型
     */
    public static final String DB_TYPE_MYSQL = "MYSQL";
    public static final String DB_TYPE_ORACLE = "ORACLE";
    public static final String DB_TYPE_DM = "DMDBMS";
    public static final String DB_TYPE_CLICKHOUSE = "CLICKHOUSE";
    public static final String DB_TYPE_POSTGRESQL = "POSTGRESQL";
    public static final String DB_TYPE_MEMORY = "MEMORY";

    public static final String EXCEL = "EXCEL";

    private static ConcurrentHashMap<SourceConfigJsonVO, DataSource> dataSourceMap = new ConcurrentHashMap<>();

    public static Connection getConnection(SourceConfigJson config) {
        return getConnection(config, true);
    }

    public static Connection getConnection(SourceConfigJson config, boolean isCache) {
        SourceConfigJsonVO sourceConfigJsonVO = new SourceConfigJsonVO();
        sourceConfigJsonVO.setUrl(config.getUrl());
        sourceConfigJsonVO.setUsername(config.getUsername());
        sourceConfigJsonVO.setPassword(config.getPassword());
        sourceConfigJsonVO.setDriverClass(config.getDriverClass());
        sourceConfigJsonVO.setProperties(config.getProperties());
        return getConnection(sourceConfigJsonVO, isCache);
    }

    public static Connection getConnection(SourceConfigJsonVO config) {
        return getConnection(config, true);
    }

    public static Connection getConnection(SourceConfigJsonVO config, boolean isCache) {
        try {
            DataSource dataSource = dataSourceMap.get(config);
            if (dataSource == null) {
                dataSource = buildDataSource(config);
                if (isCache) {
                    dataSourceMap.putIfAbsent(config, dataSource);
                }
            }
            return dataSource.getConnection();
        } catch (Exception e) {
            log.error("获取数据库连接错误。", e);
            throw new ServiceException("获取数据库连接错误。");
        }
    }

    public static CalciteConnection getGridConnection() {
        try {
            System.setProperty("saffron.default.charset", "UTF-8");
            Class.forName("org.apache.calcite.jdbc.Driver");
            Properties info = new Properties();
            info.setProperty("lex", "JAVA");
            info.setProperty("timeZone", "gmt+8");
            info.setProperty("fun", "oracle");
            Connection connection = DriverManager.getConnection("jdbc:calcite:", info);
            CalciteConnection calciteConnection = connection.unwrap(CalciteConnection.class);
            SchemaPlus rootSchema = calciteConnection.getRootSchema();
            Schema schema = MemorySchema.INSTANCE;
            rootSchema.add("grid", schema);
            return calciteConnection;
        } catch (Exception e) {
            log.error("获取数据库连接错误。", e);
            throw new ServiceException("获取数据库连接错误。");
        }
    }

    private static DataSource buildDataSource(SourceConfigJsonVO config) {
        HikariDataSource hikariDataSource = new HikariDataSource();
        hikariDataSource.setJdbcUrl(config.getUrl());
        hikariDataSource.setDriverClassName(config.getDriverClass());
        hikariDataSource.setUsername(config.getUsername());
        hikariDataSource.setPassword(config.getPassword());
        hikariDataSource.setMaximumPoolSize(30);
        hikariDataSource.setMinimumIdle(0);
        hikariDataSource.setConnectionTimeout(60000);
        hikariDataSource.setIdleTimeout(600000);
        //连接在 30 分钟后自动关闭
        hikariDataSource.setMaxLifetime(1800000);
        hikariDataSource.setDataSourceProperties(getProperties(config));
        return hikariDataSource;
    }

    private static Properties getProperties(SourceConfigJsonVO config) {
        Properties pro = new Properties();
        pro.setProperty("user", config.getUsername());
        pro.setProperty("password", config.getPassword());
        // oracle
        pro.put("remarksReporting", "true");
        // mysql
        pro.put("remarks", "true");
        pro.put("useInformationSchema", "true");
        pro.put("rewriteBatchedStatements", "true");
        pro.put("reWriteBatchedInserts", "true");
        // memory
        pro.setProperty("lex", "JAVA");
        return pro;
    }

    public static boolean isConnected(Connection conn) {
        return Objects.nonNull(conn);
    }

    public static List<TableMetaJson> loadSqlFieldList(ViewDto viewDto) {
        String sql = viewDto.getViewScript();
        if (DB_TYPE_MYSQL.equals(StringUtils.toRootUpperCase(viewDto.getSourceType())) || DB_TYPE_CLICKHOUSE.equals(StringUtils.toRootUpperCase(viewDto.getSourceType()))) {
            sql = sql + " limit " + Constants.TABLE_RANDOM_LENGTH;
        }

        List<TableMetaJson> metaJsonList = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try {
            Map<String, String> columnComments = Collections.emptyMap();
            conn = getConnection(viewDto.getSourceConfig(), true);
            if (DB_TYPE_CLICKHOUSE.equals(StringUtils.toRootUpperCase(viewDto.getSourceType()))) {
                columnComments = getClickHouseColumnComments(conn, getTableNameFromSQL(sql));
            }
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            ResultSetMetaData rsMetadata = rs.getMetaData();
            int count = rsMetadata.getColumnCount();
            for (int i = 1; i < count + 1; i++) {
                String code = rsMetadata.getColumnName(i);
                String columnName = rsMetadata.getColumnLabel(i);
                String columnTypeName = rsMetadata.getColumnTypeName(i);
                if (DB_TYPE_CLICKHOUSE.equals(StringUtils.toRootUpperCase(viewDto.getSourceType()))) {
                    ClickHouseResultSet clickHouseResultSet = rs.unwrap(ClickHouseResultSet.class);
                    columnName = columnComments.getOrDefault(code, columnName);
                    JdbcType jdbcType = JdbcType.forCode(clickHouseResultSet.getMetaData().getColumnType(i));
                    columnTypeName = jdbcType.name();
                }
                if (StrUtil.isNotBlank(columnName)) {
                    TableMetaJson metaJson = new TableMetaJson();
                    metaJson.setNo(i);
                    metaJson.setCode(code);
                    metaJson.setName(columnName);
                    metaJson.setNewColumnType(columnTypeName);
                    metaJson.setOldColumnType(columnTypeName);
                    metaJsonList.add(metaJson);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            close(conn, ps, rs);
        }
        return metaJsonList;
    }

    public static void close(Connection conn) {
        try {
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void close(Connection conn, PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
            if (ps != null) {
                ps.close();
            }
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void close(Connection conn, Statement st, ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
            if (st != null) {
                st.close();
            }
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void batchInsert(SourceConfigJson config, String tableName, List<String> columns, List<Object> values) throws Exception {
        Connection conn = null;
        List<String> tempList = new ArrayList<>();
        tempList.addAll(columns);
        String cols = StringUtils.join(columns, ",");
        tempList.replaceAll(s -> "?");
        String sql = String.format("INSERT INTO %s(%s) VALUES(%s)", tableName, cols, StringUtils.join(tempList, ","));
        PreparedStatement ps = null;
        try {
            conn = getConnection(config);
            ps = conn.prepareStatement(sql);
            conn.setAutoCommit(false);
            for (int i = 0; i < values.size(); i++) {
                for (int j = 0; j < columns.size(); j++) {
                    Object val = ((LinkedHashMap) values.get(i)).get(j);
                    if (Objects.nonNull(val)) {
                        //处理千分位数字字符串
                        if (CurrencyUtils.containsThousandSeparator(val.toString())) {
                            String numberStr = CurrencyUtils.removeThousandSeparator(val.toString());
                            if (NumberUtil.isNumber(numberStr)) {
                                val = CurrencyUtils.convertToNumber(numberStr);
                            }
                        }
                        //处理百分比字符串
                        if (CurrencyUtils.containsPercentageSeparator(val.toString())) {
                            String numberStr = CurrencyUtils.removePercentageSeparator(val.toString());
                            if (NumberUtil.isNumber(numberStr)) {
                                val = CurrencyUtils.convertPercentageToNumber(numberStr);
                            }
                        }
                        ps.setObject(j + 1, val);
                    } else {
                        ps.setObject(j + 1, null);
                    }
                }
                ps.addBatch();
            }
            ps.executeBatch();
            ps.clearBatch();
            conn.commit();
        } finally {
            close(conn, ps, null);
        }
    }

    /**
     * 根据sql获取表名(只支持单表查询)
     * @param viewDto
     * @return
     */
    public static String getTableName(ViewDto viewDto) {
        String sql = viewDto.getViewScript();
        if (StringUtils.isNotEmpty(sql)) {
            sql = sql + " limit " + Constants.TABLE_RANDOM_LENGTH;
        }
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try {
            conn = getConnection(viewDto.getSourceConfig(), true);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            ResultSetMetaData rsMetadata = rs.getMetaData();
            return rsMetadata.getTableName(1);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            close(conn, ps, rs);
        }
        return null;
    }

    public static Set<String> getSQLKeywords(SourceConfigJson config) throws SQLException {
        try (Connection conn = getConnection(config)) {
            DatabaseMetaData metaData = conn.getMetaData();
            return new HashSet<>(Arrays.asList(metaData.getSQLKeywords().split(",")));
        }
    }

    /**
     * 获取ClickHouse表的字段注释
     * @param conn
     * @param tableName
     * @return
     * @throws SQLException
     */
    public static Map<String, String> getClickHouseColumnComments(Connection conn, String tableName) throws SQLException {
        Map<String, String> commentMap = new HashMap<>();
        String sql = "SELECT name, comment FROM system.columns WHERE database = currentDatabase() AND table = ?";

        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setString(1, tableName);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    String columnName = rs.getString("name");
                    String comment = rs.getString("comment");
                    commentMap.put(columnName, comment);
                }
            }
        }
        return commentMap;
    }

    /**
     * 从SQL语句中提取第一个表名（仅支持简单单表查询）
     * @param sql       SQL语句
     * @return 表名，如果无法解析则返回 null
     */
    public static String getTableNameFromSQL(String sql) {
        if (StrUtil.isBlank(sql)) {
            return null;
        }
        try {
            net.sf.jsqlparser.statement.Statement stmt = CCJSqlParserUtil.parse(sql);
            if (stmt instanceof Select) {
                Select select = (Select) stmt;
                if (select.getSelectBody() instanceof PlainSelect) {
                    PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
                    return plainSelect.getFromItem().toString();
                }
            }
        } catch (Exception e) {
            log.warn("SQL解析失败: {}", sql, e);
        }
        return null;
    }
}
