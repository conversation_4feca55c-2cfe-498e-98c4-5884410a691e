package com.dcube.biz.util;

import cn.hutool.core.convert.ConvertException;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.format.FastDateFormat;
import cn.hutool.core.util.NumberUtil;
import com.dcube.biz.constant.enums.StorageUnitEnum;
import com.dcube.biz.dto.TableDto;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.vo.MemoryVo;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StringUtils;
import com.dcube.grid.MemorySchema;
import com.dcube.grid.TableMetaData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class MemGridUtils {

    private MemGridUtils() {

    }

    public static TableMetaData getTableMetaData(String tableName) {
        return MemorySchema.getDatabase().getTableMetadataMap().get(tableName.toLowerCase());
    }

    public static void createTable(TableDto table) {
        TableMetaData tableMetaData = parseColumn(table.getMemTableName(), table.getTableMetaJson());
        MemorySchema.getDatabase().createTable(tableMetaData);
    }

    public static void addColumn(String tableName, String newColumnName, Class<?> newColumnType) {
        MemorySchema.getDatabase().addColumn(tableName, newColumnName, newColumnType);
    }

    public static void updateColumn(String tableName, List<String> newColumnNames, List<Class<?>> newColumnTypes) {
        MemorySchema.getDatabase().updateColumn(tableName, newColumnNames, newColumnTypes);
    }

    public static void deleteColumn(String tableName, String columnName) {
        MemorySchema.getDatabase().deleteColumn(tableName, columnName);
    }

    public static void deleteColumnData(String tableName, String columnName) {
        MemorySchema.getDatabase().deleteColumnData(tableName, columnName);
    }

    public static List<String> relationCheck(String tableName, String columnName, List<String> relationKeyList) {
        return MemorySchema.getDatabase().relationCheck(tableName, columnName, relationKeyList);
    }

    public static void release(String tableName) {
        MemorySchema.getDatabase().releaseTable(tableName);
    }

    public static boolean contains(String tableName) {
        return MemorySchema.getDatabase().getTableMetadataMap().containsKey(tableName.toLowerCase());
    }

    public static boolean getLoaded(String tableName) {
        return getTotalSize(tableName) > 0;
    }

    public static Long getTotalSize(String tableName) {
        Object[][] tableData = MemorySchema.getDatabase().getTableDataMap().get(tableName.toLowerCase());
        if (tableData == null) {
            return 0L;
        }
        return (long) tableData.length;
    }

    /*public static Double getMemorySizeWithUnit(String tableName, StorageUnitEnum storageUnitEnum) {
        Object[][] tableData = MemorySchema.getDatabase().getTableDataMap().get(tableName.toLowerCase());
        if (tableData == null) {
            return 0D;
        }
        Long memSize = ObjectSizeCalculator.getObjectSize(tableData);
        return NumberUtil.div(memSize, storageUnitEnum.getSize(), 2).doubleValue();
    }*/

    public static MemoryVo getMemoryInfo(String tableName) {
        Object[][] tableData = MemorySchema.getDatabase().getTableDataMap().get(tableName.toLowerCase());
        if (tableData == null) {
            return MemoryVo.builder().totalSize(0L).memorySize("0").build();
        }
        Long memSizeLong = getMemSizeLong(tableData);
        return MemoryVo.builder()
                .totalSize((long) tableData.length)
                .memorySizeLong(memSizeLong)
                .memorySize(getMemSize(memSizeLong))//TODO 优化计算内存方法
                .build();
    }

    public static String getMemorySizeWithUnit(String tableName) {
        Object[][] tableData = MemorySchema.getDatabase().getTableDataMap().get(tableName.toLowerCase());
        return getMemSize(tableData);
    }

    private static Long getMemSizeLong(Object[][] tableData) {
        if (ArrayUtils.isEmpty(tableData)) {
            return 0L;
        }
//        Long memSize = ObjectSizeCalculator.getObjectSize(tableData);
        return MemoryEstimator.estimateMultiDimensionalArrayMemoryUsage(tableData);
    }

    private static String getMemSize(Object[][] tableData) {
        return getMemSize(getMemSizeLong(tableData));
    }

    public static String getMemSize(Long memSize) {
        if (memSize >= StorageUnitEnum.GB.getSize()) {
            double size = NumberUtil.div(memSize, StorageUnitEnum.GB.getSize(), 2).doubleValue();
            return String.format("%.2f%s", size, StorageUnitEnum.GB.getName());
        } else if (memSize >= StorageUnitEnum.MB.getSize()) {
            double size = NumberUtil.div(memSize, StorageUnitEnum.MB.getSize(), 2).doubleValue();
            return String.format("%.2f%s", size, StorageUnitEnum.MB.getName());
        } else if (memSize >= StorageUnitEnum.KB.getSize()) {
            double size = NumberUtil.div(memSize, StorageUnitEnum.KB.getSize(), 2).doubleValue();
            return String.format("%.2f%s", size, StorageUnitEnum.KB.getName());
        } else {
            double size = NumberUtil.div(memSize, StorageUnitEnum.B.getSize(), 2).doubleValue();
            return String.format("%.2f%s", size, StorageUnitEnum.B.getName());
        }
    }

    public static TableMetaData parseColumn(String tableName, List<TableMetaJson> metaJsonList) {
        List<String> columnNames = new ArrayList<>();
        List<Class<?>> columnTypes = new ArrayList<>();
        for (TableMetaJson metaJson : metaJsonList) {
            // 为兼容数据格式 优先赋值存储格式
            String type = metaJson.getNewColumnType();
            if (metaJson.getDataFormat() != null) {
                type = metaJson.getDataFormat().getStorageType();
            }
            if (StringUtils.isNotEmpty(metaJson.getCode())) {
                columnNames.add(metaJson.getCode());
                columnTypes.add(getColumnType(type));
            }
        }
        return new TableMetaData(tableName.toLowerCase(), columnNames, columnTypes);
    }

    public static Class getColumnType(String type) {
        switch (type) {
            case "INT":
            case "INTEGER":
            case "BIGINT":
                return Integer.class;
            case "LONG":
            case "DECIMAL":
            case "DOUBLE":
            case "NUMERIC":
                return Double.class;
            case "STRING":
            case "CHAR":
            case "VARCHAR":
            case "TEXT":
                return String.class;
            case "DATE":
            case "DATETIME":
                return Date.class;
            default:
                log.error("Unsupported type: {}", type);
                return String.class;
        }
    }

    public static Object getValue(Class<?> clazz, String columnValue) {
        boolean isNull = StringUtils.isEmpty(columnValue);
        if (Integer.class.equals(clazz)) {
            return isNull ? null : Integer.parseInt(columnValue);
        } else if (Double.class.equals(clazz)) {
            return isNull ? null : convertToDecimal(columnValue);
        } else if (String.class.equals(clazz)) {
            return isNull ? null : columnValue;
        } else if (Date.class.equals(clazz)) {
            try {
                return isNull ? null : new java.sql.Timestamp(DateUtil.parse(columnValue).getTime());
            } catch (Exception e) {
                throw new ServiceException("日期解析失败：" + columnValue);
            }
        }
        throw new IllegalArgumentException("Unsupported type: " + clazz.getName());
    }

    public static Object getValue(Class<?> clazz, Object columnValue) {
        if (columnValue == null) {
            return null;
        }
        // double to integer
        if (Integer.class.equals(clazz) && columnValue instanceof Double) {
            return ((Double) columnValue).intValue();
        }
        // integer to double
        if (Double.class.equals(clazz) && columnValue instanceof Integer) {
            return new Double((Integer) columnValue);
        }
        // double to string
        if (String.class.equals(clazz) && columnValue instanceof Double) {
            return new BigDecimal(Double.toString((Double) columnValue)).toPlainString();
        }
        // integer to string
        if (String.class.equals(clazz) && columnValue instanceof Integer) {
            return String.valueOf(columnValue);
        }
        // date to string
        if (String.class.equals(clazz) && columnValue instanceof Date) {
            return FastDateFormat.getInstance("yyyy/MM/dd").format(columnValue);
        }
        // string to double
        if (Double.class.equals(clazz) && columnValue instanceof String) {
            return Double.valueOf(columnValue.toString());
        }
        // string to int
        if (Integer.class.equals(clazz) && columnValue instanceof String) {
            return Integer.valueOf(columnValue.toString());
        }
        // string to date
        if (Date.class.equals(clazz)) {
            if (columnValue instanceof String) {
                DateTime dateTime;
                try {
                    dateTime = DateUtil.parse((String) columnValue);
                } catch (Exception e) {
                    throw new ConvertException(e);
                }
                return new java.sql.Timestamp(dateTime.getTime());
            } else if (columnValue instanceof Date) {
                return new java.sql.Timestamp(((Date) columnValue).getTime());
            }
        }
        return getValue(clazz, columnValue.toString());
    }

    public static Object[][] getTableData(String tableName) {
        if (StringUtils.isEmpty(tableName)) {
            return null;
        }
        return MemorySchema.getDatabase().getTableData(tableName);
    }

    /**
     * 将含"%"的数字字符串转换为数字
     *
     * @param digits
     * @return
     */
    public static double convertToDecimal(String digits) {
        // 检查输入是否为空
        if (StringUtils.isEmpty(digits)) {
            throw new IllegalArgumentException("输入不能为空");
        }

        // 检查是否包含%
        if (digits.endsWith("%")) {
            // 去掉百分号
            String numberStr = digits.replace("%", "");

            // 转换为double并除以100
            double decimalValue = Double.parseDouble(numberStr) / 100;
            return decimalValue;
        } else {
            // 不包含%，直接返回原值
            String cleanedDigits = digits.replace(",", "");
            return Double.parseDouble(cleanedDigits);
        }
    }

    public static boolean columnConvertCheck(Class<?> oldColumnType, Class<?> newColumnType) {
        if (Double.class.equals(oldColumnType) && Date.class.equals(newColumnType)) {
            throw new ConvertException("小数不能转换成日期. ");
        }
        if (Integer.class.equals(oldColumnType) && Date.class.equals(newColumnType)) {
            throw new ConvertException("整数不能转换成日期. ");
        }
        if (Date.class.equals(oldColumnType) && Double.class.equals(newColumnType)) {
            throw new ConvertException("日期不能转换成小数. ");
        }
        if (Date.class.equals(oldColumnType) && Integer.class.equals(newColumnType)) {
            throw new ConvertException("日期不能转换成整数. ");
        }
        return true;
    }
}
