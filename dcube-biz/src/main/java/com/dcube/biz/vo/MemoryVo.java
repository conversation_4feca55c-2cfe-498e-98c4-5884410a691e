package com.dcube.biz.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.dcube.biz.base.BaseDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @创建人 zhouhx
 * @创建时间 2023/9/20 10:18
 * @描述
 */
@Data
@Builder
public class MemoryVo extends BaseDto {

    private Long totalSize;

    private String memorySize;

    @JsonIgnore
    @JSONField(serialize = false)
    private Long memorySizeLong;

}
