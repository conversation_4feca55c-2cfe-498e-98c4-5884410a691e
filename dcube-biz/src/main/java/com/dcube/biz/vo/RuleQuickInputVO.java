package com.dcube.biz.vo;

import com.dcube.biz.base.BaseDto;
import com.dcube.rule.grid.domain.RuleFunc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class RuleQuickInputVO extends BaseDto {
    @Schema(description = "本表")
    private List<RuleRefVO> o = Collections.emptyList();
    @Schema(description = "父表")
    private List<RuleRefVO> p = Collections.emptyList();
    @Schema(description = "子表")
    private List<RuleRefVO> c = Collections.emptyList();
    @Schema(description = "他表")
    private List<RuleRefVO> t = Collections.emptyList();
    @Schema(description = "多维表中二维的他表")
    private List<RuleRefVO> t2 = Collections.emptyList();
    @Schema(description = "函数")
    private List<RuleFunc> func = Collections.emptyList();
    @Schema(description = "if")
    private List<RuleRefVO> expressIf = Collections.emptyList();

}



