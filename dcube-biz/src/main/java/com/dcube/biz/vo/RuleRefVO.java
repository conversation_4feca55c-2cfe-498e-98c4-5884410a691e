package com.dcube.biz.vo;

import com.dcube.biz.base.BaseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class RuleRefVO extends BaseVo {
    @Schema(description = "名称")
    private String name;
    @Schema(description = "值")
    private String value;
    @Schema(description = "描述")
    private String desc;

    @Schema(description = "表Id")
    private Integer tableId;

    @Schema(description = "父Id")
    private Integer tableParentId;

    @Schema(description = "祖级列表")
    private String tableAncestors;

    @Schema(description = "子节点")
    private List<RuleRefVO> children = Collections.emptyList();
}