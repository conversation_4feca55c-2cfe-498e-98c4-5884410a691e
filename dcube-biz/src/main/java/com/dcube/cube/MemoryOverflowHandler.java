package com.dcube.cube;

public class MemoryOverflowHandler {

    public static void main(String[] args) {
        Runtime runtime = Runtime.getRuntime();

        long totalMemory = runtime.totalMemory(); // 已分配内存
        long maxMemory = runtime.maxMemory(); // 最大内存
        long freeMemory = runtime.freeMemory(); // 可用内存
        long usedMemory = totalMemory - freeMemory; // 已使用内存

        System.out.println("Total Memory (bytes): " + totalMemory);
        System.out.println("Max Memory (bytes): " + maxMemory);
        System.out.println("Free Memory (bytes): " + freeMemory);
        System.out.println("Used Memory (bytes): " + usedMemory);

        // 转换为MB
        double totalMemoryMB = bytesToMB(totalMemory);
        double maxMemoryMB = bytesToMB(maxMemory);
        double freeMemoryMB = bytesToMB(freeMemory);
        double usedMemoryMB = bytesToMB(usedMemory);

        System.out.println("Total Memory (MB): " + totalMemoryMB);
        System.out.println("Max Memory (MB): " + maxMemoryMB);
        System.out.println("Free Memory (MB): " + freeMemoryMB);
        System.out.println("Used Memory (MB): " + usedMemoryMB);
    }

    private static double bytesToMB(long bytes) {
        return bytes / 1024.0 / 1024.0;
    }
}
