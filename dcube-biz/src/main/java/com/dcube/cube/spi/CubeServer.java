package com.dcube.cube.spi;

import com.dcube.common.exception.ServiceException;
import com.dcube.cube.core.FactTable;
import com.dcube.cube.math.DoubleDouble;
import lombok.extern.slf4j.Slf4j;
import org.roaringbitmap.RoaringBitmap;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class CubeServer implements Aggregations {

    /**
     * Take care this value, make sure do not has this values in data-set.
     */
    public static final int DUMMY_FILTER_DIM = -999999999;

    public FactTable factTable;

    private volatile boolean parallelMode = true;

    public CubeServer(FactTable factTable) {
        super();
        this.factTable = factTable;
    }

    public void setParallelMode(boolean parallelMode) {
        this.parallelMode = parallelMode;
        if (log.isInfoEnabled()) {
            log.info("Set stream's mode from {} to {} of {}", this.parallelMode, parallelMode, factTable.meta.name);
        }
    }

    public void merge(CubeServer merge) {
        if (merge == null) {
            if (log.isInfoEnabled()) {
                log.info("Do nothing when merge object is null");
            }
            return;
        }
        this.factTable.merge(merge.factTable);
    }

    // ---------------------------- Aggregation API ----------------------------

    public FactTable.Record hitRecordWithInstance(Map<String, List<String>> filterDims) {
        if (filterDims == null) {
            filterDims = new HashMap<>(0);
        }

        Map<String, Object> data = factTable.getData();
        @SuppressWarnings("unchecked")
        Map<String, RoaringBitmap> bitmapIndex = (Map<String, RoaringBitmap>) data.get("bitmapIndex");

        RoaringBitmap ands = null;
        for (Map.Entry<String, List<String>> entry : filterDims.entrySet()) {
            RoaringBitmap ors = new RoaringBitmap();
            for (String v : entry.getValue()) {
                RoaringBitmap o = bitmapIndex.get(entry.getKey() + ":" + v);
                if (o != null) {
                    ors.or(o);
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("Can not find bitmap index for " + entry.getKey() + ":" + v);
                    }
                }
            }
            if (ands == null) {
                ands = ors;
            } else {
                ands.and(ors);
            }
        }
        if (ands != null && ands.getCardinality() > 0) {
            if (log.isDebugEnabled()) {
                log.debug("Filter record IDs count {}", ands.getCardinality());
            }
            if (ands.getCardinality() > 1) {
                log.error("condition match many record.");
                throw new ServiceException("condition match many record.");
            }
            return this.factTable.getRecords().get(ands.getIntIterator().next());
        }
        return null;
    }

    public List<FactTable.Record> filterRecord(Map<String, List<String>> filterDims) {
        if (filterDims == null) {
            filterDims = new HashMap<>(0);
        }

        Map<String, Object> data = factTable.getData();
        @SuppressWarnings("unchecked")
        Map<Integer, FactTable.Record> records = (Map<Integer, FactTable.Record>) data.get("records");
        @SuppressWarnings("unchecked")
        Map<String, RoaringBitmap> bitmapIndex = (Map<String, RoaringBitmap>) data.get("bitmapIndex");

        Stream<Map.Entry<Integer, FactTable.Record>> stream = parallelMode ? records.entrySet().parallelStream() : records.entrySet().stream();

        RoaringBitmap ands = null;
        for (Map.Entry<String, List<String>> entry : filterDims.entrySet()) {
            RoaringBitmap ors = new RoaringBitmap();
            for (String v : entry.getValue()) {
                RoaringBitmap o = bitmapIndex.get(entry.getKey() + ":" + v);
                if (o != null) {
                    ors.or(o);
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("Can not find bitmap index for " + entry.getKey() + ":" + v);
                    }
                }
            }
            if (ands == null) {
                ands = ors;
            } else {
                ands.and(ors);
            }
        }
        if (ands != null) {
            final RoaringBitmap m = ands;
            if (log.isDebugEnabled()) {
                log.debug("Filter record IDs count {}", ands.getCardinality());
            }
            return stream.filter(t -> m.contains(t.getKey()))
                    .map(Map.Entry::getValue)
                    .collect(Collectors.toList());
        }
        return stream.map(Map.Entry::getValue).collect(Collectors.toList());
    }

    private Stream<Map.Entry<Integer, FactTable.Record>> filter(Map<String, List<String>> filterDims) {
        if (filterDims == null) {
            filterDims = new HashMap<>(0);
        }

        Map<String, Object> data = factTable.getData();
        @SuppressWarnings("unchecked")
        Map<Integer, FactTable.Record> records = (Map<Integer, FactTable.Record>) data.get("records");
        @SuppressWarnings("unchecked")
        Map<String, RoaringBitmap> bitmapIndex = (Map<String, RoaringBitmap>) data.get("bitmapIndex");

        Stream<Map.Entry<Integer, FactTable.Record>> stream = parallelMode ? records.entrySet().parallelStream() : records.entrySet().stream();

        RoaringBitmap ands = null;
        for (Map.Entry<String, List<String>> entry : filterDims.entrySet()) {
            RoaringBitmap ors = new RoaringBitmap();
            for (String v : entry.getValue()) {
                RoaringBitmap o = bitmapIndex.get(entry.getKey() + ":" + v);
                if (o != null) {
                    ors.or(o);
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("Can not find bitmap index for " + entry.getKey() + ":" + v);
                    }
                }
            }
            if (ands == null) {
                ands = ors;
            } else {
                ands.and(ors);
            }
        }
        if (ands != null) {
            final RoaringBitmap m = ands;
            if (log.isDebugEnabled()) {
                log.debug("Filter record IDs count {}", ands.getCardinality());
            }
            return stream.filter(t -> m.contains(t.getKey()));
        }

        Predicate<Map.Entry<Integer, FactTable.Record>> andFilter = i -> i.getKey() != DUMMY_FILTER_DIM;
        return stream.filter(andFilter);
    }

    @Override
    public BigDecimal sum(String indName) {

        // Delegate to overload method
        return sum(indName, null);
    }

    @Override
    public BigDecimal sum(String indName, Map<String, List<String>> filterDims) {

        long enterTime = System.currentTimeMillis();

        Stream<Map.Entry<Integer, FactTable.Record>> stream = filter(filterDims);
        if (log.isDebugEnabled()) {
            log.debug("Prepare predicate using {} ms.", System.currentTimeMillis() - enterTime);
        }

        DoubleDouble sum = stream.map(t -> t.getValue().getInd(indName))
                .reduce(new DoubleDouble(), DoubleDouble::add);

        enterTime = System.currentTimeMillis() - enterTime;
        if (log.isDebugEnabled()) {
            log.debug("Sum {} filter {} result {} using {} ms.", indName, filterDims, sum, enterTime);
        }

        return new BigDecimal(sum.toSciNotation()).setScale(IND_SCALE, RoundingMode.HALF_UP);
    }

    @Override
    public Map<String, BigDecimal> sum(String indName, String groupByDimName, Map<String, List<String>> filterDims) {

        long enterTime = System.currentTimeMillis();
        Stream<Map.Entry<Integer, FactTable.Record>> stream = filter(filterDims);

        Map<String, BigDecimal> group = new HashMap<>();
        stream.collect(Collectors.groupingBy(p -> p.getValue().getDim(groupByDimName), Collectors.reducing(new DoubleDouble(),
                        t -> t.getValue().getInd(indName), DoubleDouble::add)))
                .forEach((k, v) -> group.put(k, new BigDecimal(v.toSciNotation()).setScale(IND_SCALE, RoundingMode.HALF_UP)));

        enterTime = System.currentTimeMillis() - enterTime;
        if (log.isDebugEnabled()) {
            log.debug("Group by {} sum {} filter {} result {} using {} ms.", groupByDimName, indName, filterDims, group, enterTime);
        }
        return group;
    }

    public Map<String, BigDecimal> sum(String indName, List<String> groupByDimNameList, Map<String, List<String>> filterDims) {

        long enterTime = System.currentTimeMillis();
        Stream<Map.Entry<Integer, FactTable.Record>> stream = filter(filterDims);

        Map<String, BigDecimal> group = new HashMap<>();
        stream.collect(Collectors.groupingBy(p -> groupByDimNameList.stream().map(x -> p.getValue().getDim(x)).collect(Collectors.joining("&")), Collectors.reducing(new DoubleDouble(),
                        t -> t.getValue().getInd(indName), DoubleDouble::add)))
                .forEach((k, v) -> group.put(k, new BigDecimal(v.toSciNotation()).setScale(IND_SCALE, RoundingMode.HALF_UP)));

        enterTime = System.currentTimeMillis() - enterTime;
        if (log.isDebugEnabled()) {
            log.debug("Group by {} sum {} filter {} result {} using {} ms.", groupByDimNameList, indName, filterDims, group, enterTime);
        }
        return group;
    }

    public Map<String, BigDecimal[]> multiSum(List<String> indNameList, List<String> groupByDimNameList, Map<String, List<String>> filterDims) {

        long enterTime = System.currentTimeMillis();
        Stream<Map.Entry<Integer, FactTable.Record>> stream = filter(filterDims);

        Map<String, BigDecimal[]> group = new LinkedHashMap<>();
        stream.collect(Collectors.groupingBy(p -> groupByDimNameList.stream().map(x -> p.getValue().getDim(x)).collect(Collectors.joining("&")), Collectors.reducing(new DoubleDouble[]{},
                t -> {
                    DoubleDouble[] dd = new DoubleDouble[indNameList.size()];
                    for (int i = 0; i < indNameList.size(); i++) {
                        dd[i] = t.getValue().getInd(indNameList.get(i));
                    }
                    return dd;
                }, (x, y) -> {
                    if (x == null || x.length == 0) {
                        x = new DoubleDouble[indNameList.size()];
                        for (int i = 0; i < indNameList.size(); i++) {
                            x[i] = new DoubleDouble(0);
                        }
                    }
                    DoubleDouble[] dd = new DoubleDouble[indNameList.size()];
                    for (int i = 0; i < indNameList.size(); i++) {
                        dd[i] = x[i].add(y[i]);
                    }
                    return dd;
                }))).entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(e -> group.put(e.getKey(), dd2BigDecimal(e.getValue())));

        enterTime = System.currentTimeMillis() - enterTime;
        if (log.isDebugEnabled()) {
            log.debug("Group by {} sum {} filter {} result {} using {} ms.", groupByDimNameList, indNameList, filterDims, group, enterTime);
        }

        group.entrySet().stream().peek(x -> {
            if (log.isDebugEnabled()) {
                log.debug("key:{}", x.getKey());
            }
            Arrays.stream(x.getValue()).peek(a -> {
                if (log.isDebugEnabled()) {
                    log.debug("value:{}", a.doubleValue());
                }
            }).collect(Collectors.toList());
        }).collect(Collectors.toList());
        return group;
    }

    @Override
    public long count(String indName) {

        // Delegate to overload method
        return count(indName, null);
    }

    @Override
    public long count(String indName, Map<String, List<String>> filterDims) {

        long enterTime = System.currentTimeMillis();

        Stream<Map.Entry<Integer, FactTable.Record>> stream = filter(filterDims);
        if (log.isDebugEnabled()) {
            log.debug("Prepare predicate using {} ms.", System.currentTimeMillis() - enterTime);
        }
        long count = stream.count();

        enterTime = System.currentTimeMillis() - enterTime;
        if (log.isDebugEnabled()) {
            log.debug("Count {} filter {} result {} using {} ms.", indName, filterDims, count, enterTime);
        }

        return count;
    }

    @Override
    public Map<String, Long> count(String indName, String groupByDimName, Map<String, List<String>> filterDims) {

        long enterTime = System.currentTimeMillis();
        Stream<Map.Entry<Integer, FactTable.Record>> stream = filter(filterDims);

        Map<String, Long> group = stream.collect(Collectors.groupingBy(p -> p.getValue().getDim(groupByDimName), Collectors.counting()));

        enterTime = System.currentTimeMillis() - enterTime;
        if (log.isDebugEnabled()) {
            log.debug("Group by {} count {} filter {} result {} using {} ms.", groupByDimName, indName, filterDims, group, enterTime);
        }
        return group;
    }

    @Override
    public String toString() {
        return "CubeServer [factTable=" + factTable + "]";
    }

    private BigDecimal[] dd2BigDecimal(DoubleDouble[] t) {
        BigDecimal[] r = new BigDecimal[t.length];
        for (int i = 0; i < t.length; i++) {
            r[i] = new BigDecimal(t[i].toSciNotation()).setScale(IND_SCALE, RoundingMode.HALF_UP);
        }
        return r;
    }
}
