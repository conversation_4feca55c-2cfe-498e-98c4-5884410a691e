package com.dcube.grid;

import org.apache.calcite.jdbc.CalciteConnection;
import org.apache.calcite.schema.SchemaPlus;

import java.sql.*;
import java.util.Arrays;
import java.util.Properties;

public class Main {
    public static void main(String[] args) {
        System.setProperty("saffron.default.charset", "UTF-8");
        // 定义表结构
        TableMetaData employeeMetadata = new TableMetaData("employee", Arrays.asList("id", "name", "age", "salary"), Arrays.asList(Integer.class, String.class, Integer.class, Double.class));

        // 创建内存数据库
        MemoryDatabase database = MemorySchema.getDatabase();
        database.createTable(employeeMetadata);

        // 插入数据
        database.insertData("employee", new Object[]{1, "张三", 25, 50000.0});
        database.insertData("employee", new Object[]{2, "Mary", 30, 60000.0});
        database.insertData("employee", new Object[]{null, "李四",null,null});

        // 查询数据
        try {
            Properties info = new Properties();
            info.setProperty("lex", "JAVA");
            Connection connection = DriverManager.getConnection("jdbc:calcite:", info);
            CalciteConnection calciteConnection = connection.unwrap(CalciteConnection.class);
            SchemaPlus rootSchema = calciteConnection.getRootSchema();
            rootSchema.add("memory", MemorySchema.INSTANCE);
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery("SELECT * FROM memory.employee");
            while (resultSet.next()) {
                int id = resultSet.getInt("id");
                String name = resultSet.getString("name");
                int age = resultSet.getInt("age");
                double salary = resultSet.getDouble("salary");
                System.out.println(id + "\t" + name + "\t" + age + "\t" + salary);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
