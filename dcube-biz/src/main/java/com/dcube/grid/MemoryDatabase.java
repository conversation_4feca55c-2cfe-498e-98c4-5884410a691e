package com.dcube.grid;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.ConvertException;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.common.exception.ServiceException;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MemoryDatabase {

    protected MemoryDatabase() {

    }

    @Getter
    private final Map<String, TableMetaData> tableMetadataMap = new HashMap<>();
    @Getter
    private final Map<String, Object[][]> tableDataMap = new HashMap<>();

    public void createTable(TableMetaData tableMetadata) {
        String tableName = tableMetadata.getTableName().toLowerCase();
        tableMetadataMap.put(tableName, tableMetadata);
        tableDataMap.put(tableName, new Object[0][]);
    }

    public void releaseTable(String tableName) {
        tableName = tableName.toLowerCase();
        tableMetadataMap.remove(tableName);
        tableDataMap.remove(tableName);
    }

    public void addColumn(String tableName, String newColumnName, Class<?> newColumnType) {
        tableName = tableName.toLowerCase();
        TableMetaData oldMetadata = tableMetadataMap.get(tableName);
        if (oldMetadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        oldMetadata.getColumnNames().add(newColumnName);
        oldMetadata.getColumnTypes().add(newColumnType);

        Object[][] tableData = tableDataMap.get(tableName);
        for (int rowIndex = 0, len = tableData.length; rowIndex < len; rowIndex++) {
            Object[] row = tableData[rowIndex];
            int rowLen = row.length;
            Object[] newRow = Arrays.copyOf(row, rowLen + 1);
            newRow[rowLen] = null;
            tableData[rowIndex] = newRow;
        }
    }

    public void updateColumn(String tableName, List<String> newColumnNames, List<Class<?>> newColumnTypes) {
        tableName = tableName.toLowerCase();
        TableMetaData oldMetadata = tableMetadataMap.get(tableName);
        if (oldMetadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        List<Integer> updateIndexList = new ArrayList<>();
        List<Class<?>> updateType = new ArrayList<>();
        for (int i = 0, iSize = oldMetadata.getColumnNames().size(); i < iSize; i++) {
            for (int j = 0, jSize = newColumnNames.size(); j < jSize; j++) {
                if (oldMetadata.getColumnNames().get(i).equals(newColumnNames.get(j)) && oldMetadata.getColumnTypes().get(i) != newColumnTypes.get(j)) {
                    // 增强类型转换验证
                    MemGridUtils.columnConvertCheck(oldMetadata.getColumnTypes().get(i), newColumnTypes.get(j));
                    // 记录列下标
                    updateIndexList.add(i);
                    // 记录列类型
                    updateType.add(newColumnTypes.get(j));
                    break;
                }
            }
        }
        if (CollectionUtil.isEmpty(updateIndexList)) {
            return;
        }
        // 遍历数据表增强类型转换验证
        Object[][] tableData = tableDataMap.get(tableName);
        for (Object[] row : tableData) {
            for (int k = 0, size = updateIndexList.size(); k < size; k++) {
                try {
                    MemGridUtils.getValue(updateType.get(k), row[updateIndexList.get(k)]);
                } catch (Exception e) {
                    throw new ConvertException("数据表行ID号[{}]数据类型转换错误. 错误信息[{}]. ", row[0], e.getMessage());
                }
            }
        }
        // 提交更新列类型
        for (int i = 0, size = updateIndexList.size(); i < size; i++) {
            oldMetadata.getColumnTypes().set(updateIndexList.get(i), updateType.get(i));
        }
        // 遍历转换数据
        for (Object[] row : tableData) {
            for (int k = 0, size = updateIndexList.size(); k < size; k++) {
                row[updateIndexList.get(k)] = MemGridUtils.getValue(updateType.get(k), row[updateIndexList.get(k)]);
            }
        }
    }

    public void deleteColumn(String tableName, String columnName) {
        tableName = tableName.toLowerCase();
        TableMetaData oldMetadata = tableMetadataMap.get(tableName);
        if (oldMetadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        int deleteIndex = -1;
        for (int i = 0, size = oldMetadata.getColumnNames().size(); i < size; i++) {
            if (oldMetadata.getColumnNames().get(i).equals(columnName)) {
                deleteIndex = i;
                break;
            }
        }
        if (deleteIndex == -1) {
            throw new ServiceException("列【" + columnName + "】未找到");
        }
        oldMetadata.getColumnNames().remove(deleteIndex);
        oldMetadata.getColumnTypes().remove(deleteIndex);

        Object[][] tableData = tableDataMap.get(tableName);
        for (int rowIndex = 0, len = tableData.length; rowIndex < len; rowIndex++) {
            Object[] row = tableData[rowIndex];
            int rowLen = row.length;
            Object[] newRow = new Object[rowLen - 1];
            int j = 0;
            for (int i = 0; i < rowLen; i++) {
                if (i != deleteIndex) {
                    newRow[j] = row[i];
                    j++;
                }
            }
            tableData[rowIndex] = newRow;
        }
    }

    /**
     * 删除列数据
     *
     * @param tableName
     * @param columnName
     */
    public void deleteColumnData(String tableName, String columnName) {
        tableName = tableName.toLowerCase();
        TableMetaData oldMetadata = tableMetadataMap.get(tableName);
        if (oldMetadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        int deleteIndex = -1;
        for (int i = 0, size = oldMetadata.getColumnNames().size(); i < size; i++) {
            if (oldMetadata.getColumnNames().get(i).equals(columnName)) {
                deleteIndex = i;
                break;
            }
        }
        if (deleteIndex == -1) {
            throw new ServiceException("列[" + columnName + "]未找到！");
        }

        Object[][] tableData = tableDataMap.get(tableName);
        for (Object[] row : tableData) {
            row[deleteIndex] = null;
        }
    }

    public List<String> relationCheck(String tableName, String columnName, List<String> relationKeyList) {
        tableName = tableName.toLowerCase();
        TableMetaData oldMetadata = tableMetadataMap.get(tableName);
        if (oldMetadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        int checkIndex = -1;
        for (int i = 0, size = oldMetadata.getColumnNames().size(); i < size; i++) {
            if (oldMetadata.getColumnNames().get(i).equals(columnName)) {
                checkIndex = i;
                break;
            }
        }

        List<String> unmatchKeyList = new ArrayList<>();
        Set<String> relationKeySet = new HashSet<>(relationKeyList);
        Object[][] tableData = tableDataMap.get(tableName);
        for (Object[] row : tableData) {
            if (!relationKeySet.contains(String.valueOf(row[checkIndex]))) {
                unmatchKeyList.add(String.valueOf(row[checkIndex]));
            }
        }
        return unmatchKeyList;
    }

    public void insertData(String tableName, Object[] rowData) {
        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        if (rowData.length != metadata.getColumnNames().size()) {
            throw new IllegalArgumentException("Number of columns must match");
        }
        Object[][] tableData = tableDataMap.get(tableName);
        Object[][] newData = Arrays.copyOf(tableData, tableData.length + 1);
        newData[newData.length - 1] = rowData;
        tableDataMap.put(tableName, newData);
    }

    public void reData(String tableName, Object[][] rowData) {
        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        tableDataMap.put(tableName, rowData);
    }

    public void reData(String tableName, List<Object[]> rowData) {
        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        tableDataMap.put(tableName, rowData.toArray(new Object[0][]));
    }

    public void insertData(String tableName, List<Object[]> rowDataList) {
        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        if (rowDataList == null || rowDataList.get(0).length != metadata.getColumnNames().size()) {
            throw new IllegalArgumentException("Number of columns must match");
        }
        Object[][] tableData = tableDataMap.get(tableName);
        Object[][] newData = Arrays.copyOf(tableData, tableData.length + rowDataList.size());
        int index = 0;
        for (Object[] row : rowDataList) {
            newData[tableData.length + index] = row;
            index++;
        }
        tableDataMap.put(tableName, newData);
    }

    public void updateData(String tableName, Integer tableLevel, List<Object[]> rowDataList) {
        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        if (rowDataList.get(0).length != metadata.getColumnNames().size()) {
            throw new IllegalArgumentException("Number of columns must match");
        }
        Object[][] tableData = tableDataMap.get(tableName);
        // 创建 Map 加速查找
        Map<String, Object[]> rowDataMap = rowDataList.stream()
                .collect(Collectors.toMap(rowData -> buildKey(rowData, tableLevel), Function.identity()));
        // 是否替换完，提前退出循环
        int replaceCount = 0;
        // 遍历 tableData 并进行替换
        for (int rowIndex = 0, len = tableData.length; rowIndex < len; rowIndex++) {
            Object[] row = tableData[rowIndex];
            String key = buildKey(row, tableLevel);  // 为当前行生成键
            // 如果在 rowDataMap 中找到匹配的键，则替换当前行数据
            Object[] matchingRowData = rowDataMap.get(key);
            if (matchingRowData != null) {
                tableData[rowIndex] = matchingRowData;
                replaceCount++;
                // 如果已替换完 rowDataList 中所有的匹配项，则提前退出
                if (replaceCount == rowDataList.size()) {
                    break;
                }
            }
        }
    }

    /**
     * @Author: yanghao
     * @Date: 2024-12-11 10:15:29
     * @Params:
     * @Return: null
     * @Description: 构建唯一键的方法，基于 rowData 中的前 tableLevel 列数据
     */
    private String buildKey(Object[] rowData, int tableLevel) {
        StringBuilder keyBuilder = new StringBuilder();
        for (int i = 0; i <= tableLevel; i++) {
            keyBuilder.append(rowData[i]);
            // 用分隔符分隔每一列的值，避免列值之间的歧义
            if (i != tableLevel) {
                keyBuilder.append("|");
            }
        }
        return keyBuilder.toString();
    }

    /**
     * 根据内存表名和数据行号、数据列号更新数据
     *
     * @param tableName
     * @param updateList
     */
    public void updateDataByList(String tableName, List<Map<String, Object>> updateList) {
        if (StringUtils.isEmpty(tableName) || CollectionUtils.isEmpty(updateList)) {
            return;
        }

        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        Object[][] tableData = tableDataMap.get(tableName);

        for (Map<String, Object> map : updateList) {
            Integer dataRowNum = MapUtils.getInteger(map, "dataRowNum");
            Integer dataColumnNum = MapUtils.getInteger(map, "dataColumnNum");
            if (dataRowNum < 0 || dataRowNum >= tableData.length) {
                throw new IllegalArgumentException("dataRowNum not valid");
            }
            if (dataColumnNum < 0 || dataColumnNum >= metadata.getColumnNames().size()) {
                throw new IllegalArgumentException("dataColumnNum not valid");
            }
            tableData[dataRowNum][dataColumnNum] = MapUtils.getObject(map, "data");
        }

    }


    /**
     * 根据内存表名和数据行号、数据列号更新数据
     *
     * @param tableName
     * @param dataRowNum
     * @param dataColumnNum
     * @param data
     */
    public void updateData(String tableName, int dataRowNum, int dataColumnNum, Object data) {
        if (dataColumnNum < 0) {
            throw new IllegalArgumentException("dataColumnNum cannot < 0");
        }
        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        if (dataColumnNum >= metadata.getColumnNames().size()) {
            throw new IllegalArgumentException("dataColumnNum cannot > metadataColumnNum");
        }
        Object[][] tableData = tableDataMap.get(tableName);
        if (dataRowNum < 0 || dataRowNum >= tableData.length) {
            throw new IllegalArgumentException("dataRowNum not valid");
        }
        tableData[dataRowNum][dataColumnNum] = data;
    }

    public void deleteData(String tableName, List<String> rowKeyList) {
        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        Object[][] tableData = tableDataMap.get(tableName);
        Object[][] newData = new Object[tableData.length - rowKeyList.size()][];
        int j = 0;
        for (Object[] row : tableData) {
            if (!rowKeyList.contains(row[0].toString())) {
                newData[j] = row;
                j++;
            }
        }
        tableDataMap.put(tableName, newData);
    }

    public Object[][] getTableData(String tableName) {
        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new ServiceException("Table not found: " + tableName, BizConstants.TABLE_NOT_FOUND);
        }
        Object[][] tableData = tableDataMap.get(tableName);
        return tableData;
    }

    public TableMetaData getTableMetaData(String tableName) {
        tableName = tableName.toLowerCase();
        TableMetaData metadata = tableMetadataMap.get(tableName);
        if (metadata == null) {
            throw new ServiceException("Table not found: " + tableName, BizConstants.TABLE_NOT_FOUND);
        }
        return metadata;
    }

}
