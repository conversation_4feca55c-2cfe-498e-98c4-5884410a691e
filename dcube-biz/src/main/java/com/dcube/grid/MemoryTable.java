package com.dcube.grid;

import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.DataContext;
import org.apache.calcite.linq4j.Enumerable;
import org.apache.calcite.linq4j.Linq4j;
import org.apache.calcite.rel.type.RelDataType;
import org.apache.calcite.rel.type.RelDataTypeFactory;
import org.apache.calcite.rel.type.RelDataTypeField;
import org.apache.calcite.rel.type.RelDataTypeFieldImpl;
import org.apache.calcite.schema.ScannableTable;
import org.apache.calcite.schema.Statistic;
import org.apache.calcite.schema.Statistics;
import org.apache.calcite.schema.impl.AbstractTable;
import org.apache.calcite.sql.type.SqlTypeName;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class MemoryTable extends AbstractTable implements ScannableTable {

    private TableMetaData tableMetaData;

    private Object[][] data;

    // 是否有日期列
//    private boolean dateColumnFlag = false;

    private MemoryTable() {

    }

    protected MemoryTable(TableMetaData tableMetaData, Object[][] data) {
        this.tableMetaData = tableMetaData;
        this.data = data;
//        // 是否有日期列
//        for (Class<?> columnType : tableMetaData.getColumnTypes()) {
//            if (columnType == java.util.Date.class) {
//                // 将 java.util.Date 映射为 TIMESTAMP 类型
//                dateColumnFlag = true;
//                break;
//            }
//        }
    }

    @Override
    public Enumerable<Object[]> scan(DataContext dataContext) {
//        if (dateColumnFlag) {
//            long start = System.currentTimeMillis();
//            // todo 日期类型问题
//            // 使用 Stream 转换数据中的 java.util.Date 为 java.sql.Timestamp，暂时做兼容
//            Object[][] convertedData = Arrays.stream(data)
//                    .parallel()
//                    .map(row -> Arrays.stream(row)
//                            .map(value -> value instanceof java.util.Date
//                                    ? new java.sql.Timestamp(((java.util.Date) value).getTime()) // 转换 Date 为 Timestamp
//                                    : value) // 其他类型保持不变
//                            .toArray()) // 将每一行转换回 Object[]
//                    .toArray(Object[][]::new); // 将整个流转换回 Object[][]
//            long cost = System.currentTimeMillis() - start;
//            log.info("==================> 转换日期时间类型耗时：{}ms", cost);
//            return Linq4j.asEnumerable(convertedData);
//        } else {
        return Linq4j.asEnumerable(data);
//        }
    }

    @Override
    public RelDataType getRowType(RelDataTypeFactory typeFactory) {
        List<RelDataTypeField> fields = new ArrayList<>();
        for (int i = 0; i < tableMetaData.getColumnNames().size(); i++) {
            String columnName = tableMetaData.getColumnNames().get(i);
            Class<?> columnType = tableMetaData.getColumnTypes().get(i);
            RelDataType relDataType;
            if (columnType == java.util.Date.class) {
                // 将 java.util.Date 映射为 TIMESTAMP 类型
                relDataType = typeFactory.createSqlType(SqlTypeName.TIMESTAMP);
            } else if (columnType == Double.class) {
                relDataType = typeFactory.createSqlType(SqlTypeName.DECIMAL);
            } else {
                // 其他类型保持不变
                relDataType = typeFactory.createJavaType(columnType);
            }
            fields.add(new RelDataTypeFieldImpl(columnName, -1, relDataType));
        }
        return typeFactory.createStructType(fields);
    }

    @Override
    public Statistic getStatistic() {
        return Statistics.of((double) data.length, null, null, null);
    }

}
