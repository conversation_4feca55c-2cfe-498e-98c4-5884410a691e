package com.dcube.rule.cube.constants.enums;

import lombok.Getter;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Getter
public enum DimOperationFundamentalTypeEnum {

    ADD("+", 1) {
        @Override
        public BigDecimal apply(BigDecimal a, BigDecimal b) {
            return a.add(b);
        }
    },
    SUBTRACT("-", 1) {
        @Override
        public BigDecimal apply(BigDecimal a, BigDecimal b) {
            return a.subtract(b);
        }
    },
    MULTIPLY("*", 2) {
        @Override
        public BigDecimal apply(BigDecimal a, BigDecimal b) {
            return a.multiply(b);
        }
    },
    DIVIDE("/", 2) {
        @Override
        public BigDecimal apply(BigDecimal a, BigDecimal b) {
            if (b.compareTo(BigDecimal.ZERO) == 0) {
                throw new ArithmeticException("除数不能为0");
            }
            return a.divide(b, 10, RoundingMode.HALF_UP);
        }
    };

    private final String symbol;
    private final int precedence;

    DimOperationFundamentalTypeEnum(String symbol, int precedence) {
        this.symbol = symbol;
        this.precedence = precedence;
    }

    public abstract BigDecimal apply(BigDecimal a, BigDecimal b);

    public static DimOperationFundamentalTypeEnum fromSymbol(String symbol) {
        for (DimOperationFundamentalTypeEnum type : values()) {
            if (type.symbol.equals(symbol)) {
                return type;
            }
        }
        throw new IllegalArgumentException("不支持的运算符: " + symbol);
    }
}
