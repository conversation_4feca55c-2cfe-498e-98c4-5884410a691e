package com.dcube.rule.cube.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.dcube.rule.cube.constants.enums.DimRuleTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 多维计算规则
 * @TableName cube_dim_rule
 */
@TableName(value = "cube_dim_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString
public class DimRule extends Model<DimRule> implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 多维表ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "多维表ID")
    private Long dimTableId;

    /**
     * 规则名称
     */
    @Schema(description = "规则名称")
    private String ruleName;

    /**
     *规则类型
     */
    @Schema(description = "规则类型")
    private DimRuleTypeEnum ruleType;

    /**
     *规则序号
     */
    @Schema(description = "规则序号")
    private Integer orderNum;

    /**
     *规则顺序
     */
    @Schema(description = "规则顺序")
    private Integer position;

    /**
     *版本号
     */
    @Schema(description = "版本号")
    @Version
    private Integer versionNum;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @TableLogic
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "初始化维度")
    private String initDimValue;
}