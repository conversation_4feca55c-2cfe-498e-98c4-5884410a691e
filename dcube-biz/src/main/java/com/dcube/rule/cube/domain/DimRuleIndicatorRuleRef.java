package com.dcube.rule.cube.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 多维计算规则指标引用关系
 * @TableName cube_dim_rule_indicator_ref
 */
@TableName(value = "cube_dim_rule_indicator_ref")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString
@Accessors(chain = true)
public class DimRuleIndicatorRuleRef extends Model<DimRuleIndicatorRuleRef> implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 多维表ID
     */
    @Schema(description = "多维表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimTableId;

    /**
     * 多维表规则ID
     */
    @Schema(description = "多维表规则ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dimRuleId;

    /**
     * 指标ID
     */
    @Schema(description = "指标ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long indId;

    /**
     * 引用多维表ID
     */
    @Schema(description = "引用多维表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long refDimTableId;

    /**
     * 引用指标ID
     */
    @Schema(description = "引用指标ID")
    private String refIndId;

    /**
     * 引用表类型，二维｜多维
     */
    @Schema(description = "引用表类型，二维｜多维")
    private String refTableType;

    /**
     * 引用类型，指标｜维度
     */
    @Schema(description = "引用类型，指标｜维度")
    private String refType;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @JsonIgnore
    @JSONField(serialize = false)
    @TableLogic
    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}