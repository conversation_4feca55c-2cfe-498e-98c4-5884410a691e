package com.dcube.rule.cube.dto;

import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationFundamentalTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode
@ToString
public class DimOperationDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运算详情类型
     */
    @Schema(description = "运算详情类型（四则运算，括号，变量）", requiredMode = Schema.RequiredMode.REQUIRED)
    private DimOperationDetailTypeEnum dimOperationDetailType;

    /**
     * 变量ID
     */
    @Schema(description = "变量ID")
    private Long dimVariableId;

    /**
     * 变量名
     */
    @Schema(description = "变量名")
    private String dimVariableName;

    /**
     * 维度操作类型（加减乘除）
     */
    @Schema(description = "维度操作类型（加减乘除）")
    private DimOperationFundamentalTypeEnum fundamentalType;

    /**
     * 括号操作类型（左右）
     */
    @Schema(description = "括号操作类型（左右）")
    private DimOperationBracketTypeEnum bracketType;

    /**
     * 常数值
     */
    @Schema(description = "常数值")
    private String constantVal;

    /**
     * 缓存的常数值 BigDecimal 对象，避免重复转换
     */
    private BigDecimal cachedConstantValue;

    /**
     * 获取常数值的 BigDecimal 表示，使用缓存避免重复转换
     * @return BigDecimal 值，如果转换失败返回 null
     */
    public BigDecimal getConstantValueAsBigDecimal() {
        // 如果缓存值存在且常数值未改变，直接返回缓存值
        if (cachedConstantValue != null) {
            return cachedConstantValue;
        }

        if (constantVal == null || constantVal.trim().isEmpty()) {
            return null;
        }

        try {
            cachedConstantValue = BigDecimalUtils.convertDigDecimal(constantVal.trim());
            return cachedConstantValue;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 设置常数值，同时清除缓存
     * @param constantVal 常数值字符串
     */
    public void setConstantVal(String constantVal) {
        this.constantVal = constantVal;
        this.cachedConstantValue = null; // 清除缓存，下次访问时重新计算
    }

}
