package com.dcube.rule.cube.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode
@ToString
public class IndicatorOperationEffectScopeBatchSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "多维表id")
    private Long dimTableId;

    @Schema(description = "多维表规则id")
    private Long dimRuleId;

    @Schema(description = "初始化维度")
    private String initDimValue;

    @Schema(description = "维度")
    private List<IndicatorOperationEffectScopeSaveDTO> effectScopes;

}
