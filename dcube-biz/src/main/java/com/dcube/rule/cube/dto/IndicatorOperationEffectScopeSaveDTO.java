package com.dcube.rule.cube.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@ToString
public class IndicatorOperationEffectScopeSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维度ID
     */
    @Schema(description = "维度ID")
    private Long dimDirectoryId;

    /**
     * 维度名称
     */
    @Schema(description = "维度名称")
    private String dimDirectoryName;

    /**
     * 生效范围
     */
    @Schema(description = "生效范围")
    private String effectScope;


}
