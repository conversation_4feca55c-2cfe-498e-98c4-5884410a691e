package com.dcube.rule.cube.dto;

import com.dcube.rule.cube.constants.enums.IndicatorOperationTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@ToString
public class IndicatorOperationSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "多维表id")
    private Long dimTableId;

    @Schema(description = "多维表规则id")
    private Long dimRuleId;

    /**
     * 维度ID
     */
    @Schema(description = "维度ID")
    private Long dimDirectoryId;

    /**
     * 维度名称
     */
    @Schema(description = "维度名称")
    private String dimDirectoryName;

    /**
     * 生效范围
     */
    @Schema(description = "生效范围")
    private String effectScope;

    /**
     * 选中数量
     */
    @Schema(description = "选中数量")
    private Integer effectScopeSize;

    /**
     * 指标ID
     */
    @Schema(description = "指标ID")
    private Long indId;

    /**
     * 指标名称
     */
    @Schema(description = "指标名称")
    private String indName;

    /**
     * 规则表达式
     */
    @Schema(description = "规则表达式")
    private String ruleExpression;

    /**
     * 指标运算类型（1作用范围，2指标规则）
     */
    @Schema(description = "指标运算类型（1作用范围，2指标规则）")
    private IndicatorOperationTypeEnum indicatorOperationType;

}
