package com.dcube.rule.cube.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcube.rule.cube.domain.DimRule;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【cube_dim_rule(多维计算规则)】的数据库操作Mapper
 * @createDate 2024-06-22 21:21:33
 * @Entity generator.domain.MdRule
 */
public interface DimRuleMapper extends BaseMapper<DimRule> {
    DimRule getPreviousDimRuleByPosition(@Param("dimTableId") Long dimTableId, @Param("position") Integer position);

    DimRule getNextDimRuleByPosition(@Param("dimTableId") Long dimTableId, @Param("position") Integer position);

    int updateByVersion(@Param("id") Long id, @Param("versionNum") Integer versionNum);

    DimRule getMaxPosition(@Param("dimTableId") Long dimTableId);
}




