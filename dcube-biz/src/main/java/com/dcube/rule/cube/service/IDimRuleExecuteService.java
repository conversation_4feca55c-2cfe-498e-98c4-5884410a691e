package com.dcube.rule.cube.service;

import com.dcube.biz.dto.DimTableDataCellDto;
import com.dcube.rule.cube.domain.DimRuleIndicatorOperation;

import java.util.List;

public interface IDimRuleExecuteService {

    /**
     * 执行指标规则
     *
     * @param dimRuleId
     * @param indicatorOperationList
     */
    void doExecuteIndicatorOperations(Long dimRuleId, List<DimRuleIndicatorOperation> indicatorOperationList);


    /**
     * 执行指标规则
     *
     * @param dimRuleId
     * @param indicatorOperationList
     * @param parentCellList
     */
    void doExecuteIndicatorOperations(Long dimRuleId, List<DimRuleIndicatorOperation> indicatorOperationList, List<DimTableDataCellDto> parentCellList);


}
