package com.dcube.rule.cube.service.impl;

import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dcube.biz.domain.DimDirectory;
import com.dcube.biz.domain.DimInstance;
import com.dcube.biz.domain.Ind;
import com.dcube.biz.domain.Table;
import com.dcube.biz.dto.DimTableDataCellDto;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.service.IDimDirectoryService;
import com.dcube.biz.service.IDimInstanceService;
import com.dcube.biz.service.IDimTableService;
import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.vo.DimTableInfoVo;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.aspect.ThreadLocalCacheAspect;
import com.dcube.common.utils.ExceptionUtil;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.cube.core.FactTable;
import com.dcube.cube.math.DoubleDouble;
import com.dcube.cube.spi.CubeMetaData;
import com.dcube.cube.spi.CubeSchema;
import com.dcube.cube.spi.CubeServer;
import com.dcube.grid.TableMetaData;
import com.dcube.rule.cube.constants.CubeRuleConstant;
import com.dcube.rule.cube.constants.enums.IndicatorOperationTypeEnum;
import com.dcube.rule.cube.domain.DimRule;
import com.dcube.rule.cube.domain.DimRuleIndicatorOperation;
import com.dcube.rule.cube.exception.CubeRuleDefinitionException;
import com.dcube.rule.cube.service.IDimRuleExecuteService;
import com.dcube.rule.cube.service.IDimRuleIndicatorOperationService;
import com.dcube.rule.cube.service.IDimRuleService;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.operator.OPCTOperator;
import com.dcube.rule.grid.service.IRuleService;
import com.dcube.rule.grid.util.ExpressUtils;
import com.dcube.system.service.ISysConfigService;
import com.dcube.tran.store.repository.AbstractRepository;
import com.dcube.tran.store.repository.RepositoryFactory;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static com.dcube.rule.cube.service.impl.DimRuleServiceImpl.expressTPattern;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DimRuleExecuteServiceImpl implements IDimRuleExecuteService {

    @Autowired
    private IDimRuleService dimRuleService;
    @Autowired
    private IDimRuleIndicatorOperationService dimRuleIndicatorOperationService;
    @Autowired
    private IDimTableService dimTableService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    @Qualifier("cubeTaskExecutorCallerRun")
    private ThreadPoolTaskExecutor cubeTaskExecutorCallerRun;
    @Autowired
    private ExpressUtils expressUtils;
    @Autowired
    private IRuleService ruleService;

    /**
     * 执行指标规则
     *
     * @param dimRuleId
     * @param indicatorOperationList
     */
    @Override
    public void doExecuteIndicatorOperations(Long dimRuleId, List<DimRuleIndicatorOperation> indicatorOperationList) {
        doExecuteIndicatorOperations(dimRuleId, indicatorOperationList, null);
    }

    /**
     * 执行指标规则
     *
     * @param dimRuleId
     * @param indicatorOperationList
     * @param parentCellList
     */
    @Override
    public void doExecuteIndicatorOperations(Long dimRuleId, List<DimRuleIndicatorOperation> indicatorOperationList, List<DimTableDataCellDto> parentCellList) {
        if (CollectionUtils.isEmpty(indicatorOperationList)) {
            return;
        }
        List<DimRuleIndicatorOperation> indicatorOperations = dimRuleIndicatorOperationService.getIndicatorOperations(dimRuleId, IndicatorOperationTypeEnum.EFFECT_SCOPE);
        if (CollectionUtils.isEmpty(indicatorOperations)) {
            return;
        }
        // 过滤空的生效范围
        indicatorOperations = indicatorOperations.stream().filter(v -> StringUtils.isNotEmpty(v.getEffectScope())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(indicatorOperations)) {
            return;
        }
        DimRule dimRule = dimRuleService.getById(dimRuleId);
        Assert.notNull(dimRule, "未查询到多维规则");

        int cubeId = Math.toIntExact(dimRule.getDimTableId());
        DimTableInfoVo dimTableInfoVo = dimTableService.getByIdExt(cubeId);
        Assert.notNull(dimTableInfoVo, "未查询到多维表");

        // 需要计算的指标
        Map<Long, String> indRuleExpressMap = StreamUtils.toLinkedMap(indicatorOperationList, DimRuleIndicatorOperation::getIndId, DimRuleIndicatorOperation::getRuleExpressionInner);
        CubeServer cubeServer = CubeSchema.get().getTable(String.valueOf(dimRule.getDimTableId()));
        if (cubeServer == null) {
            List<String> dimIdList = dimTableInfoVo.getDimList().stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
            List<String> indIdList = dimTableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
            CubeMetaData cubeMetaData = new CubeMetaData(dimTableInfoVo.getId().toString(), dimIdList, indIdList);
            cubeServer = CubeSchema.get().getOrCreateTable(cubeMetaData);
        }

        // 需要过滤处维度成员和指标构建过滤列表
        Map<String, List<String>> filterDims = indicatorOperations.stream()
                .collect(Collectors.toMap(v -> String.valueOf(v.getDimDirectoryId()),
                        v -> Arrays.asList(v.getEffectScope().split(",")),
                        (l, r) -> l,
                        LinkedHashMap::new));

        List<FactTable.Record> recordList = cubeServer.filterRecord(filterDims);
        if (CollectionUtils.isNotEmpty(recordList)) {
            // 初始化线程变量
            ThreadLocalCacheAspect.init();
            ThreadLocalCacheAspect.initTtl();

            // 缓存
            Map<String, Map<String, String>> tTableColumnNameCodeMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.T_TABLE_COLUMN_NAME_CODE_MAP_CACHE + dimTableInfoVo.getId());
            if (tTableColumnNameCodeMap == null) {
                tTableColumnNameCodeMap = new HashMap<>();
                ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.T_TABLE_COLUMN_NAME_CODE_MAP_CACHE + dimTableInfoVo.getId(), tTableColumnNameCodeMap);
            }
            Map<String, Map<String, Integer>> tDimTableIndNameIdMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.T_DIM_TABLE_IND_NAME_ID_MAP_CACHE);
            if (tDimTableIndNameIdMap == null) {
                tDimTableIndNameIdMap = new HashMap<>();
                ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.T_DIM_TABLE_IND_NAME_ID_MAP_CACHE, tDimTableIndNameIdMap);
            }
            Map<String, Integer> tDimTableNameIdMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.T_DIM_TABLE_NAME_ID_MAP_CACHE + dimTableInfoVo.getId());
            if (tDimTableNameIdMap == null) {
                tDimTableNameIdMap = new HashMap<>();
                ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.T_DIM_TABLE_NAME_ID_MAP_CACHE + dimTableInfoVo.getId(), tDimTableNameIdMap);
            }
            Map<String, Map<String, Integer>> tDimTableDimNameIdMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.T_DIM_TABLE_DIM_NAME_ID_MAP_CACHE);
            if (tDimTableDimNameIdMap == null) {
                tDimTableDimNameIdMap = new HashMap<>();
                ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.T_DIM_TABLE_DIM_NAME_ID_MAP_CACHE, tDimTableDimNameIdMap);
            }
            Map<Long, Set<String>> expressCacheMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.EXPRESS_CACHE_MAP_CACHE + dimTableInfoVo.getId());
            if (expressCacheMap == null) {
                expressCacheMap = new HashMap<>();
                ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.EXPRESS_CACHE_MAP_CACHE + dimTableInfoVo.getId(), expressCacheMap);
            }
            Map<Long, Set<String>> dimExpressCacheMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.DIM_EXPRESS_CACHE_MAP_CACHE + dimTableInfoVo.getId());
            if (dimExpressCacheMap == null) {
                dimExpressCacheMap = new HashMap<>();
                ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.DIM_EXPRESS_CACHE_MAP_CACHE + dimTableInfoVo.getId(), dimExpressCacheMap);
            }
            // 本表缓存
            Map<String, Integer> indNameIdMap = tDimTableIndNameIdMap.get(dimTableInfoVo.getTableName());
            if (indNameIdMap == null) {
                indNameIdMap = CollectionUtils.isNotEmpty(dimTableInfoVo.getIndList()) ? dimTableInfoVo.getIndList().stream().collect(Collectors.toMap(Ind::getIndName, Ind::getId)) : Collections.emptyMap();
                tDimTableIndNameIdMap.put(dimTableInfoVo.getTableName(), indNameIdMap);
            }
            IDimDirectoryService dimDirectoryService = SpringUtils.getBean(IDimDirectoryService.class);
            Set<Integer> dimDirectoryIds = StreamUtils.toSet(dimTableInfoVo.getDimList(), DimDirectory::getId);
            List<DimDirectory> dimDirectories = dimDirectoryService.listByIds(dimDirectoryIds);
            IDimInstanceService dimInstanceService = SpringUtils.getBean(IDimInstanceService.class);
            List<DimInstance> dimInstances = dimInstanceService.listByDimDirectoryIds(dimDirectoryIds);

            Map<Integer, List<DimInstance>> dimDirectoryIdMap = StreamUtils.groupByKey(dimInstances, DimInstance::getDimDirectoryId);
            Map<String, Map<String, String>> dimDirectoryMap = new HashMap<>();
            for (DimDirectory dimDirectory : dimDirectories) {
                dimDirectoryMap.put(dimDirectory.getDimDirectoryName(), StreamUtils.toMap(MapUtils.getObject(dimDirectoryIdMap, dimDirectory.getId()), v -> String.valueOf(v.getId()), DimInstance::getDimName));
            }
            ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.DIM_DIRECTORY_MAP_CACHE + dimTableInfoVo.getTableName(), dimDirectoryMap);
            Map<Integer, Integer> dimInstanceDirectoryMap = new HashMap<>();
            for (Map.Entry<Integer, List<DimInstance>> entry : dimDirectoryIdMap.entrySet()) {
                for (DimInstance dimInstance : entry.getValue()) {
                    dimInstanceDirectoryMap.put(dimInstance.getId(), entry.getKey());
                }
            }
            Map<Integer, String> dimInstanceMap = StreamUtils.toMap(dimInstances, DimInstance::getId, DimInstance::getDimName);
            ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.DIM_INSTANCE_MAP_CACHE + dimTableInfoVo.getId(), dimInstanceMap);
            for (String ruleExpress : indRuleExpressMap.values()) {
                initTCache(ruleExpress, tTableColumnNameCodeMap, tDimTableIndNameIdMap, tDimTableDimNameIdMap, tDimTableNameIdMap, expressCacheMap, dimExpressCacheMap);
                handleVsumFunc(dimRuleId, ruleExpress);
            }

            // 分批
            int length = recordList.size();
            int threadSize = sysConfigService.getRuleExecuteThreadSize();

            // 调整threadSize，如果length小于threadSize，只使用length个线程
            threadSize = Math.min(length, threadSize);
            log.info("实际的执行规则线程数：{}", threadSize);
            CompletableFuture<Void> future = new CompletableFuture<>();
            // 异步，分批
            // 计算每个线程的基本任务数
            int batchSize = length / threadSize;
            int remainder = length % threadSize;
            final CubeServer _cubeServer = cubeServer;
            List<CompletableFuture<Void>> completableFutures = new ArrayList<>(threadSize);
            // 分配任务到线程
            for (int i = 0; i < threadSize; i++) {
                // 起始索引
                int startIndex = i * batchSize + Math.min(i, remainder);
                // 结束索引，确保不越界
                int endIndex = (i + 1) * batchSize + Math.min(i + 1, remainder);
                // 确保最后一个线程不会越界
                endIndex = Math.min(endIndex, length);
                final int _endIndex = endIndex;
                CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(TtlRunnable.get(() -> {
                    for (int j = startIndex; j < _endIndex; j++) {
                        FactTable.Record record = recordList.get(j);
                        for (Map.Entry<Long, String> entry : indRuleExpressMap.entrySet()) {
                            this.handleOneRecord(record, _cubeServer, entry.getKey(), entry.getValue(), dimInstanceDirectoryMap, dimRuleId, dimTableInfoVo);
                        }
                        CubeSchema.get().updateTableData(_cubeServer, record);
                    }
                }), cubeTaskExecutorCallerRun).exceptionally(e -> {
                    future.completeExceptionally(e);
                    return null;
                });
                completableFutures.add(completableFuture);
            }
            future.exceptionally(e -> {
                completableFutures.forEach(completableFuture -> {
                    completableFuture.completeExceptionally(e);
                });
                return null;
            });
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).exceptionally(e -> {
                log.error("执行计算规则时出现异常", e);
                String message;
                if (e instanceof CompletionException) {
                    message = ExceptionUtil.getLocalizedMessage(e.getCause());
                } else {
                    message = ExceptionUtil.getLocalizedMessage(e);
                }
                throw new RuleExecutionException(message);
            }).join();

            // 父级汇总
            if (parentCellList != null) {
                List<List<String>> dimInstanceIdLists = collectDimInstanceIds(new ArrayList<>(filterDims.values()));
                if (CollectionUtils.isEmpty(dimInstanceIdLists)) {
                    return;
                }
                Map<String, Integer> toParentMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.TO_PARENT_MAP + dimTableInfoVo.getId());
                if (toParentMap == null) {
                    toParentMap = dimInstances.stream().filter(x -> x.getParentId() != null && x.getParentId() > 0).collect(Collectors.toMap(x -> x.getDimDirectoryId() + ":" + x.getId(), DimInstance::getParentId));
                    ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.TO_PARENT_MAP + dimTableInfoVo.getId(), toParentMap);
                }
                // 添加到父级汇总
                List<Integer> relDimIdList = filterDims.keySet().stream().map(Integer::valueOf).collect(Collectors.toList());
                Set<DimTableDataCellDto> parentCellSet = new HashSet<>();
                for (List<String> dimInstanceIdList : dimInstanceIdLists) {
                    for (int i = 0, size = relDimIdList.size(); i < size; i++) {
                        String dimId = relDimIdList.get(i).toString();
                        String parentDimInstanceIdString = dimId + ":" + dimInstanceIdList.get(i);
                        Integer parentDimInstanceId = MapUtils.getObject(toParentMap, parentDimInstanceIdString);
                        if (parentDimInstanceId == null) {
                            continue;
                        }
                        for (Map.Entry<Long, String> entry : indRuleExpressMap.entrySet()) {
                            DimTableDataCellDto dto = new DimTableDataCellDto();
                            dto.setRelDimIdList(relDimIdList);
                            dto.setDimInstanceIdList(new ArrayList<>(dimInstanceIdList));
                            dto.setOpType("add");
                            dto.setIndId(String.valueOf(entry.getKey()));
                            dto.setOriginalValue((double) 0);
                            dto.setParentDimId(dimId);
                            String dimInstanceIdString = parentDimInstanceId.toString();
                            dto.setParentDimInstanceId(dimInstanceIdString);
                            dto.getDimInstanceIdList().set(i, dimInstanceIdString);
                            parentCellSet.add(dto);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(parentCellSet)) {
                    parentCellList.addAll(parentCellSet);
                }
            }
        }
    }


    private static List<List<String>> collectDimInstanceIds(List<List<String>> lists) {
        if (CollectionUtils.isEmpty(lists)) {
            return Collections.emptyList();
        }
        List<List<String>> result = new ArrayList<>();

        Deque<List<String>> stack = new ArrayDeque<>();
        stack.push(new ArrayList<>());

        while (!stack.isEmpty()) {
            List<String> combination = stack.pop();

            if (combination.size() == lists.size()) {
                result.add(combination);
            } else {
                int nextIdx = combination.size();
                List<String> nextList = lists.get(nextIdx);

                // 反向添加以保持原始顺序
                for (int i = nextList.size() - 1; i >= 0; i--) {
                    List<String> newCombination = new ArrayList<>(combination);
                    newCombination.add(nextList.get(i));
                    stack.push(newCombination);
                }
            }
        }
        return result;
    }

    private void handleOneRecord(FactTable.Record factTableRecord, CubeServer cubeServer, Long key, String ruleExpress, Map<Integer, Integer> dimInstanceDirectoryMap, Long dimRuleId, DimTableInfoVo dimTableInfoVo) {
        double val;
        try {
            val = executeRule(ruleExpress, factTableRecord, cubeServer, dimRuleId, dimTableInfoVo);
        } catch (Exception e) {
            log.error("executeRule {} occur exception:", factTableRecord, e);
            throw e;
        }
        log.debug("==================> 更新的record：{}，计算值：{}", factTableRecord, val);
        DoubleDouble[] indOfFact = factTableRecord.getIndOfFact();
        int index = cubeServer.factTable.getIndIndex(String.valueOf(key));
        indOfFact[index] = new DoubleDouble(val);
    }

    private double executeRule(String ruleExpress, FactTable.Record rs, CubeServer cubeServer, Long dimRuleId, DimTableInfoVo dimTableInfoVo) {
        Map<String, Object> context = new HashMap<>();
        ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.RULE_ID, dimRuleId);
        ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.CUBE_RULE, Boolean.TRUE);
        ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.CUBE_RECORD, rs);
        ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.CUBE_TABLE, dimTableInfoVo);
        Object executed = expressUtils.executeCubeRule(ruleExpress, context);
        if (executed == null) {
            return 0.0d;
        }
        if (executed instanceof Number) {
            return ((Number) executed).doubleValue();
        } else if (executed instanceof DoubleDouble) {
            return ((DoubleDouble) executed).doubleValue();
        } else {
            try {
                return BigDecimalUtils.convertDigDecimal(executed).doubleValue();
            } catch (Exception e) {
                return 0.0d;
            }
        }
    }

    private void initTCache(String express, Map<String, Map<String, String>> tTableColumnNameCodeMap, Map<String, Map<String, Integer>> tDimTableIndNameIdMap, Map<String, Map<String, Integer>> tDimTableDimNameIdMap, Map<String, Integer> tDimTableNameIdMap, Map<Long, Set<String>> expressCacheMap, Map<Long, Set<String>> dimExpressCacheMap) {
        if (StringUtils.isEmpty(express)) {
            return;
        }
        Matcher tMatcher = expressTPattern.matcher(express);
        IRuleService ruleService = SpringUtils.getBean(IRuleService.class);
        IDimRuleService dimRuleService = SpringUtils.getBean(IDimRuleService.class);
        while (tMatcher.find()) {
            String refTableType = tMatcher.group(1);
            if (StringUtils.equals(refTableType, "grid")) {
                // 二维
                String tableName = tMatcher.group(2);
                Table tableByName = ruleService.getTableByName(tableName);
                if (tableByName == null) {
                    throw new CubeRuleDefinitionException("未找到二维表：" + tableName + "！");
                }
                Integer tTableId = tableByName.getId();
                TableVo tableVoById = ruleService.getTableVoById(tTableId);
                if (tableVoById == null) {
                    throw new CubeRuleDefinitionException("未找到二维表：" + tableName + "！");
                }
                Map<String, String> columnNameCodeMap = tTableColumnNameCodeMap.get(tableName);
                if (columnNameCodeMap == null) {
                    columnNameCodeMap = CollectionUtils.isNotEmpty(tableVoById.getTableMetaJson()) ? tableVoById.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode)) : Collections.emptyMap();
                    tTableColumnNameCodeMap.put(tableName, columnNameCodeMap);

                    String memTableName = tableVoById.getMemTableName();
                    TableMetaData tableMetaData = MemGridUtils.getTableMetaData(memTableName);
                    if (tableMetaData != null) {
                        List<String> columnNames = tableMetaData.getColumnNames();
                        Map<Integer, String> indexColumnMap = Maps.newLinkedHashMapWithExpectedSize(columnNames.size());
                        for (int i = 0, size = columnNames.size(); i < size; i++) {
                            String columnCode = columnNames.get(i);
                            indexColumnMap.put(i, columnCode);
                        }

                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLUMN_INDEX_MAP + tTableId, indexColumnMap);
                        Map<String, Integer> reverseIndexColumnMap = indexColumnMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2));
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP + tTableId, reverseIndexColumnMap);

                        Map<String, String> tableNameCodeMap = tableVoById.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode));
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId, tableNameCodeMap);// 当前表的列名编码映射
                    }
                }
                String columnName = tMatcher.group(3);
                Set<String> strings = expressCacheMap.computeIfAbsent(Long.valueOf(tTableId), v -> new HashSet<>());
                if (strings.contains(columnName)) {
                    continue;
                }
                strings.add(columnName);
                Map tableColumnNameCodeMap = MapUtils.getMap(tTableColumnNameCodeMap, tableName);
                String tColumnCode = MapUtils.getString(tableColumnNameCodeMap, columnName);
                if (StringUtils.isEmpty(tColumnCode)) {
                    throw new CubeRuleDefinitionException("二维表：" + tableName + "不存在列：" + columnName);
                }

                // 表数据缓存
                Object ruleCache = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.TABLE_DATA + tableVoById.getTableName() + "_" + columnName);
                if (ruleCache == null) {
                    Map resMap;
                    Object[][] tableData = ruleService.getTableData(tableByName);
                    if (tableData == null || tableData.length == 0) {
                        resMap = Collections.emptyMap();
                    } else {
                        resMap = Maps.newHashMapWithExpectedSize(tableData.length);
                        Map<String, Integer> columnIndexMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP + tTableId);
                        if (StringUtils.equalsIgnoreCase(tableVoById.getChildFlag(), "Y")) {
                            for (Object[] rowData : tableData) {
                                resMap.put(String.valueOf(rowData[1]), rowData[MapUtils.getObject(columnIndexMap, tColumnCode)]);
                            }
                        } else {
                            for (Object[] rowData : tableData) {
                                resMap.put(String.valueOf(rowData[0]), rowData[MapUtils.getObject(columnIndexMap, tColumnCode)]);
                            }
                        }
                    }
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.TABLE_DATA + tableVoById.getTableName() + "_" + columnName, resMap);
                }
            } else {
                // 多维
                String tableName = tMatcher.group(2);
                Integer tTableId = tDimTableNameIdMap.get(tableName);
                DimTableInfoVo tableByName = dimRuleService.getDimTableByName(tableName);
                if (tableByName == null) {
                    throw new CubeRuleDefinitionException("未找到多维表：" + tableName);
                }
                if (tTableId == null) {
                    tTableId = tableByName.getId();
                    tDimTableNameIdMap.put(tableName, tTableId);
                }
                Map<String, Integer> indNameIdMap = tDimTableIndNameIdMap.get(tableName);
                if (indNameIdMap == null) {
                    indNameIdMap = CollectionUtils.isNotEmpty(tableByName.getIndList()) ? tableByName.getIndList().stream().collect(Collectors.toMap(Ind::getIndName, Ind::getId)) : Collections.emptyMap();
                    tDimTableIndNameIdMap.put(tableName, indNameIdMap);
                }
                Map<String, Integer> dimNameIdMap = tDimTableDimNameIdMap.get(tableName);
                if (dimNameIdMap == null) {
                    dimNameIdMap = CollectionUtils.isNotEmpty(tableByName.getDimList()) ? tableByName.getDimList().stream().collect(Collectors.toMap(DimDirectory::getDimDirectoryName, DimDirectory::getId)) : Collections.emptyMap();
                    tDimTableDimNameIdMap.put(tableName, dimNameIdMap);
                }
                IDimDirectoryService dimDirectoryService = SpringUtils.getBean(IDimDirectoryService.class);
                Set<Integer> dimDirectoryIds = StreamUtils.toSet(tableByName.getDimList(), DimDirectory::getId);
                List<DimDirectory> dimDirectories = dimDirectoryService.listByIds(dimDirectoryIds);
                IDimInstanceService dimInstanceService = SpringUtils.getBean(IDimInstanceService.class);
                List<DimInstance> dimInstances = dimInstanceService.list(Wrappers.<DimInstance>lambdaQuery().in(DimInstance::getDimDirectoryId, dimDirectoryIds));
                Map<Integer, List<DimInstance>> dimDirectoryIdMap = StreamUtils.groupByKey(dimInstances, DimInstance::getDimDirectoryId);
                Map<String, Map<String, String>> dimDirectoryMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.DIM_DIRECTORY_MAP_CACHE + tableByName.getTableName());
                if (dimDirectoryMap == null) {
                    dimDirectoryMap = new HashMap<>();
                    for (DimDirectory dimDirectory : dimDirectories) {
                        dimDirectoryMap.put(dimDirectory.getDimDirectoryName(), StreamUtils.toMap(MapUtils.getObject(dimDirectoryIdMap, dimDirectory.getId()), v -> String.valueOf(v.getId()), DimInstance::getDimName));
                    }
                    ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.DIM_DIRECTORY_MAP_CACHE + tableByName.getTableName(), dimDirectoryMap);
                }

                String indName = tMatcher.group(3);
                Set<String> strings = dimExpressCacheMap.computeIfAbsent(Long.valueOf(tTableId), v -> new HashSet<>());
                if (strings.contains(indName)) {
                    continue;
                }
                strings.add(indName);

                // 缓存数据
                Map<String, Map<String, Object>> dimTableDataMapCache = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.DIM_TABLE_DATA_MAP_CACHE);
                if (dimTableDataMapCache == null) {
                    dimTableDataMapCache = new HashMap<>();
                    ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.DIM_TABLE_DATA_MAP_CACHE, dimTableDataMapCache);
                }
                Map<String, Object> dimTableDataMap = dimTableDataMapCache.get(tableName);
                if (dimTableDataMap == null) {
                    CubeServer tCubeServer = CubeSchema.get().getTable(String.valueOf(tTableId));
                    if (tCubeServer == null) {
                        List<String> dimIdList = tableByName.getDimList().stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
                        List<String> indIdList = tableByName.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
                        CubeMetaData cubeMetaData = new CubeMetaData(tableByName.getId().toString(), dimIdList, indIdList);
                        tCubeServer = CubeSchema.get().getOrCreateTable(cubeMetaData);
                    }
                    // 构建全量的维度查询条件
                    List<DimDirectory> dimList = tableByName.getDimList();
                    Map<String, List<String>> filterDims = Maps.newHashMapWithExpectedSize(dimList.size());
                    for (DimDirectory dimDirectory : dimList) {
                        filterDims.put(String.valueOf(dimDirectory.getId()), StreamUtils.toList(MapUtils.getObject(dimDirectoryIdMap, dimDirectory.getId(), Collections.emptyList()), v -> String.valueOf(v.getId())));
                    }
                    // 查询指标
                    List<FactTable.Record> records = tCubeServer.filterRecord(filterDims);
                    if (CollectionUtils.isNotEmpty(records)) {
                        dimTableDataMap = Maps.newHashMapWithExpectedSize(records.size() + 1);
                        for (FactTable.Record record : records) {
                            dimTableDataMap.put(String.valueOf(record.getId()), record);
                        }
                    } else {
                        dimTableDataMap = Maps.newHashMapWithExpectedSize(1);
                    }
                    dimTableDataMap.put(RuleConstant.RULE_TYPE, "cube");
                    dimTableDataMapCache.put(tableName, dimTableDataMap);
                }
            }
        }
    }

    private void handleVsumFunc(Long ruleId, String express) {
        // 聚合
        if (StringUtils.contains(express, "vsum(")) {
            String vsum = ExpressUtils.extractFuncContent(express, "vsum(");
            if (StringUtils.isNotEmpty(vsum)) {
                Matcher vsumTMatcher = expressTPattern.matcher(vsum);
                String ruleType = "";
                String tName = "";
                String sumColumnName = "";
                if (vsumTMatcher.find()) {
                    ruleType = vsumTMatcher.group(1);
                    if (!StringUtils.equals(ruleType, "grid")) {
                        // 二维规则才有缓存
                        return;
                    }
                    tName = vsumTMatcher.group(2);
                    sumColumnName = vsumTMatcher.group(3);
                }

                Integer tTableId = ruleService.getTableByName(tName).getId();
                Map columnNameCodeMap = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COLUMN_NAME_CODE_MAP + tTableId);

                TableVo tableVo = ruleService.getTableVoById(tTableId);
                String sumColumnCode = MapUtils.getString(columnNameCodeMap, sumColumnName);
                if (StringUtils.isEmpty(sumColumnCode)) {
                    throw new RuleExecutionException("vsum函数中：" + sumColumnName + "未找到！");
                }

                List<String> groupColumnNames = new ArrayList<>();
                while (vsumTMatcher.find()) {
                    if (!StringUtils.equals(vsumTMatcher.group(2), tName)) {
                        throw new RuleExecutionException("vsum函数中他表需要保持一致");
                    }
                    groupColumnNames.add(vsumTMatcher.group(3));
                }

                String sumColumnType;
                Map<String, TableMetaJson> tableMetaJsonMap = tableVo.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getCode, Function.identity()));
                TableMetaJson statColumnMetaJson = tableMetaJsonMap.get(sumColumnCode);
                // 为兼容数据格式 优先赋值存储格式
                if (statColumnMetaJson.getDataFormat() != null) {
                    sumColumnType = statColumnMetaJson.getDataFormat().getStorageType();
                } else if (StringUtils.isNotEmpty(statColumnMetaJson.getNewColumnType())) {
                    sumColumnType = statColumnMetaJson.getNewColumnType();
                } else {
                    sumColumnType = "VARCHAR";
                }
                Class cl = MemGridUtils.getColumnType(sumColumnType);
                // 非数字类型不参与计算
                if (Integer.class != cl && Double.class != cl) {
                    throw new RuleExecutionException("vsum函数中：" + sumColumnName + "非数字类型不参与计算！");
                }
                String tableName = "grid." + tableVo.getMemTableName().toLowerCase();
                // group by
                AbstractRepository tarRepository = RepositoryFactory.getRepository(JdbcUtils.DB_TYPE_MEMORY, null, tableName);
                if (CollectionUtils.isNotEmpty(groupColumnNames)) {
                    List<String> groupColumnCodes = new ArrayList<>(groupColumnNames.size());
                    for (String groupColumnName : groupColumnNames) {
                        String groupColumnCode = MapUtils.getString(columnNameCodeMap, groupColumnName);
                        if (StringUtils.isEmpty(groupColumnCode)) {
                            throw new RuleExecutionException("vsum函数中：" + groupColumnName + "未找到！");
                        } else {
                            groupColumnCodes.add(groupColumnCode);
                        }
                    }

                    String groupColumnCodesString1 = String.join(",", groupColumnCodes);
                    String query = String.format("select %s, sum(%s) as dcube_sum_s from %s group by %s", groupColumnCodesString1, sumColumnCode, tableName, groupColumnCodesString1);
                    Map<String, Object> vsumCache = tarRepository.executeQuerySql(query, groupColumnCodes, "dcube_sum_s");
                    ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VSUM_T_SUM + ruleId, vsumCache);
                } else {
                    String query = String.format("select sum(%s) as s from %s", sumColumnCode, tableName);
                    List<Map<String, Object>> data = tarRepository.executeQuerySql(query);
                    for (Map<String, Object> map : data) {
                        ThreadLocalUtils.setTtl(ExpressUtils.class, RuleConstant.VSUM_T_SUM + ruleId, MapUtils.getObject(map, "s"));
                    }
                }
            } else {
                throw new RuleExecutionException("vsum函数定义出现错误，请检查！");
            }
        }
    }

}
