package com.dcube.rule.cube.service.impl;

import com.alibaba.ttl.TtlRunnable;
import com.dcube.biz.dto.DimTableDataCellDto;
import com.dcube.common.aspect.ThreadLocalCacheAspect;
import com.dcube.common.core.text.Convert;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.rule.cube.domain.DimRuleIndicatorOperation;
import com.dcube.rule.cube.service.IDimRuleService;
import com.dcube.rule.cube.service.IDimRuleServiceHelperService;
import com.dcube.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DimRuleServiceHelperImpl implements IDimRuleServiceHelperService {

    @Autowired
    private IDimRuleService dimRuleService;
    @Autowired
    @Qualifier("cubeTaskExecutorAbort")
    private ThreadPoolTaskExecutor cubeTaskExecutorAbort;
    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public void execute(Integer dimTableId) {
        try {
            ThreadLocalCacheAspect.init();
            ThreadLocalCacheAspect.initTtl();
            dimRuleService.execute(dimTableId);
        } finally {
            ThreadLocalUtils.clear();
        }
    }

    @Override
    public void execute(Integer dimTableId, List<DimTableDataCellDto> parentCellList) {
        Boolean isAsync = Convert.toBool(sysConfigService.selectConfigByKey("sys.rule.cube.execute.isAsync"));
        if (Boolean.TRUE.equals(isAsync)) {
            cubeTaskExecutorAbort.execute(TtlRunnable.get(() -> {
                try {
                    ThreadLocalCacheAspect.init();
                    ThreadLocalCacheAspect.initTtl();
                    dimRuleService.execute(dimTableId, parentCellList);
                } finally {
                    ThreadLocalUtils.clear();
                }
            }));
        } else {
            try {
                ThreadLocalCacheAspect.init();
                ThreadLocalCacheAspect.initTtl();
                dimRuleService.execute(dimTableId, parentCellList);
            } finally {
                ThreadLocalUtils.clear();
            }
        }
    }

    @Override
    public void initDimValueAndExecute(Long dimTableId, String initDimValue, List<DimRuleIndicatorOperation> dimRuleIndicatorOperations) {
        Boolean isAsync = Convert.toBool(sysConfigService.selectConfigByKey("sys.rule.cube.execute.isAsync"));
        if (Boolean.TRUE.equals(isAsync)) {
            cubeTaskExecutorAbort.execute(TtlRunnable.get(() -> {
                // 初始化维度数据
                if (StringUtils.equals(initDimValue, "1")) {
                    dimRuleService.initDimValue(dimTableId, dimRuleIndicatorOperations);
                }
                try {
                    ThreadLocalCacheAspect.init();
                    ThreadLocalCacheAspect.initTtl();
                    dimRuleService.execute(Math.toIntExact(dimTableId));
                } finally {
                    ThreadLocalUtils.clear();
                }
            }));
        } else {
            // 初始化维度数据
            if (StringUtils.equals(initDimValue, "1")) {
                dimRuleService.initDimValue(dimTableId, dimRuleIndicatorOperations);
            }
            try {
                ThreadLocalCacheAspect.init();
                ThreadLocalCacheAspect.initTtl();
                dimRuleService.execute(Math.toIntExact(dimTableId));
            } finally {
                ThreadLocalUtils.clear();
            }
        }
    }

}




