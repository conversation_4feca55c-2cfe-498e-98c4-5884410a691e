package com.dcube.rule.cube.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.biz.constant.BizConstants;
import com.dcube.biz.constant.enums.DimTypeEnum;
import com.dcube.biz.domain.DimDirectory;
import com.dcube.biz.domain.DimInstance;
import com.dcube.biz.domain.Ind;
import com.dcube.biz.domain.Table;
import com.dcube.biz.dto.DimTableDataCellDto;
import com.dcube.biz.dto.DimTableDataDto;
import com.dcube.biz.json.TableMetaJson;
import com.dcube.biz.mapper.DimInstanceMapper;
import com.dcube.biz.query.InstanceTreeQuery;
import com.dcube.biz.service.IDimInstanceService;
import com.dcube.biz.service.IDimTableService;
import com.dcube.biz.vo.DimTableInfoVo;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.annotation.ThreadLocalCache;
import com.dcube.common.constant.enums.MoveTypeEnum;
import com.dcube.common.enums.ThreadLocalCacheType;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.StreamUtils;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.cube.core.FactTable;
import com.dcube.cube.math.DoubleDouble;
import com.dcube.cube.spi.CubeMetaData;
import com.dcube.cube.spi.CubeSchema;
import com.dcube.cube.spi.CubeServer;
import com.dcube.rule.cube.constants.CubeRuleConstant;
import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.IndicatorOperationTypeEnum;
import com.dcube.rule.cube.domain.*;
import com.dcube.rule.cube.dto.*;
import com.dcube.rule.cube.exception.CubeRuleDefinitionException;
import com.dcube.rule.cube.mapper.DimRuleIndicatorOperationMapper;
import com.dcube.rule.cube.mapper.DimRuleMapper;
import com.dcube.rule.cube.service.*;
import com.dcube.rule.cube.utils.DimOperationUtil;
import com.dcube.rule.cube.vo.*;
import com.dcube.rule.grid.RuleGraph;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.operator.OPCTOperator;
import com.dcube.rule.grid.service.IRuleService;
import com.dcube.rule.grid.util.ExpressUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.dcube.rule.grid.util.ExpressUtils.generateAllWriterFilterDims;

/**
 * <AUTHOR>
 * @description 针对表【cube_dim_rule(多维计算规则)】的数据库操作Service实现
 * @createDate 2024-06-22 21:21:33
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DimRuleServiceImpl extends ServiceImpl<DimRuleMapper, DimRule>
        implements IDimRuleService {
    private final IDimRuleIndicatorOperationService dimRuleIndicatorOperationService;
    private final DimRuleIndicatorOperationMapper dimRuleIndicatorOperationMapper;
    @Autowired
    private IDimTableService dimTableService;
    @Autowired
    private IDimInstanceService instanceService;
    @Autowired
    private DimInstanceMapper dimInstanceMapper;
    @Autowired
    private IRuleService ruleService;
    @Autowired
    private IDimRuleExecuteService dimRuleExecuteService;
    @Autowired
    private IDimRuleIndicatorRuleRefService dimRuleIndicatorRuleRefService;
    @Autowired
    @Qualifier("cubeTaskExecutorAbort")
    private ThreadPoolTaskExecutor cubeTaskExecutorAbort;
    @Autowired
    private IDimRuleServiceHelperService dimRuleServiceHelperService;
    @Autowired
    @Qualifier("ruleGraphCache")
    private Cache<String, RuleGraph> ruleGraphCache;
    //    @Autowired
//    private ISysConfigService sysConfigService;
//    @Autowired
//    @Qualifier("cubeTaskExecutorCallerRun")
//    private ThreadPoolTaskExecutor cubeTaskExecutorCallerRun;
    @Autowired
    private IDimInstanceService dimInstanceService;
    @Autowired
    private IDimRuleVariableService dimRuleVariableService;
    @Autowired
    private IDimRuleVariableDetailService dimRuleVariableDetailService;

    private static final Pattern expressOPattern = Pattern.compile("o\\(\"(.*?)\"\\)");
    private static final String expressTPatternString = "t\\(\"(.*?)\",\"(.*?)\",\"(.*?)\"\\)";
    public static final Pattern expressTPattern = Pattern.compile(expressTPatternString);

    @Override
    public List<DimRule> getCubeRule(Long dimTableId) {
        return this.list(Wrappers.<DimRule>lambdaQuery().eq(DimRule::getDimTableId, dimTableId).orderByAsc(DimRule::getPosition));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean move(Long id, MoveTypeEnum moveType) {
        if (moveType == MoveTypeEnum.UP) {
            return moveGroupUp(id);
        } else {
            return moveGroupDown(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean moveGroupUp(Long id) {
        DimRule dimRule = this.baseMapper.selectById(id);
        Assert.notNull(dimRule, "当前规则不存在");
        Integer currentOrder = dimRule.getPosition();
        DimRule previousDimRule = this.baseMapper.getPreviousDimRuleByPosition(dimRule.getDimTableId(), currentOrder);
        Assert.notNull(previousDimRule, "无法上移");
        swapSortOrder(dimRule, previousDimRule);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean moveGroupDown(Long id) {
        DimRule dimRule = this.baseMapper.selectById(id);
        Assert.notNull(dimRule, "当前规则不存在");
        Integer currentOrder = dimRule.getPosition();
        DimRule nextDimRule = this.baseMapper.getNextDimRuleByPosition(dimRule.getDimTableId(), currentOrder);
        Assert.notNull(nextDimRule, "无法下移");
        swapSortOrder(dimRule, nextDimRule);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DimRule add(DimRuleAddDTO addDto) {
        DimRule maxPosition = this.baseMapper.getMaxPosition(addDto.getDimTableId());
        DimRule dimRule = new DimRule();
        BeanUtils.copyProperties(addDto, dimRule);
        if (Objects.nonNull(maxPosition)) {
            if (Objects.nonNull(maxPosition.getPosition())) {
                dimRule.setPosition(maxPosition.getPosition() + 1);
            }
            if (Objects.nonNull(maxPosition.getOrderNum())) {
                dimRule.setOrderNum(maxPosition.getOrderNum() + 1);
            }
        }
        if (dimRule.getPosition() == null) {
            dimRule.setPosition(0);
        }
        if (dimRule.getOrderNum() == null) {
            dimRule.setOrderNum(1);
        }
        this.save(dimRule);
        if (Objects.nonNull(maxPosition)) {
            int i = this.baseMapper.updateByVersion(maxPosition.getId(), maxPosition.getVersionNum());
            if (i < 1) {
                throw new CubeRuleDefinitionException("规则添加失败，该多维表规则有更新");
            }
        }
        return dimRule;
    }

    @Override
    public IndicatorOperationVO getIndicatorOperation(Long dimRuleId) {
        DimRule dimRule = this.getById(dimRuleId);
        Assert.notNull(dimRule, "未查询到规则");
        DimTableInfoVo dimTableInfoVo = dimTableService.getByIdExt(Math.toIntExact(dimRule.getDimTableId()));
        Assert.notNull(dimTableInfoVo, "未查询到多维表");
        List<DimRuleIndicatorOperation> list = dimRuleIndicatorOperationService.list(Wrappers.<DimRuleIndicatorOperation>lambdaQuery()
                .eq(DimRuleIndicatorOperation::getDimRuleId, dimRuleId));
        Map<IndicatorOperationTypeEnum, List<DimRuleIndicatorOperation>> indicatorOperationTypeMap = list.stream().collect(Collectors.groupingBy(DimRuleIndicatorOperation::getIndicatorOperationType));
        IndicatorOperationVO indicatorOperationVO = new IndicatorOperationVO();
        indicatorOperationVO.setInitDimValue(dimRule.getInitDimValue());
        // 设置维度作用范围
        if (CollectionUtils.isNotEmpty(dimTableInfoVo.getDimList())) {
            QueryWrapper<DimInstance> query = Wrappers.query();
            query.select("dim_directory_id", "count(*) as cnt");
            query.in("dim_directory_id", dimTableInfoVo.getDimList().stream().map(DimDirectory::getId).collect(Collectors.toSet()));
            query.groupBy("dim_directory_id");
            Map<String, Long> dimDirectoryIdInstanceMap = dimInstanceMapper.selectMaps(query).stream().collect(Collectors.toMap(v -> MapUtils.getString(v, "dim_directory_id"), v -> MapUtils.getLong(v, "cnt")));
            List<DimRuleIndicatorOperation> effectScopeList = MapUtils.getObject(indicatorOperationTypeMap, IndicatorOperationTypeEnum.EFFECT_SCOPE, Collections.emptyList());
            Map<Long, DimRuleIndicatorOperation> dimDirectoryIdMap = effectScopeList.stream().collect(Collectors.toMap(DimRuleIndicatorOperation::getDimDirectoryId, Function.identity()));
            List<DimDirectoryVO> dimDirectoryVOS = dimTableInfoVo.getDimList().stream().map(dimDirectory -> {
                DimDirectoryVO dimDirectoryVO = new DimDirectoryVO();
                Long dimDirectoryId = Long.valueOf(dimDirectory.getId());
                dimDirectoryVO.setId(dimDirectoryId);
                dimDirectoryVO.setDimDirectoryName(dimDirectory.getDimDirectoryName());
                dimDirectoryVO.setDimDirectoryType(dimDirectory.getDimDirectoryType());
                dimDirectoryVO.setDimType(dimDirectory.getDimType());
                dimDirectoryVO.setIndexNo(dimDirectory.getIndexNo());
                dimDirectoryVO.setParentId(Long.valueOf(dimDirectory.getParentId()));
//                dimDirectoryVO.setItemSize(dimDirectory.getItemSize());
                dimDirectoryVO.setItemSize(MapUtils.getLongValue(dimDirectoryIdInstanceMap, String.valueOf(dimDirectoryId)));
                dimDirectoryVO.setIndicatorOperation(MapUtils.getObject(dimDirectoryIdMap, dimDirectoryId));
                if (dimDirectoryVO.getIndicatorOperation() != null && StringUtils.isNotEmpty(dimDirectoryVO.getIndicatorOperation().getEffectScope())) {
                    dimDirectoryVO.setCheckedItemSize((long) dimDirectoryVO.getIndicatorOperation().getEffectScope().split(",").length);
                } else {
                    dimDirectoryVO.setCheckedItemSize(0L);
                }
                return dimDirectoryVO;
            }).collect(Collectors.toList());
            indicatorOperationVO.setDimList(dimDirectoryVOS);
        }
        // 设置指标规则
        if (CollectionUtils.isNotEmpty(dimTableInfoVo.getIndList())) {
            List<DimRuleIndicatorOperation> indRuleList = MapUtils.getObject(indicatorOperationTypeMap, IndicatorOperationTypeEnum.IND_RULE, Collections.emptyList());
            Map<Long, DimRuleIndicatorOperation> indIdMap = indRuleList.stream().collect(Collectors.toMap(DimRuleIndicatorOperation::getIndId, Function.identity()));
            List<IndVO> indVOS = dimTableInfoVo.getIndList()
                    .stream()
                    .filter(ind -> !StringUtils.equals(ind.getIndType(), "GROUP"))
                    .map(ind -> {
                        IndVO indVO = new IndVO();
                        Long indId = Long.valueOf(ind.getId());
                        indVO.setId(indId);
                        indVO.setIndName(ind.getIndName());
                        indVO.setIndType(ind.getIndType());
                        indVO.setIndexNo(ind.getIndexNo());
                        indVO.setParentId(Long.valueOf(ind.getParentId()));
                        indVO.setLevelNumber(ind.getLevelNumber());
                        indVO.setDataFormatId(ind.getDataFormatId());
                        indVO.setFunctionName(ind.getFunctionName());
                        indVO.setFunctionValue(ind.getFunctionValue());
                        indVO.setAvgParmaIndId(ind.getAvgParmaIndId());
                        indVO.setIndicatorOperation(MapUtils.getObject(indIdMap, indId));
                        return indVO;
                    })
                    .collect(Collectors.toList());
            indicatorOperationVO.setIndList(indVOS);
        }

        return indicatorOperationVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DimRuleIndicatorOperation saveIndicatorOperation(IndicatorOperationSaveDTO indicatorOperationSaveDto) {
        DimRuleIndicatorOperation dimRuleIndicatorOperation = new DimRuleIndicatorOperation();
        BeanUtils.copyProperties(indicatorOperationSaveDto, dimRuleIndicatorOperation);
        if (dimRuleIndicatorOperation.getIndicatorOperationType() == IndicatorOperationTypeEnum.EFFECT_SCOPE) {
            // 暂时取消校验
            // 多个规则之间生效范围不允许重复
//            Long dimTableId = dimRuleIndicatorOperation.getDimTableId();
//            LambdaQueryWrapper<DimRuleIndicatorOperation> lambdaQueryWrapper = Wrappers.<DimRuleIndicatorOperation>lambdaQuery()
//                    .eq(DimRuleIndicatorOperation::getDimTableId, dimTableId)
//                    .eq(DimRuleIndicatorOperation::getDimDirectoryId, dimRuleIndicatorOperation.getDimDirectoryId())
//                    .eq(DimRuleIndicatorOperation::getIndicatorOperationType, IndicatorOperationTypeEnum.EFFECT_SCOPE)
//                    .ne(DimRuleIndicatorOperation::getDimRuleId, dimRuleIndicatorOperation.getDimRuleId())
//                    .and(wrapper -> wrapper.isNotNull(DimRuleIndicatorOperation::getEffectScope).ne(DimRuleIndicatorOperation::getEffectScope, ""));
//            List<DimRuleIndicatorOperation> list = dimRuleIndicatorOperationService.list(lambdaQueryWrapper);
//            if (CollectionUtils.isNotEmpty(list)) {
//                boolean flag = false;
//                Long dimRuleId = null;
//                String dimName = null;
//                for (DimRuleIndicatorOperation indicatorOperation : list) {
//                    if (StringUtils.isEmpty(indicatorOperation.getEffectScope())) {
//                        continue;
//                    }
//                    Set<String> effectScopeSet = Arrays.stream(dimRuleIndicatorOperation.getEffectScope().split(",")).collect(Collectors.toSet());
//                    Set<String> splitSet = Arrays.stream(indicatorOperation.getEffectScope().split(",")).collect(Collectors.toSet());
//                    if (effectScopeSet.size() == splitSet.size()) {
//                        effectScopeSet.removeAll(splitSet);
//                        if (CollectionUtils.isEmpty(effectScopeSet)) {
//                            flag = true;
//                            dimName = indicatorOperation.getDimDirectoryName();
//                            dimRuleId = indicatorOperation.getDimRuleId();
//                            break;
//                        }
//                    }
//                }
//                if (flag) {
//                    DimRule dimRule = this.getById(dimRuleId);
//                    throw new CubeRuleDefinitionException("维度【" + dimName + "】的生效范围不能和【" + dimRule.getRuleName() + "】规则重复");
//                }
//            }
            dimRuleIndicatorOperationService.saveOrUpdate(dimRuleIndicatorOperation);
        } else if (dimRuleIndicatorOperation.getIndicatorOperationType() == IndicatorOperationTypeEnum.IND_RULE) {
            // 删除指标引用关系
            List<DimRuleIndicatorRuleRef> dimRuleIndicatorRuleRefs = dimRuleIndicatorRuleRefService.list(Wrappers.<DimRuleIndicatorRuleRef>lambdaQuery()
                    .eq(DimRuleIndicatorRuleRef::getDimRuleId, dimRuleIndicatorOperation.getDimRuleId())
                    .eq(DimRuleIndicatorRuleRef::getDimTableId, dimRuleIndicatorOperation.getDimTableId())
                    .eq(DimRuleIndicatorRuleRef::getIndId, dimRuleIndicatorOperation.getIndId())
            );
            RuleGraph ruleGraph = this.getRuleGraph(dimRuleIndicatorOperation.getDimRuleId());
            for (DimRuleIndicatorRuleRef v : dimRuleIndicatorRuleRefs) {
                CubeRuleVO origin = new CubeRuleVO().setDimTableId(v.getDimTableId()).setIndId(String.valueOf(v.getIndId()));
                CubeRuleVO target = new CubeRuleVO().setDimTableId(v.getRefDimTableId()).setIndId(v.getRefIndId()).setRefTableType(v.getRefTableType()).setRefType(v.getRefType());
                ruleGraph.removeEdge(target, origin);
            }
            // 规则为空
            if (StringUtils.isBlank(dimRuleIndicatorOperation.getRuleExpression())) {
                // 删除指标规则
                dimRuleIndicatorOperationService.remove(Wrappers.<DimRuleIndicatorOperation>lambdaQuery()
                        .eq(DimRuleIndicatorOperation::getIndicatorOperationType, IndicatorOperationTypeEnum.IND_RULE)
                        .eq(DimRuleIndicatorOperation::getIndId, dimRuleIndicatorOperation.getIndId())
                        .eq(DimRuleIndicatorOperation::getDimRuleId, dimRuleIndicatorOperation.getDimRuleId())
                        .eq(DimRuleIndicatorOperation::getDimTableId, dimRuleIndicatorOperation.getDimTableId())
                );
                dimRuleIndicatorRuleRefService.removeByIds(StreamUtils.toSet(dimRuleIndicatorRuleRefs, DimRuleIndicatorRuleRef::getId));
                // 删除规则
                ruleGraphCache.put(RuleConstant.CUBE_CACHE_PREFIX + RuleConstant.RULE_GRAPH + ":" + dimRuleIndicatorOperation.getDimRuleId(), ruleGraph);
                return dimRuleIndicatorOperation;
            }
            // 校验维度生效范围
            List<DimRuleIndicatorOperation> list = dimRuleIndicatorOperationService.list(Wrappers.<DimRuleIndicatorOperation>lambdaQuery()
                    .eq(DimRuleIndicatorOperation::getIndicatorOperationType, IndicatorOperationTypeEnum.EFFECT_SCOPE)
                    .eq(DimRuleIndicatorOperation::getDimRuleId, dimRuleIndicatorOperation.getDimRuleId()));
            // 校验维度是否有值
            Optional<DimRuleIndicatorOperation> optional = list.stream().filter(v -> StringUtils.isEmpty(v.getEffectScope())).findAny();
            if (optional.isPresent()) {
                throw new CubeRuleDefinitionException("维度【" + optional.get().getDimDirectoryName() + "】至少选中一个成员");
            }
            // 判断是否是全部的维度
            DimTableInfoVo dimTableInfoVo = dimTableService.getByIdExt(Math.toIntExact(dimRuleIndicatorOperation.getDimTableId()));
            Assert.notNull(dimTableInfoVo, "未查询到多维表");
            Map<Integer, DimDirectory> dimIdMap = StreamUtils.toMap(dimTableInfoVo.getDimList(), DimDirectory::getId, Function.identity());
            Set<Integer> dimIds1 = new HashSet<>(dimIdMap.keySet());
            Set<Integer> dimIds2 = StreamUtils.toSet(list, v -> Math.toIntExact(v.getDimDirectoryId()));
            dimIds1.removeAll(dimIds2);
            if (CollectionUtils.isNotEmpty(dimIds1)) {
                String dimNames = dimIds1.stream().map(dimId -> MapUtils.getObject(dimIdMap, dimId).getDimDirectoryName()).collect(Collectors.joining(","));
                throw new CubeRuleDefinitionException("维度【" + dimNames + "】没有成员");
            }

            String ruleExpress = ruleService.handleRuleExpress(dimRuleIndicatorOperation.getRuleExpression());
            dimRuleIndicatorOperation.setRuleExpressionInner(ruleExpress);
            dimRuleIndicatorOperationService.saveOrUpdate(dimRuleIndicatorOperation);
            // 删除规则引用
            dimRuleIndicatorRuleRefService.remove(Wrappers.<DimRuleIndicatorRuleRef>lambdaUpdate()
                    .eq(DimRuleIndicatorRuleRef::getDimRuleId, dimRuleIndicatorOperation.getDimRuleId())
                    .eq(DimRuleIndicatorRuleRef::getIndId, dimRuleIndicatorOperation.getIndId()));
            // 保存数据
            Map<Long, Set<String>> dimExpressCacheMap = new HashMap<>();
            CubeRuleVO ruleVO1 = new CubeRuleVO(dimRuleIndicatorOperation.getDimTableId(), dimRuleIndicatorOperation.getIndId());
            // 添加顶点
            ruleGraph.addVertex(ruleVO1);
            // 保存本表引用
            saveExpressORef(dimRuleIndicatorOperation.getDimRuleId(), dimRuleIndicatorOperation.getDimTableId(), dimRuleIndicatorOperation.getIndId(), ruleExpress, ruleGraph, ruleVO1, dimExpressCacheMap, true);
            // 保存他表引用
            saveExpressTRef(dimRuleIndicatorOperation.getDimRuleId(), dimRuleIndicatorOperation.getDimTableId(), dimRuleIndicatorOperation.getIndId(), ruleExpress, ruleGraph, ruleVO1, dimExpressCacheMap, true);
            // 执行规则
            dimRuleExecuteService.doExecuteIndicatorOperations(dimRuleIndicatorOperation.getDimRuleId(), Collections.singletonList(dimRuleIndicatorOperation));
            ruleGraphCache.put(RuleConstant.CUBE_CACHE_PREFIX + RuleConstant.RULE_GRAPH + ":" + dimRuleIndicatorOperation.getDimRuleId(), ruleGraph);
        }
        return dimRuleIndicatorOperation;
    }

    private void saveExpressORef(Long dimRuleId, Long dimTableId, Long indId, String express, RuleGraph ruleGraph, CubeRuleVO ruleVO1, Map<Long, Set<String>> expressCacheMap, boolean saveFlag) {
        Matcher oMatcher = expressOPattern.matcher(express);
        DimTableInfoVo currentTableVo = SpringUtils.getAopProxy(this).getByIdExt(dimTableId);
        Map<String, Integer> currentDimColumnNameCodeMap = CollectionUtils.isNotEmpty(currentTableVo.getDimList()) ? currentTableVo.getDimList().stream().collect(Collectors.toMap(DimDirectory::getDimDirectoryName, DimDirectory::getId)) : Collections.emptyMap();
        Map<String, Integer> currentIndColumnNameCodeMap = CollectionUtils.isNotEmpty(currentTableVo.getIndList()) ? currentTableVo.getIndList().stream().collect(Collectors.toMap(Ind::getIndName, Ind::getId)) : Collections.emptyMap();
        while (oMatcher.find()) {
            String indName = oMatcher.group(1);
            Set<String> strings = expressCacheMap.computeIfAbsent(dimTableId, v -> new HashSet<>());
            if (strings.contains(indName)) {
                continue;
            }
            strings.add(indName);
            boolean indFlag = false;
            String _indId = MapUtils.getString(currentIndColumnNameCodeMap, indName);
            if (_indId == null) {
                _indId = MapUtils.getString(currentDimColumnNameCodeMap, indName);
                if (_indId == null) {
                    throw new CubeRuleDefinitionException("当前表不存在列：" + indName);
                }
            } else {
                indFlag = true;
            }
            String refType = indFlag ? "ind" : "dim";
            CubeRuleVO ruleVO2 = new CubeRuleVO(dimTableId, _indId, refType);
            addEdge(ruleGraph, ruleVO2, ruleVO1);
            if (saveFlag) {
                dimRuleIndicatorRuleRefService.save(new DimRuleIndicatorRuleRef().setDimRuleId(dimRuleId).setDimTableId(dimTableId).setIndId(indId).setRefDimTableId(dimTableId).setRefIndId(_indId).setRefTableType("cube").setRefType(refType));
            }
        }
    }

    private void saveExpressTRef(Long dimRuleId, Long dimTableId, Long indId, String express, RuleGraph ruleGraph, CubeRuleVO ruleVO1, Map<Long, Set<String>> dimExpressCacheMap, boolean saveFlag) {
        Map<String, Map<String, String>> tTableColumnNameCodeMap = new HashMap<>();
        Map<String, Map<String, Integer>> tDimTableIndNameIdMap = new HashMap<>();
        Map<String, Map<String, Integer>> tDimTableDimNameIdMap = new HashMap<>();
        Map<String, Integer> tTableNameIdMap = new HashMap<>();
        Matcher tMatcher = expressTPattern.matcher(express);
        Map<Long, Set<String>> expressCacheMap = new HashMap<>();
        while (tMatcher.find()) {
            String refTableType = tMatcher.group(1);
            if (StringUtils.equals(refTableType, "grid")) {
                // 二维
                String tableName = tMatcher.group(2);
                Integer tTableId = tTableNameIdMap.get(tableName);
                if (tTableId == null) {
                    Table tableByName = ruleService.getTableByName(tableName);
                    if (tableByName == null) {
                        throw new CubeRuleDefinitionException("未找到二维表：" + tableName + "！");
                    }
                    tTableId = tableByName.getId();
                    tTableNameIdMap.put(tableName, tTableId);
                }
                String columnName = tMatcher.group(3);
                Set<String> strings = expressCacheMap.computeIfAbsent(Long.valueOf(tTableId), v -> new HashSet<>());
                if (strings.contains(columnName)) {
                    continue;
                }
                strings.add(columnName);
                TableVo tableVoById = null;
                Map<String, String> columnNameCodeMap = tTableColumnNameCodeMap.get(tableName);
                if (columnNameCodeMap == null) {
                    tableVoById = ruleService.getTableVoById(tTableId);
                    if (tableVoById == null) {
                        throw new CubeRuleDefinitionException("未找到二维表：" + tableName + "！");
                    }
                    columnNameCodeMap = CollectionUtils.isNotEmpty(tableVoById.getTableMetaJson()) ? tableVoById.getTableMetaJson().stream().collect(Collectors.toMap(TableMetaJson::getName, TableMetaJson::getCode)) : Collections.emptyMap();
                    tTableColumnNameCodeMap.put(tableName, columnNameCodeMap);
                }
                Map tableColumnNameCodeMap = MapUtils.getMap(tTableColumnNameCodeMap, tableName);
                String tColumnCode = MapUtils.getString(tableColumnNameCodeMap, columnName);
                if (StringUtils.isEmpty(tColumnCode)) {
                    throw new CubeRuleDefinitionException("二维表：" + tableName + "不存在列：" + columnName);
                }
                CubeRuleVO ruleVO2 = new CubeRuleVO(tTableId, tColumnCode, refTableType, "");
                addEdge(ruleGraph, ruleVO2, ruleVO1);
                if (saveFlag) {
                    dimRuleIndicatorRuleRefService.save(new DimRuleIndicatorRuleRef().setDimRuleId(dimRuleId).setDimTableId(dimTableId).setIndId(indId).setRefDimTableId(Long.valueOf(tTableId)).setRefIndId(tColumnCode).setRefTableType(refTableType));
                }
            } else {
                // 多维
                String tableName = tMatcher.group(2);
                DimTableInfoVo tableByName = SpringUtils.getAopProxy(this).getDimTableByName(tableName);
                if (tableByName == null) {
                    throw new CubeRuleDefinitionException("未找到多维表：" + tableName);
                }
                Integer tTableId = tableByName.getId();
                String indName = tMatcher.group(3);
                Set<String> strings = dimExpressCacheMap.computeIfAbsent(Long.valueOf(tTableId), v -> new HashSet<>());
                if (strings.contains(indName)) {
                    continue;
                }
                strings.add(indName);
                Map<String, Integer> indNameIdMap = tDimTableIndNameIdMap.get(tableName);
                if (indNameIdMap == null) {
                    indNameIdMap = CollectionUtils.isNotEmpty(tableByName.getIndList()) ? tableByName.getIndList().stream().collect(Collectors.toMap(Ind::getIndName, Ind::getId)) : Collections.emptyMap();
                    tDimTableIndNameIdMap.put(tableName, indNameIdMap);
                }
                Map<String, Integer> dimNameIdMap = tDimTableDimNameIdMap.get(tableName);
                if (dimNameIdMap == null) {
                    dimNameIdMap = CollectionUtils.isNotEmpty(tableByName.getDimList()) ? tableByName.getDimList().stream().collect(Collectors.toMap(DimDirectory::getDimDirectoryName, DimDirectory::getId)) : Collections.emptyMap();
                    tDimTableDimNameIdMap.put(tableName, dimNameIdMap);
                }
                boolean indFlag = false;
                String _indId = MapUtils.getString(indNameIdMap, indName);
                if (StringUtils.isEmpty(_indId)) {
                    _indId = MapUtils.getString(dimNameIdMap, indName);
                    if (_indId == null) {
                        throw new CubeRuleDefinitionException("多维表【" + tableName + "】不存在列：" + indName);
                    }
                } else {
                    indFlag = true;
                }
                String refType = indFlag ? "ind" : "dim";
                CubeRuleVO ruleVO2 = new CubeRuleVO(tTableId, _indId, refTableType, refType);
                addEdge(ruleGraph, ruleVO2, ruleVO1);
                if (saveFlag) {
                    dimRuleIndicatorRuleRefService.save(new DimRuleIndicatorRuleRef().setDimRuleId(dimRuleId).setDimTableId(dimTableId).setIndId(indId).setRefDimTableId((long) tTableId).setRefIndId(_indId).setRefTableType(refTableType).setRefType(refType));
                }
            }
        }
    }

    @Override
    @ThreadLocalCache(value = ThreadLocalCacheType.TTL)
    public DimTableInfoVo getDimTableByName(String tableName) {
        return dimTableService.getByTableName(tableName);
    }

    @Override
    @ThreadLocalCache(value = ThreadLocalCacheType.TTL)
    public CubeRuleVO parse(String string) {
        return JSON.parseObject(string, CubeRuleVO.class);
    }

    @Override
    public void execute(Integer dimTableId) {
        List<DimTableDataCellDto> parentCellList = new ArrayList<>();
        execute(dimTableId, parentCellList);
        if (CollectionUtils.isNotEmpty(parentCellList)) {
            DimTableInfoVo tableInfoVo = SpringUtils.getAopProxy(this).getByIdExt(Long.valueOf(dimTableId));
            List<String> dimIdList = tableInfoVo.getDimList().stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
            List<String> indIdList = tableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(x -> String.valueOf(x.getId())).collect(Collectors.toList());

            CubeMetaData cubeMetaData = new CubeMetaData(tableInfoVo.getId().toString(), dimIdList, indIdList);
            CubeServer cubeServer = CubeSchema.get().getOrCreateTable(cubeMetaData);

            List<DimInstance> dimInstanceList = dimInstanceService.listByDimDirectoryIds(dimIdList);
            Map<String, List<DimInstance>> toChildMap = dimInstanceList.stream().collect(Collectors.groupingBy(x -> x.getDimDirectoryId() + ":" + x.getParentId()));
            Map<String, Integer> toParentMap = ThreadLocalUtils.getTtl(OPCTOperator.class, CubeRuleConstant.TO_PARENT_MAP + tableInfoVo.getId());
            if (toParentMap == null) {
                toParentMap = dimInstanceList.stream().filter(x -> x.getParentId() != null && x.getParentId() > 0).collect(Collectors.toMap(x -> x.getDimDirectoryId() + ":" + x.getId(), DimInstance::getParentId));
                ThreadLocalUtils.setTtl(OPCTOperator.class, CubeRuleConstant.TO_PARENT_MAP + tableInfoVo.getId(), toParentMap);
            }
            Map<Integer, Ind> indMap = tableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).collect(Collectors.toMap(Ind::getId, Function.identity()));

            Map<String, Double> parentSumCache = new HashMap<>(); // 缓存父节点的sum结果
            Queue<DimTableDataDto> queue = new LinkedList<>();
            DimTableDataDto parentDimTableDataDto = new DimTableDataDto();
            parentDimTableDataDto.setTableId(dimTableId);
            parentDimTableDataDto.setDimTableDataCellList(parentCellList);
            queue.offer(parentDimTableDataDto);
            while (!queue.isEmpty()) {
                DimTableDataDto currentDto = queue.poll();
                List<DimTableDataCellDto> parentCellList2 = new ArrayList<>();
                for (DimTableDataCellDto cellDto : currentDto.getDimTableDataCellList()) {
                    if (CollectionUtil.isEmpty(cellDto.getRelDimIdList()) && cellDto.getRelDimIdList().size() != dimIdList.size()) {
                        log.error("数据关联维度ID参数错误。{}", cellDto.getRelDimIdList());
                        throw new ServiceException("数据关联维度ID参数错误。");
                    }
                    if (cellDto.getOriginalValue() == null && !"delete".equals(cellDto.getOpType())) {
                        continue;
                    }
                    Map<String, List<String>> filterDims = new HashMap<>();
                    Ind ind = indMap.get(Integer.valueOf(cellDto.getIndId()));
                    boolean notSummarizeFlag = StringUtils.equals(BizConstants.NOT_SUMMARIZE, ind.getFunctionName());
                    String[] dimOfFact = new String[cubeServer.factTable.meta.getDimColumnNames().size()];
                    for (int i = 0, size = cellDto.getRelDimIdList().size(); i < size; i++) {
                        String dimId = cellDto.getRelDimIdList().get(i).toString();
                        int index = cubeServer.factTable.getDimIndex(dimId);
                        dimOfFact[index] = cellDto.getDimInstanceIdList().get(i);
                        if (dimId.equals(cellDto.getParentDimId())) {
                            filterDims.put(dimId, toChildMap.get(dimId + ":" + dimOfFact[index]).stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList()));
                        } else {
                            filterDims.put(dimId, Collections.singletonList(cellDto.getDimInstanceIdList().get(i)));
                        }
                        if (!notSummarizeFlag && toParentMap.containsKey(dimId + ":" + dimOfFact[index])) {
                            DimTableDataCellDto dto = new DimTableDataCellDto();
                            dto.setRelDimIdList(new ArrayList<>(cellDto.getRelDimIdList()));
                            dto.setDimInstanceIdList(new ArrayList<>(cellDto.getDimInstanceIdList()));
                            dto.setOpType("add");
                            dto.setIndId(cellDto.getIndId());
                            dto.setOriginalValue((double) 0);
                            dto.setParentDimId(dimId);
                            dto.setParentDimInstanceId(toParentMap.get(dimId + ":" + dimOfFact[index]).toString());
                            dto.getDimInstanceIdList().set(i, toParentMap.get(dimId + ":" + dimOfFact[index]).toString());
                            parentCellList2.add(dto);
                        }
                    }
                    double sumVal = 0L;
                    if (!notSummarizeFlag) {
                        String cacheKey = filterDims + "-" + cellDto.getIndId();
                        if (parentSumCache.containsKey(cacheKey)) {
                            sumVal = parentSumCache.get(cacheKey);
                        } else {
                            List<FactTable.Record> recordList = cubeServer.filterRecord(filterDims);
                            sumVal = dimTableService.getSummaryInd(cubeServer, recordList, ind, filterDims.get(cellDto.getParentDimId()).size());
                            parentSumCache.put(cacheKey, sumVal);
                        }
                    }
                    filterDims.put(cellDto.getParentDimId(), Collections.singletonList(cellDto.getParentDimInstanceId()));
                    FactTable.Record record = cubeServer.hitRecordWithInstance(filterDims);
                    if (record == null) {
                        DoubleDouble[] indOfFact = new DoubleDouble[cubeServer.factTable.meta.getIndColumnNames().size()];
                        int index = cubeServer.factTable.getIndIndex(cellDto.getIndId());
                        if (!notSummarizeFlag) {
                            indOfFact[index] = new DoubleDouble(sumVal);
                        } else {
                            indOfFact[index] = new DoubleDouble(Double.parseDouble(cellDto.getOriginalValue().toString()));
                        }
                        record = cubeServer.factTable.new Record(CubeSchema.get().getTableNextKey(tableInfoVo.getId().toString()));
                        record.setDimOfFact(dimOfFact);
                        record.setIndOfFact(indOfFact);
                        CubeSchema.get().addTableData(cubeServer, record);
                    } else {
                        DoubleDouble[] indOfFact = record.getIndOfFact();
                        int index = cubeServer.factTable.getIndIndex(cellDto.getIndId());
                        if ("delete".equals(cellDto.getOpType())) {
                            indOfFact[index] = new DoubleDouble();
                        } else {
                            if (!notSummarizeFlag) {
                                indOfFact[index] = new DoubleDouble(sumVal);
                            } else {
                                indOfFact[index] = new DoubleDouble(cellDto.getOriginalValue());
                            }
                        }
//                    record.setIndOfFact(indOfFact);
//                    CubeSchema.get().updateTableData(cubeServer, record);
                    }
                }
                if (CollectionUtil.isNotEmpty(parentCellList2)) {
                    parentDimTableDataDto = new DimTableDataDto();
                    parentDimTableDataDto.setTableId(dimTableId);
                    parentDimTableDataDto.setDimTableDataCellList(parentCellList2);
                    queue.offer(parentDimTableDataDto);
                }
            }
        }
    }

    @Override
    public void execute(Integer dimTableId, List<DimTableDataCellDto> parentCellList) {
        // 查询当前表的规则
        List<DimRule> dimRules = SpringUtils.getAopProxy(this).listByDimTableId(Long.valueOf(dimTableId));
        // 规则排序
        Map<Long, Integer> rulePositionMap = StreamUtils.toMap(dimRules, DimRule::getId, DimRule::getPosition);
        List<DimRuleIndicatorOperation> indRules = dimRuleIndicatorOperationService.listByDimTableIdAndIndicatorOperationType(dimTableId, IndicatorOperationTypeEnum.IND_RULE);
        if (CollectionUtils.isNotEmpty(indRules)) {
            // 排序
            indRules = indRules.stream().sorted(Comparator.comparingInt(v -> MapUtils.getInteger(rulePositionMap, v.getDimRuleId()))).collect(Collectors.toList());
            Map<Long, List<DimRuleIndicatorOperation>> ruleIdMap = new LinkedHashMap<>();
            for (DimRuleIndicatorOperation dimRuleIndicatorOperation : indRules) {
                Long ruleId = dimRuleIndicatorOperation.getDimRuleId();
                List<DimRuleIndicatorOperation> indicatorOperations = ruleIdMap.computeIfAbsent(ruleId, k -> new ArrayList<>());
                indicatorOperations.add(dimRuleIndicatorOperation);
            }
            String dimTableIdString = String.valueOf(dimTableId);
            for (Map.Entry<Long, List<DimRuleIndicatorOperation>> entry : ruleIdMap.entrySet()) {
                List<DimRuleIndicatorOperation> executeList = new ArrayList<>();
                Map<Long, DimRuleIndicatorOperation> indRuleMap = StreamUtils.toMap(entry.getValue(), DimRuleIndicatorOperation::getIndId, Function.identity());
                Map<Integer, Map<String, List<String>>> ruleRefMap = ExpressUtils.collectCubeRuleRef(entry.getKey());
                for (Map.Entry<Integer, Map<String, List<String>>> ruleRefEntry : ruleRefMap.entrySet()) {
                    Map<String, List<String>> value = ruleRefEntry.getValue();
                    for (Map.Entry<String, List<String>> valueEntry : value.entrySet()) {
                        String[] strings = valueEntry.getKey().split(",");
                        if (StringUtils.equals(strings[1], "cube")) {
                            if (StringUtils.equals(strings[0], dimTableIdString)) {
                                List<String> list = valueEntry.getValue();
                                for (String ind : list) {
                                    String[] split = ind.split(",");
                                    if (StringUtils.equals(split[1], "ind")) {
                                        DimRuleIndicatorOperation indicatorOperation = MapUtils.getObject(indRuleMap, Long.valueOf(split[0]));
                                        if (indicatorOperation != null) {
                                            executeList.add(indicatorOperation);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                dimRuleExecuteService.doExecuteIndicatorOperations(entry.getKey(), executeList, parentCellList);
            }
        }
    }

    @Override
    public void initDimValue(Long dimTableId, List<DimRuleIndicatorOperation> indicatorOperations) {
        CubeServer cubeServer = CubeSchema.get().getTable(String.valueOf(dimTableId));
        if (cubeServer == null) {
            DimTableInfoVo dimTableInfoVo = this.getByIdExt(dimTableId);
            List<String> dimIdList = dimTableInfoVo.getDimList().stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
            List<String> indIdList = dimTableInfoVo.getIndList().stream().filter(x -> "INSTANCE".equals(x.getIndType())).map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
            CubeMetaData cubeMetaData = new CubeMetaData(dimTableInfoVo.getId().toString(), dimIdList, indIdList);
            cubeServer = CubeSchema.get().getOrCreateTable(cubeMetaData);
        }
        Map<String, List<String>> filterDims = indicatorOperations.stream()
                .collect(Collectors.toMap(v -> String.valueOf(v.getDimDirectoryId()), v -> Arrays.asList(v.getEffectScope().split(",")), (l, f) -> l, LinkedHashMap::new));
        List<Map<String, List<String>>> writerFilterDimsList = generateAllWriterFilterDims(filterDims);
        for (Map<String, List<String>> writerFilterDims : writerFilterDimsList) {
            //默认赋值
            List<String> relDimIdList = new ArrayList<>(writerFilterDims.keySet());
            // 新增
            FactTable.Record hitFactTableRecord = cubeServer.hitRecordWithInstance(writerFilterDims);
            if (hitFactTableRecord == null) {
                DoubleDouble[] indOfFact = new DoubleDouble[cubeServer.factTable.meta.getIndColumnNames().size()];
//                for (int i = 0, len = indOfFact.length; i < len; i++) {
//                    indOfFact[i] = new DoubleDouble();
//                }
                hitFactTableRecord = cubeServer.factTable.new Record(CubeSchema.get().getTableNextKey(String.valueOf(dimTableId)));
                String[] dimOfFact = new String[cubeServer.factTable.meta.getDimColumnNames().size()];
                for (String dimId : relDimIdList) {
                    int dimIndex = cubeServer.factTable.getDimIndex(dimId);
                    dimOfFact[dimIndex] = writerFilterDims.get(dimId).get(0);
                }
                hitFactTableRecord.setDimOfFact(dimOfFact);
                hitFactTableRecord.setIndOfFact(indOfFact);
                CubeSchema.get().addTableData(cubeServer, hitFactTableRecord);
            }
        }
//        int threadSize = sysConfigService.getRuleExecuteThreadSize();
//        int batchSize = Math.max(writerFilterDimsList.size() / threadSize, 1); // 动态计算批处理大小，确保至少有一个元素
//        List<List<Map<String, List<String>>>> partitions = Lists.partition(writerFilterDimsList, batchSize);
//        List<CompletableFuture<Void>> futures = new ArrayList<>(partitions.size());
//        for (List<Map<String, List<String>>> batch : partitions) {
//            final CubeServer finalCubeServer = cubeServer;
//            CompletableFuture<Void> future = CompletableFuture.runAsync(TtlRunnable.get(() -> {
//                for (Map<String, List<String>> writerFilterDims : batch) {
//                    //默认赋值
//                    List<String> relDimIdList = new ArrayList<>(writerFilterDims.keySet());
//                    // 新增
//                    FactTable.Record hitFactTableRecord = finalCubeServer.hitRecordWithInstance(writerFilterDims);
//                    if (hitFactTableRecord == null) {
//                        DoubleDouble[] indOfFact = new DoubleDouble[finalCubeServer.factTable.meta.getIndColumnNames().size()];
//                        hitFactTableRecord = finalCubeServer.factTable.new Record(CubeSchema.get().getTableNextKey(String.valueOf(dimTableId)));
//                        String[] dimOfFact = new String[finalCubeServer.factTable.meta.getDimColumnNames().size()];
//                        for (String dimId : relDimIdList) {
//                            int dimIndex = finalCubeServer.factTable.getDimIndex(dimId);
//                            dimOfFact[dimIndex] = writerFilterDims.get(dimId).get(0);
//                        }
//                        hitFactTableRecord.setDimOfFact(dimOfFact);
//                        hitFactTableRecord.setIndOfFact(indOfFact);
//                        CubeSchema.get().addTableData(finalCubeServer, hitFactTableRecord);
//                    }
//                }
//            }), cubeTaskExecutorCallerRun);
//            futures.add(future);
//        }
//
//        // 等待所有任务完成
//        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    @Override
    public boolean validateRule(Long dimTableId, Long dimRuleId, Long indId, String express) {
        RuleGraph ruleGraph = this.getRuleGraph(dimRuleId);
        Map<Long, Set<String>> dimExpressCacheMap = new HashMap<>();
        CubeRuleVO ruleVO1 = new CubeRuleVO(dimTableId, indId);
        saveExpressORef(dimRuleId, dimTableId, indId, express, ruleGraph, ruleVO1, dimExpressCacheMap, false);
        saveExpressTRef(dimRuleId, dimTableId, indId, express, ruleGraph, ruleVO1, dimExpressCacheMap, false);
        return true;
    }

    @Override
    @ThreadLocalCache(value = ThreadLocalCacheType.TTL)
    public List<DimRule> listByDimTableId(Long dimTableId) {
        return this.list(Wrappers.<DimRule>lambdaQuery().eq(DimRule::getDimTableId, dimTableId));
    }

    @Override
    @ThreadLocalCache(value = ThreadLocalCacheType.TTL)
    public DimRule getByRuleId(Long ruleId) {
        return this.getById(ruleId);
    }

    private static void addEdge(RuleGraph ruleGraph, CubeRuleVO ruleVO1, CubeRuleVO ruleVO2) {
        if (!ruleGraph.hasEdge(ruleVO1, ruleVO2)) {
            ruleGraph.addVertex(ruleVO1);
            ruleGraph.addVertex(ruleVO2);
            boolean addEdgeFlag = ruleGraph.addEdge(ruleVO1, ruleVO2);
            if (!addEdgeFlag) {
                throw new RuleExecutionException("规则出现循环引用，请检查！");
            }
        }
    }

    @Override
    public List<DimInstanceVO> queryIndicatorOperationDimInstance(IndicatorOperationDimInstanceQueryDTO indicatorOperationDimInstanceQueryDto) {
        DimRule dimRule = this.getById(indicatorOperationDimInstanceQueryDto.getDimRuleId());
        Assert.notNull(dimRule, "未查询到规则");
        DimRuleIndicatorOperation dimRuleIndicatorOperation = dimRuleIndicatorOperationService.getOne(Wrappers.<DimRuleIndicatorOperation>lambdaQuery()
                .eq(DimRuleIndicatorOperation::getDimRuleId, dimRule.getId())
                .eq(DimRuleIndicatorOperation::getDimDirectoryId, indicatorOperationDimInstanceQueryDto.getDimDirectoryId())
                .last("limit 1")
        );
        InstanceTreeQuery instanceTreeQuery = new InstanceTreeQuery();
        instanceTreeQuery.setDimDirectoryId(Math.toIntExact(indicatorOperationDimInstanceQueryDto.getDimDirectoryId()));
        List<DimInstance> instanceList = instanceService.list(instanceTreeQuery);
        List<DimInstanceVO> dimInstanceVOList = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(instanceList)) {
            Set<String> dimIds;
            if (dimRuleIndicatorOperation != null && StringUtils.isNotEmpty(dimRuleIndicatorOperation.getEffectScope())) {
                dimIds = Arrays.stream(dimRuleIndicatorOperation.getEffectScope().split(",")).collect(Collectors.toSet());
            } else {
                dimIds = Collections.emptySet();
            }

            dimInstanceVOList = instanceList.stream()
                    .map(dimInstance -> {
                        DimInstanceVO dimInstanceVO = new DimInstanceVO();
                        dimInstanceVO.setId(Long.valueOf(dimInstance.getId()));
                        dimInstanceVO.setDimDirectoryId(Long.valueOf(dimInstance.getDimDirectoryId()));
                        dimInstanceVO.setDimName(dimInstance.getDimName());
                        dimInstanceVO.setDimCode(dimInstance.getDimCode());
                        dimInstanceVO.setIndexNo(dimInstance.getIndexNo());
                        dimInstanceVO.setParentId(Long.valueOf(dimInstance.getParentId()));
                        dimInstanceVO.setAncestors(dimInstance.getAncestors());
                        dimInstanceVO.setIsLeaf(dimInstance.getIsLeaf());
                        if (dimIds.contains(String.valueOf(dimInstance.getId()))) {
                            dimInstanceVO.setChecked(true);
                        }
                        return dimInstanceVO;
                    })
                    .collect(Collectors.toList());
        }
        return dimInstanceVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        dimRuleIndicatorOperationService.remove(Wrappers.<DimRuleIndicatorOperation>lambdaUpdate().eq(DimRuleIndicatorOperation::getDimRuleId, id));
        boolean b = this.removeById(id);
        ruleGraphCache.invalidate(RuleConstant.CUBE_CACHE_PREFIX + RuleConstant.RULE_GRAPH + ":" + id);
        return b;
    }

    @Override
    public Map<String, Boolean> getIndHasRule(Collection<? extends Serializable> indIds) {
        if (CollectionUtils.isEmpty(indIds)) {
            return Collections.emptyMap();
        }
        QueryWrapper<DimRuleIndicatorOperation> query = Wrappers.query();
        query.select("ind_id", "count(*) as cnt");
        query.in("ind_id", indIds);
        query.eq("indicator_operation_type", IndicatorOperationTypeEnum.IND_RULE);
        query.groupBy("ind_id");
        Map<String, Long> indCntMap = dimRuleIndicatorOperationMapper.selectMaps(query).stream().collect(Collectors.toMap(v -> MapUtils.getString(v, "ind_id"), v -> MapUtils.getLong(v, "cnt")));
        Map<String, Boolean> map = Maps.newHashMapWithExpectedSize(indIds.size());
        for (Object o : indIds) {
            String s = String.valueOf(o);
            map.put(s, MapUtils.getLongValue(indCntMap, s) > 0);
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveIndicatorOperationEffectScopes(IndicatorOperationEffectScopeBatchSaveDTO indicatorOperationEffectScopeBatchSaveDto) {
        if (indicatorOperationEffectScopeBatchSaveDto == null || CollectionUtils.isEmpty(indicatorOperationEffectScopeBatchSaveDto.getEffectScopes())) {
            return Boolean.FALSE;
        }
        List<IndicatorOperationEffectScopeSaveDTO> effectScopes = indicatorOperationEffectScopeBatchSaveDto.getEffectScopes();
        // 校验维度是否有值
        Optional<IndicatorOperationEffectScopeSaveDTO> optional = effectScopes.stream().filter(v -> StringUtils.isEmpty(v.getEffectScope())).findAny();
        if (optional.isPresent()) {
            throw new CubeRuleDefinitionException("维度【" + optional.get().getDimDirectoryName() + "】至少选中一个成员");
        }
        // 判断是否是全部的维度
        DimTableInfoVo dimTableInfoVo = dimTableService.getByIdExt(Math.toIntExact(indicatorOperationEffectScopeBatchSaveDto.getDimTableId()));
        Assert.notNull(dimTableInfoVo, "未查询到多维表");
        Map<Integer, DimDirectory> dimIdMap = StreamUtils.toMap(dimTableInfoVo.getDimList(), DimDirectory::getId, Function.identity());
        Set<Integer> dimIds1 = new HashSet<>(dimIdMap.keySet());
        Set<Integer> dimIds2 = StreamUtils.toSet(effectScopes, v -> Math.toIntExact(v.getDimDirectoryId()));
        dimIds1.removeAll(dimIds2);
        if (CollectionUtils.isNotEmpty(dimIds1)) {
            String dimNames = dimIds1.stream().map(dimId -> MapUtils.getObject(dimIdMap, dimId).getDimDirectoryName()).collect(Collectors.joining(","));
            throw new CubeRuleDefinitionException("维度【" + dimNames + "】没有成员");
        }
        if (StringUtils.isNotEmpty(indicatorOperationEffectScopeBatchSaveDto.getInitDimValue())) {
            DimRule dimRule = new DimRule();
            dimRule.setId(indicatorOperationEffectScopeBatchSaveDto.getDimRuleId());
            dimRule.setInitDimValue(indicatorOperationEffectScopeBatchSaveDto.getInitDimValue());
            this.updateById(dimRule);
        }
        // 删除指标的生效范围
        dimRuleIndicatorOperationService.remove(Wrappers.<DimRuleIndicatorOperation>lambdaQuery()
                .eq(DimRuleIndicatorOperation::getDimRuleId, indicatorOperationEffectScopeBatchSaveDto.getDimRuleId())
                .eq(DimRuleIndicatorOperation::getIndicatorOperationType, IndicatorOperationTypeEnum.EFFECT_SCOPE)
        );
        // 保存指标生效范围
        Long dimTableId = indicatorOperationEffectScopeBatchSaveDto.getDimTableId();
        Long dimRuleId = indicatorOperationEffectScopeBatchSaveDto.getDimRuleId();
        // 暂时去掉校验
//        LambdaQueryWrapper<DimRuleIndicatorOperation> lambdaQueryWrapper = Wrappers.<DimRuleIndicatorOperation>lambdaQuery()
//                .eq(DimRuleIndicatorOperation::getDimTableId, dimTableId)
//                .in(DimRuleIndicatorOperation::getDimDirectoryId, dimIdMap.keySet())
//                .eq(DimRuleIndicatorOperation::getIndicatorOperationType, IndicatorOperationTypeEnum.EFFECT_SCOPE)
//                .ne(DimRuleIndicatorOperation::getDimRuleId, indicatorOperationEffectScopeBatchSaveDto.getDimRuleId())
//                .and(wrapper -> wrapper.isNotNull(DimRuleIndicatorOperation::getEffectScope).ne(DimRuleIndicatorOperation::getEffectScope, ""));
//        List<DimRuleIndicatorOperation> list = dimRuleIndicatorOperationService.list(lambdaQueryWrapper);
//        if (CollectionUtils.isNotEmpty(list)) {
//            Map<Long, List<DimRuleIndicatorOperation>> directoryIdMap = StreamUtils.groupByKey(list, DimRuleIndicatorOperation::getDimDirectoryId);
//            for (IndicatorOperationEffectScopeSaveDto effectScopeSaveDto : effectScopes) {
//                Long dimDirectoryId = effectScopeSaveDto.getDimDirectoryId();
//                List<DimRuleIndicatorOperation> indicatorOperations = MapUtils.getObject(directoryIdMap, dimDirectoryId, Collections.emptyList());
//                if (CollectionUtils.isNotEmpty(indicatorOperations)) {
//                    // 多个规则之间生效范围不允许重复
//                    boolean flag = false;
//                    Long _dimRuleId = null;
//                    String dimName = null;
//                    for (DimRuleIndicatorOperation indicatorOperation : indicatorOperations) {
//                        if (StringUtils.isEmpty(indicatorOperation.getEffectScope())) {
//                            continue;
//                        }
//                        Set<String> effectScopeSet = Arrays.stream(indicatorOperation.getEffectScope().split(",")).collect(Collectors.toSet());
//                        Set<String> splitSet = Arrays.stream(effectScopeSaveDto.getEffectScope().split(",")).collect(Collectors.toSet());
//                        if (effectScopeSet.size() == splitSet.size()) {
//                            effectScopeSet.removeAll(splitSet);
//                            if (CollectionUtils.isEmpty(effectScopeSet)) {
//                                flag = true;
//                                dimName = indicatorOperation.getDimDirectoryName();
//                                _dimRuleId = indicatorOperation.getDimRuleId();
//                                break;
//                            }
//                        }
//                    }
//                    if (flag) {
//                        DimRule dimRule = this.getById(_dimRuleId);
//                        throw new CubeRuleDefinitionException("维度【" + dimName + "】的生效范围不能和【" + dimRule.getRuleName() + "】规则重复");
//                    }
//                }
//            }
//        }
        List<DimRuleIndicatorOperation> dimRuleIndicatorOperations = effectScopes.stream().map(indicatorOperationEffectScopeSaveDTO -> {
            DimRuleIndicatorOperation dimRuleIndicatorOperation = new DimRuleIndicatorOperation();
            dimRuleIndicatorOperation.setDimTableId(dimTableId);
            dimRuleIndicatorOperation.setDimRuleId(dimRuleId);
            dimRuleIndicatorOperation.setDimDirectoryId(indicatorOperationEffectScopeSaveDTO.getDimDirectoryId());
            dimRuleIndicatorOperation.setDimDirectoryName(indicatorOperationEffectScopeSaveDTO.getDimDirectoryName());
            dimRuleIndicatorOperation.setEffectScope(indicatorOperationEffectScopeSaveDTO.getEffectScope());
            dimRuleIndicatorOperation.setIndicatorOperationType(IndicatorOperationTypeEnum.EFFECT_SCOPE);
            return dimRuleIndicatorOperation;
        }).collect(Collectors.toList());
        dimRuleIndicatorOperationService.saveBatch(dimRuleIndicatorOperations);
        // 保存后执行规则（异步）
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    dimRuleServiceHelperService.initDimValueAndExecute(dimTableId, indicatorOperationEffectScopeBatchSaveDto.getInitDimValue(), dimRuleIndicatorOperations);
                }
            });
        } else {
            cubeTaskExecutorAbort.execute(TtlRunnable.get(() -> {
                dimRuleServiceHelperService.initDimValueAndExecute(dimTableId, indicatorOperationEffectScopeBatchSaveDto.getInitDimValue(), dimRuleIndicatorOperations);
            }));
        }
        return Boolean.TRUE;
    }

    @Override
    public RuleGraph getRuleGraph(Long dimRuleId) {
        //查出全部的引用关系组装ruleGraph
        RuleGraph ruleGraph = ruleGraphCache.getIfPresent(RuleConstant.CUBE_CACHE_PREFIX + RuleConstant.RULE_GRAPH + ":" + dimRuleId);
        if (ruleGraph == null) {
            // 从数据库读取规则引用关系组装ruleGraph
            ruleGraph = RuleGraph.create();
            List<DimRuleIndicatorRuleRef> rules = dimRuleIndicatorRuleRefService.list(Wrappers.<DimRuleIndicatorRuleRef>lambdaQuery().eq(DimRuleIndicatorRuleRef::getDimRuleId, dimRuleId));
            if (CollectionUtils.isNotEmpty(rules)) {
                Map<String, Map<String, Map<String, CubeRuleVO>>> ruleMap = new HashMap<>();
                List<CubeRuleVO> ruleVOS = rules.stream().map(v -> new CubeRuleVO().setDimTableId(v.getDimTableId()).setIndId(String.valueOf(v.getIndId()))).collect(Collectors.toList());
                List<CubeRuleVO> refRuleVOS = rules.stream().map(v -> new CubeRuleVO().setDimTableId(v.getRefDimTableId()).setIndId(v.getRefIndId()).setRefTableType(v.getRefTableType()).setRefType(v.getRefType())).collect(Collectors.toList());
                for (CubeRuleVO ruleVO : ruleVOS) {
                    Map<String, Map<String, CubeRuleVO>> stringMapMap = ruleMap.computeIfAbsent("cube", k -> new HashMap<>());
                    Map<String, CubeRuleVO> stringRuleMap = stringMapMap.computeIfAbsent(String.valueOf(ruleVO.getDimTableId()), k -> new HashMap<>());
                    stringRuleMap.putIfAbsent(ruleVO.getIndId(), ruleVO);
                }
                for (CubeRuleVO ruleVO : refRuleVOS) {
                    Map<String, Map<String, CubeRuleVO>> stringMapMap = ruleMap.computeIfAbsent(ruleVO.getRefTableType(), k -> new HashMap<>());
                    Map<String, CubeRuleVO> stringRuleMap = stringMapMap.computeIfAbsent(String.valueOf(ruleVO.getDimTableId()), k -> new HashMap<>());
                    stringRuleMap.putIfAbsent(ruleVO.getIndId(), ruleVO);
                }
                Set<CubeRuleVO> vertexSet = Sets.newHashSetWithExpectedSize(rules.size());
                for (DimRuleIndicatorRuleRef rule : rules) {
                    CubeRuleVO ruleVO = ruleMap.get("cube").get(String.valueOf(rule.getDimTableId())).get(String.valueOf(rule.getIndId()));
                    vertexSet.add(ruleVO);
                }
                for (CubeRuleVO ruleVO : vertexSet) {
                    ruleGraph.addVertex(ruleVO);
                }
                for (DimRuleIndicatorRuleRef rule : rules) {
                    boolean flag = ruleGraph.addEdge(ruleMap.get(rule.getRefTableType()).get(String.valueOf(rule.getRefDimTableId())).get(rule.getRefIndId()), ruleMap.get("cube").get(String.valueOf(rule.getDimTableId())).get(String.valueOf(rule.getIndId())));
                    if (!flag) {
                        throw new RuleExecutionException("规则引用出现循环，请检查！");
                    }
                }
            }
            ruleGraphCache.put(RuleConstant.CUBE_CACHE_PREFIX + RuleConstant.RULE_GRAPH + ":" + dimRuleId, ruleGraph);
        }

        return ruleGraph;
    }

    @Override
    @ThreadLocalCache(value = ThreadLocalCacheType.TTL)
    public DimTableInfoVo getByIdExt(Long dimTableId) {
        return dimTableService.getByIdExt(Math.toIntExact(dimTableId));
    }

    private void swapSortOrder(DimRule dimRule1, DimRule dimRule2) {
        Integer tempOrder = dimRule1.getPosition();
        dimRule1.setPosition(dimRule2.getPosition());
        dimRule2.setPosition(tempOrder);
        this.baseMapper.updateById(dimRule1);
        this.baseMapper.updateById(dimRule2);
    }

    @Override
    public List<DimRuleVariableVO> listDimVariable(Long dimRuleId) {
        List<DimRuleVariable> list = dimRuleVariableService.list(Wrappers.<DimRuleVariable>lambdaQuery().eq(DimRuleVariable::getDimRuleId, dimRuleId).orderByAsc(DimRuleVariable::getCreateTime));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return BeanUtil.copyToList(list, DimRuleVariableVO.class);
    }

    @Override
    public List<DimRuleDimVO> getDimRuleDims(Long tableId, Long dimVariableId) {
        DimTableInfoVo dimTableInfoVo = this.getByIdExt(tableId);
        Assert.notNull(dimTableInfoVo, "未查询到数方");
        Set<Long> selectDimIdSet;
        Set<Long> selectIndIdSet;
        if (dimVariableId != null) {
            selectDimIdSet = new HashSet<>();
            selectIndIdSet = new HashSet<>();
            List<DimRuleVariableDetail> list = dimRuleVariableDetailService.list(Wrappers.<DimRuleVariableDetail>lambdaQuery().eq(DimRuleVariableDetail::getDimRuleVariableId, dimVariableId));
            for (DimRuleVariableDetail dimRuleVariableDetail : list) {
                switch (dimRuleVariableDetail.getDimType()) {
                    case DIM:
                        selectDimIdSet.addAll(Arrays.stream(dimRuleVariableDetail.getEffectScope().split(",")).map(Long::valueOf).collect(Collectors.toSet()));
                        break;
                    case IND:
                        selectIndIdSet.addAll(Arrays.stream(dimRuleVariableDetail.getEffectScope().split(",")).map(Long::valueOf).collect(Collectors.toSet()));
                        break;
                }
            }
        } else {
            selectDimIdSet = Collections.emptySet();
            selectIndIdSet = Collections.emptySet();
        }
        List<DimRuleDimVO> list = new ArrayList<>(dimTableInfoVo.getDimList().size() + 1);
        // 添加维度
        Set<Integer> dimDirectoryIdSet = StreamUtils.toSet(dimTableInfoVo.getDimList(), DimDirectory::getId);
        List<DimInstance> dimInstances = dimInstanceService.listByDimDirectoryIds(dimDirectoryIdSet);
        Map<Integer, List<DimInstance>> dimInstanceDirectoryIdMap = StreamUtils.groupByKey(dimInstances, DimInstance::getDimDirectoryId);
        for (DimDirectory dimDirectory : dimTableInfoVo.getDimList()) {
            DimRuleDimVO dimRuleDimVO = new DimRuleDimVO();
            dimRuleDimVO.setDimType(DimTypeEnum.DIM);
            dimRuleDimVO.setDimDirectoryId(Long.valueOf(dimDirectory.getId()));
            dimRuleDimVO.setDimDirectoryName(dimDirectory.getDimDirectoryName());
            dimRuleDimVO.setDimDirectoryType(dimDirectory.getDimDirectoryType());
            List<DimInstance> instances = MapUtils.getObject(dimInstanceDirectoryIdMap, dimDirectory.getId());
            if (CollectionUtils.isNotEmpty(instances)) {
                int instanceSize = instances.size();
                dimRuleDimVO.setItemSize(instanceSize);
                List<DimRuleDimVO.DimRuleDimInstanceVO> dimInstanceVOS = new ArrayList<>(instanceSize);
                int checkedSize = 0;
                for (DimInstance dimInstance : instances) {
                    DimRuleDimVO.DimRuleDimInstanceVO dimRuleDimInstanceVO = new DimRuleDimVO.DimRuleDimInstanceVO();
                    dimRuleDimInstanceVO.setId(Long.valueOf(dimInstance.getId()));
                    dimRuleDimInstanceVO.setDimCode(dimInstance.getDimCode());
                    dimRuleDimInstanceVO.setAncestors(dimInstance.getAncestors());
                    dimRuleDimInstanceVO.setDimName(dimInstance.getDimName());
                    dimRuleDimInstanceVO.setIsLeaf(dimInstance.getIsLeaf());
                    dimRuleDimInstanceVO.setDimDirectoryId(Long.valueOf(dimInstance.getDimDirectoryId()));
                    dimRuleDimInstanceVO.setIndexNo(dimInstance.getIndexNo());
                    dimRuleDimInstanceVO.setParentId(Long.valueOf(dimInstance.getParentId()));
                    if (selectDimIdSet.contains(dimRuleDimInstanceVO.getId())) {
                        dimRuleDimInstanceVO.setChecked(true);
                        checkedSize++;
                    }
                    dimInstanceVOS.add(dimRuleDimInstanceVO);
                }
                dimRuleDimVO.setCheckedItemSize(checkedSize);
                dimRuleDimVO.setInstanceList(dimInstanceVOS);
            }
            list.add(dimRuleDimVO);
        }
        // 添加指标
        DimRuleDimVO dimRuleDimVO = new DimRuleDimVO();
        dimRuleDimVO.setDimType(DimTypeEnum.IND);
        dimRuleDimVO.setDimDirectoryName("指标");
        if (CollectionUtils.isNotEmpty(dimTableInfoVo.getIndList())) {
            List<DimRuleDimVO.DimRuleDimInstanceVO> instanceVOS = new ArrayList<>(dimTableInfoVo.getIndList().size());
            int checkedSize = 0;
            for (Ind ind : dimTableInfoVo.getIndList()) {
                DimRuleDimVO.DimRuleDimInstanceVO dimRuleDimInstanceVO = new DimRuleDimVO.DimRuleDimInstanceVO();
                dimRuleDimInstanceVO.setId(Long.valueOf(ind.getId()));
                dimRuleDimInstanceVO.setIndName(ind.getIndName());
                dimRuleDimInstanceVO.setIndType(ind.getIndType());
                dimRuleDimInstanceVO.setIndexNo(ind.getIndexNo());
                dimRuleDimInstanceVO.setParentId(Long.valueOf(ind.getParentId()));
                dimRuleDimInstanceVO.setLevelNumber(ind.getLevelNumber());
                if (selectIndIdSet.contains(dimRuleDimInstanceVO.getId())) {
                    dimRuleDimInstanceVO.setChecked(true);
                    checkedSize++;
                }
                instanceVOS.add(dimRuleDimInstanceVO);
            }
            dimRuleDimVO.setInstanceList(instanceVOS);
            dimRuleDimVO.setItemSize(instanceVOS.size());
            dimRuleDimVO.setCheckedItemSize(checkedSize);
        }
        list.add(dimRuleDimVO);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DimRuleVariable saveDimVariable(DimRuleVariableSaveDTO dimRuleVariableSaveDTO) {
        Assert.notEmpty(dimRuleVariableSaveDTO.getVariableDetails(), "维度成员不能为空");
        DimRuleVariable dimRuleVariable;
        if (dimRuleVariableSaveDTO.getId() != null) {
            dimRuleVariable = dimRuleVariableService.getById(dimRuleVariableSaveDTO.getId());
            Assert.notNull(dimRuleVariable, "未查询到多维变量");
            if (!StringUtils.equals(dimRuleVariableSaveDTO.getVariableName(), dimRuleVariable.getVariableName())) {
                // 检测变量名重复
                long count = dimRuleVariableService.count(Wrappers.<DimRuleVariable>lambdaQuery().eq(DimRuleVariable::getVariableName, dimRuleVariableSaveDTO.getVariableName()).eq(DimRuleVariable::getDimRuleId, dimRuleVariableSaveDTO.getDimRuleId()).ne(DimRuleVariable::getId, dimRuleVariableSaveDTO.getId()));
                Assert.isTrue(count == 0, "变量名【" + dimRuleVariableSaveDTO.getVariableName() + "】不能重复");
                dimRuleVariable.setVariableName(dimRuleVariableSaveDTO.getVariableName());
                // 更新变量名
                dimRuleVariableService.updateById(dimRuleVariable);
            }
            // 先删除历史的
            dimRuleVariableDetailService.remove(Wrappers.<DimRuleVariableDetail>lambdaQuery().eq(DimRuleVariableDetail::getDimRuleVariableId, dimRuleVariableSaveDTO.getId()));
        } else {
            // 检测变量名重复
            long count = dimRuleVariableService.count(Wrappers.<DimRuleVariable>lambdaQuery().eq(DimRuleVariable::getVariableName, dimRuleVariableSaveDTO.getVariableName()).eq(DimRuleVariable::getDimRuleId, dimRuleVariableSaveDTO.getDimRuleId()));
            Assert.isTrue(count == 0, "变量名【" + dimRuleVariableSaveDTO.getVariableName() + "】不能重复");
            dimRuleVariable = new DimRuleVariable();
            dimRuleVariable.setDimTableId(dimRuleVariableSaveDTO.getDimTableId());
            dimRuleVariable.setDimRuleId(dimRuleVariableSaveDTO.getDimRuleId());
            dimRuleVariable.setVariableName(dimRuleVariableSaveDTO.getVariableName());
            dimRuleVariableService.save(dimRuleVariable);
        }

        // 校验是否都选择了维度
        List<DimRuleVariableDetailSaveDTO> variableDetails = dimRuleVariableSaveDTO.getVariableDetails();
        // 校验维度是否有值
        Optional<DimRuleVariableDetailSaveDTO> optional = variableDetails.stream().filter(v -> StringUtils.isEmpty(v.getEffectScope())).findAny();
        if (optional.isPresent()) {
            throw new CubeRuleDefinitionException("维度【" + optional.get().getDimDirectoryName() + "】至少选中一个成员");
        }
        // 判断是否是全部的维度
        DimTableInfoVo dimTableInfoVo = dimTableService.getByIdExt(Math.toIntExact(dimRuleVariableSaveDTO.getDimTableId()));
        Assert.notNull(dimTableInfoVo, "未查询到多维表");
        Map<Integer, DimDirectory> dimIdMap = StreamUtils.toMap(dimTableInfoVo.getDimList(), DimDirectory::getId, Function.identity());
        Set<Integer> dimIds1 = new HashSet<>(dimIdMap.keySet());
        Set<Integer> dimIds2 = variableDetails.stream()
                .filter(v -> v.getDimType() == DimTypeEnum.DIM)
                .map(v -> Math.toIntExact(v.getDimDirectoryId()))
                .collect(Collectors.toSet());
        dimIds1.removeAll(dimIds2);
        if (CollectionUtils.isNotEmpty(dimIds1)) {
            String dimNames = dimIds1.stream().map(dimId -> MapUtils.getObject(dimIdMap, dimId).getDimDirectoryName()).collect(Collectors.joining(","));
            throw new CubeRuleDefinitionException("维度【" + dimNames + "】没有成员");
        }
        // 校验是否有指标
        Optional<DimRuleVariableDetailSaveDTO> indOptional = variableDetails.stream().filter(v -> v.getDimType() == DimTypeEnum.IND).findAny();
        if (indOptional.isEmpty()) {
            throw new CubeRuleDefinitionException("维度【指标】没有成员");
        }

        //保存详情
        List<DimRuleVariableDetail> dimRuleVariableDetails = new ArrayList<>(variableDetails.size());
        for (DimRuleVariableDetailSaveDTO dimRuleVariableDetailSaveDTO : variableDetails) {
            DimRuleVariableDetail dimRuleVariableDetail = new DimRuleVariableDetail();
            dimRuleVariableDetail.setDimRuleVariableId(dimRuleVariable.getId());
            dimRuleVariableDetail.setDimRuleVariableTableId(dimRuleVariableDetailSaveDTO.getDimRuleVariableTableId());
            dimRuleVariableDetail.setDimType(dimRuleVariableDetailSaveDTO.getDimType());
            dimRuleVariableDetail.setDimDirectoryId(dimRuleVariableDetailSaveDTO.getDimDirectoryId());
            dimRuleVariableDetail.setEffectScope(dimRuleVariableDetailSaveDTO.getEffectScope());
            dimRuleVariableDetails.add(dimRuleVariableDetail);
        }
        dimRuleVariableDetailService.saveBatch(dimRuleVariableDetails);

        return dimRuleVariable;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDimVariable(Long id) {
        dimRuleVariableDetailService.remove(Wrappers.<DimRuleVariableDetail>lambdaQuery().eq(DimRuleVariableDetail::getDimRuleVariableId, id));
        return dimRuleVariableService.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDimOperation(DimOperationSaveDTO dimOperationSaveDTO) {
        // 校验结果变量需要是本表的变量
        DimRuleVariable resDimRuleVariable = dimRuleVariableService.getById(dimOperationSaveDTO.getResDimVariableId());
        Assert.notNull(resDimRuleVariable, "未查询到结果变量");
        Assert.isTrue(Objects.equals(resDimRuleVariable.getDimTableId(), dimOperationSaveDTO.getDimTableId()), "结果变量的多维表需要和本表一致");
        // 校验运算详情
        DimOperationUtil.validateDimOperationDetails(dimOperationSaveDTO.getDimOperationDetails());


        return true;
    }

}




