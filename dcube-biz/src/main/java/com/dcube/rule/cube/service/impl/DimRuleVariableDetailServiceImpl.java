package com.dcube.rule.cube.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.rule.cube.domain.DimRuleVariableDetail;
import com.dcube.rule.cube.mapper.DimRuleVariableDetailMapper;
import com.dcube.rule.cube.service.IDimRuleVariableDetailService;
import org.springframework.stereotype.Service;

@Service
public class DimRuleVariableDetailServiceImpl extends ServiceImpl<DimRuleVariableDetailMapper, DimRuleVariableDetail>
        implements IDimRuleVariableDetailService {


}




