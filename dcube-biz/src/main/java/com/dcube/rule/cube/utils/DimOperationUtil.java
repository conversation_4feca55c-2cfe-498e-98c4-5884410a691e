package com.dcube.rule.cube.utils;

import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationFundamentalTypeEnum;
import com.dcube.rule.cube.dto.DimOperationDetailDTO;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

@Slf4j
public class DimOperationUtil {

    private DimOperationUtil() {
    }

    // 跟踪配置
    @Getter
    public static class TraceConfig {
        // Getter 方法
        private final boolean enabled;
        private final Consumer<String> logger;
        private final boolean includeTimestamp;
        private final boolean includeStackTrace;
        private final LogLevel logLevel;

        @Getter
        public enum LogLevel {
            TRACE(log::trace),
            DEBUG(log::debug),
            INFO(log::info),
            WARN(log::warn),
            ERROR(log::error);

            private final Consumer<String> logger;

            LogLevel(Consumer<String> logger) {
                this.logger = logger;
            }

        }

        private TraceConfig(boolean enabled, LogLevel logLevel, boolean includeTimestamp, boolean includeStackTrace) {
            this.enabled = enabled;
            this.logLevel = logLevel != null ? logLevel : LogLevel.DEBUG;
            this.logger = this.logLevel.getLogger();
            this.includeTimestamp = includeTimestamp;
            this.includeStackTrace = includeStackTrace;
        }

        // 静态工厂方法 - 基础配置
        public static TraceConfig disabled() {
            return new TraceConfig(false, LogLevel.DEBUG, false, false);
        }

        public static TraceConfig of(LogLevel level) {
            return new TraceConfig(true, level, false, false);
        }

        // 便捷方法 - 各个日志级别
        public static TraceConfig trace() {
            return of(LogLevel.TRACE);
        }

        public static TraceConfig debug() {
            return of(LogLevel.DEBUG);
        }

        public static TraceConfig info() {
            return of(LogLevel.INFO);
        }

        public static TraceConfig warn() {
            return of(LogLevel.WARN);
        }

        public static TraceConfig error() {
            return of(LogLevel.ERROR);
        }

        // 详细配置
        public static TraceConfig detailed(LogLevel level) {
            return new TraceConfig(true, level, true, true);
        }

        public static TraceConfig custom(LogLevel level, boolean includeTimestamp, boolean includeStackTrace) {
            return new TraceConfig(true, level, includeTimestamp, includeStackTrace);
        }

        // 链式配置方法
        public TraceConfig withTimestamp(boolean includeTimestamp) {
            return new TraceConfig(this.enabled, this.logLevel, includeTimestamp, this.includeStackTrace);
        }

        public TraceConfig withStackTrace(boolean includeStackTrace) {
            return new TraceConfig(this.enabled, this.logLevel, this.includeTimestamp, includeStackTrace);
        }

        public TraceConfig withLevel(LogLevel level) {
            return new TraceConfig(this.enabled, level, this.includeTimestamp, this.includeStackTrace);
        }

        @Override
        public String toString() {
            return String.format("TraceConfig{enabled=%s, level=%s, timestamp=%s, stackTrace=%s}",
                    enabled, logLevel, includeTimestamp, includeStackTrace);
        }
    }

    // 执行上下文
    public static class ExecutionContext {
        private final TraceConfig traceConfig;
        private final long startTime;
        private final StringBuilder traceLog;
        private int step;

        // 内存保护：限制日志大小
        private static final int MAX_TRACE_LOG_SIZE = 10 * 1024; // 10KB
        private static final int MAX_STEPS = 1000; // 最大步骤数
        /**
         *  是否已截断
         */
        @Getter
        private boolean logTruncated = false;

        public ExecutionContext(TraceConfig traceConfig) {
            this.traceConfig = traceConfig;
            this.startTime = System.nanoTime();
            // 预估初始容量，避免频繁扩容
            this.traceLog = new StringBuilder(512);
            this.step = 0;
        }

        public void trace(String message) {
            traceWithLevel(message, traceConfig.getLogLevel());
        }

        public void traceWithLevel(String message, TraceConfig.LogLevel level) {
            if (!traceConfig.isEnabled()) return;

            // 内存保护：限制步骤数
            if (step >= MAX_STEPS) {
                if (!logTruncated) {
                    level.getLogger().accept("[DimOperation] 警告: 跟踪步骤数超过限制(" + MAX_STEPS + ")，后续日志将被忽略");
                    logTruncated = true;
                }
                return;
            }

            step++;
            String logMessage = formatLogMessage(message);

            // 内存保护：限制日志大小
            if (traceLog.length() + logMessage.length() + 1 > MAX_TRACE_LOG_SIZE) {
                if (!logTruncated) {
                    String truncateMsg = "\n[DimOperation] 警告: 跟踪日志大小超过限制(" + MAX_TRACE_LOG_SIZE + "字节)，后续日志将被截断";
                    traceLog.append(truncateMsg);
                    level.getLogger().accept("[DimOperation] 警告: 跟踪日志大小超过限制，后续日志将被截断");
                    logTruncated = true;
                }
                // 只输出到日志器，不再添加到 traceLog
                level.getLogger().accept(logMessage);
                return;
            }

            traceLog.append(logMessage).append("\n");
            level.getLogger().accept(logMessage);
        }

        private String formatLogMessage(String message) {
            StringBuilder sb = new StringBuilder();

            // 添加 DimOperation 前缀标识
            sb.append("[DimOperation] ");

            if (traceConfig.isIncludeTimestamp()) {
                long elapsed = (System.nanoTime() - startTime) / 1_000_000; // ms
                sb.append("[").append(elapsed).append("ms] ");
            }

            sb.append("Step ").append(step).append(": ").append(message);

            if (traceConfig.isIncludeStackTrace()) {
                StackTraceElement caller = Thread.currentThread().getStackTrace()[4]; // 调整调用栈深度
                sb.append(" (").append(caller.getMethodName()).append(":").append(caller.getLineNumber()).append(")");
            }

            return sb.toString();
        }

        public String getFullTrace() {
            return traceLog.toString();
        }

        public long getElapsedTimeMs() {
            return (System.nanoTime() - startTime) / 1_000_000;
        }

        /**
         * 清理资源，防止内存泄露
         */
        public void cleanup() {
            // 清空 StringBuilder 内容，释放内存
            if (traceLog.length() > 0) {
                traceLog.setLength(0);
                traceLog.trimToSize();
            }
        }

        /**
         * 获取当前日志大小（字节）
         */
        public int getCurrentLogSize() {
            return traceLog.length();
        }

    }

    // 缓存常用的符号映射
    private static final Map<DimOperationFundamentalTypeEnum, String> SYMBOL_CACHE = new ConcurrentHashMap<>();

    // 预编译的常量
    private static final String LEFT_BRACKET = "(";
    private static final String RIGHT_BRACKET = ")";
    private static final String UNKNOWN_TYPE = "[未知类型]";
    private static final String UNKNOWN_SYMBOL = "[?]";
    private static final String VARIABLE_PREFIX = "变量#";

    // BigDecimal 常量池 - 避免重复创建常用的 BigDecimal 对象
    private static final BigDecimal BD_THREE = new BigDecimal("3");
    private static final BigDecimal BD_FOUR = new BigDecimal("4");
    private static final BigDecimal BD_FIVE = new BigDecimal("5");

    // 常用 BigDecimal 值的缓存映射
    private static final Map<String, BigDecimal> COMMON_BIGDECIMAL_CACHE = new ConcurrentHashMap<>();

    static {
        // 预填充常用值
        COMMON_BIGDECIMAL_CACHE.put("0", BigDecimal.ZERO);
        COMMON_BIGDECIMAL_CACHE.put("0.0", BigDecimal.ZERO);
        COMMON_BIGDECIMAL_CACHE.put("1", BigDecimal.ONE);
        COMMON_BIGDECIMAL_CACHE.put("1.0", BigDecimal.ONE);
        COMMON_BIGDECIMAL_CACHE.put("2", BigDecimal.TWO);
        COMMON_BIGDECIMAL_CACHE.put("2.0", BigDecimal.TWO);
        COMMON_BIGDECIMAL_CACHE.put("3", BD_THREE);
        COMMON_BIGDECIMAL_CACHE.put("3.0", BD_THREE);
        COMMON_BIGDECIMAL_CACHE.put("4", BD_FOUR);
        COMMON_BIGDECIMAL_CACHE.put("4.0", BD_FOUR);
        COMMON_BIGDECIMAL_CACHE.put("5", BD_FIVE);
        COMMON_BIGDECIMAL_CACHE.put("5.0", BD_FIVE);
        COMMON_BIGDECIMAL_CACHE.put("10", BigDecimal.TEN);
        COMMON_BIGDECIMAL_CACHE.put("10.0", BigDecimal.TEN);
    }

    // 线程安全的内存监控相关
    private static volatile long lastMaintenanceTime = System.currentTimeMillis();
    private static final AtomicLong executionCounter = new AtomicLong(0);
    private static final long MAINTENANCE_INTERVAL_MS = 5 * 60 * 1000; // 5分钟
    private static final long MAINTENANCE_EXECUTION_THRESHOLD = 1000; // 1000次执行后检查

    // 维护操作的读写锁，确保维护时不影响并发执行
    private static final ReentrantReadWriteLock maintenanceLock = new ReentrantReadWriteLock();
    private static final ReentrantReadWriteLock.ReadLock maintenanceReadLock = maintenanceLock.readLock();
    private static final ReentrantReadWriteLock.WriteLock maintenanceWriteLock = maintenanceLock.writeLock();

    // Caffeine 缓存配置
    private static final Cache<String, List<Object>> POSTFIX_CACHE = Caffeine.newBuilder()
            .maximumSize(1000)  // 增加缓存大小以提高命中率
            .expireAfterWrite(Duration.ofHours(2))  // 延长过期时间
            .expireAfterAccess(Duration.ofMinutes(30)) // 适中的访问过期时间
            .removalListener((key, value, cause) -> {
                // 缓存移除时的清理逻辑
                if (value instanceof List) {
                    ((List<?>) value).clear();
                }
            })
            .recordStats()
            .build();

    private static final Cache<String, List<DimOperationDetailDTO>> OPTIMIZED_EXPRESSION_CACHE = Caffeine.newBuilder()
            .maximumSize(500)  // 增加缓存大小
            .expireAfterWrite(Duration.ofHours(1))  // 延长过期时间
            .expireAfterAccess(Duration.ofMinutes(20))  // 适中的访问过期时间
            .removalListener((key, value, cause) -> {
                // 缓存移除时的清理逻辑
                if (value instanceof List) {
                    ((List<?>) value).clear();
                }
            })
            .recordStats()
            .build();

    // 表达式优化配置
    @Getter
    public static class OptimizationConfig {
        // Getters
        private final boolean enabled;
        private final boolean constantFolding;
        private final boolean redundantBracketRemoval;
        private final boolean algebraicSimplification;

        public OptimizationConfig(boolean enabled, boolean constantFolding,
                                  boolean redundantBracketRemoval, boolean algebraicSimplification) {
            this.enabled = enabled;
            this.constantFolding = constantFolding;
            this.redundantBracketRemoval = redundantBracketRemoval;
            this.algebraicSimplification = algebraicSimplification;
        }

        public static OptimizationConfig disabled() {
            return new OptimizationConfig(false, false, false, false);
        }

        public static OptimizationConfig basic() {
            return new OptimizationConfig(true, true, true, false);
        }

        public static OptimizationConfig full() {
            return new OptimizationConfig(true, true, true, true);
        }

    }

    /**
     * 表达式打印
     */
    public static String printExpression(@NotEmpty List<DimOperationDetailDTO> details) {
        // 预估容量以减少StringBuilder扩容
        StringBuilder expression = new StringBuilder(details.size() * 8);

        for (DimOperationDetailDTO dto : details) {
            DimOperationDetailTypeEnum type = dto.getDimOperationDetailType();

            switch (type) {
                case VARIABLE:
                    // 变量处理：优先使用变量名，没有则用ID
                    String varName = dto.getDimVariableName();
                    if (varName == null || varName.trim().isEmpty()) {
                        expression.append(VARIABLE_PREFIX).append(dto.getDimVariableId());
                    } else {
                        expression.append(varName);
                    }
                    break;

                case CONSTANT:
                    // 常数处理：直接显示常数值
                    String constantVal = dto.getConstantVal();
                    if (constantVal != null && !constantVal.trim().isEmpty()) {
                        expression.append(constantVal);
                    } else {
                        expression.append("0");
                    }
                    break;

                case BRACKET:
                    // 括号处理 - 使用预编译常量
                    expression.append(dto.getBracketType() == DimOperationBracketTypeEnum.LEFT ?
                            LEFT_BRACKET : RIGHT_BRACKET);
                    break;

                case FUNDAMENTAL:
                    // 四则运算符号处理 - 使用缓存
                    expression.append(getOperatorSymbolCached(dto.getFundamentalType()));
                    break;

                default:
                    expression.append(UNKNOWN_TYPE);
            }
        }
        return expression.toString();
    }

    /**
     * 符号获取
     */
    private static String getOperatorSymbolCached(DimOperationFundamentalTypeEnum opType) {
        if (opType == null) return UNKNOWN_SYMBOL;
        return SYMBOL_CACHE.computeIfAbsent(opType, DimOperationFundamentalTypeEnum::getSymbol);
    }

    /**
     * BigDecimal 获取方法
     * @param dto 常数 DTO
     * @return BigDecimal 值，如果转换失败返回 null
     */
    private static BigDecimal getConstantValueOptimized(DimOperationDetailDTO dto) {
        if (!isConstant(dto)) {
            return null;
        }

        String constantVal = dto.getConstantVal();
        if (constantVal == null || constantVal.trim().isEmpty()) {
            return null;
        }

        String trimmedVal = constantVal.trim();

        // 首先检查常用值缓存
        BigDecimal cached = COMMON_BIGDECIMAL_CACHE.get(trimmedVal);
        if (cached != null) {
            return cached;
        }

        // 使用 DTO 内置的缓存机制
        return dto.getConstantValueAsBigDecimal();
    }

    /**
     * 检查是否为常量0
     */
    private static boolean isZeroConstantOptimized(DimOperationDetailDTO dto) {
        if (!isConstant(dto)) return false;
        BigDecimal value = getConstantValueOptimized(dto);
        return value != null && value.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 检查是否为常量1
     */
    private static boolean isOneConstantOptimized(DimOperationDetailDTO dto) {
        if (!isConstant(dto)) return false;
        BigDecimal value = getConstantValueOptimized(dto);
        return value != null && value.compareTo(BigDecimal.ONE) == 0;
    }


    // 状态定义
    private enum ParseState {
        START,              // 初始状态
        AFTER_OPERAND,      // 操作数后（变量或右括号）
        AFTER_OPERATOR,     // 运算符后
        AFTER_LEFT_BRACKET  // 左括号后
    }

    /**
     * 表达式校验
     * @Author: yanghao
     * @Date: 2025-06-17 18:02:55
     * @Params: [details]
     * @Return: void
     * @Description: 校验运算详情
     */
    public static void validateDimOperationDetails(@NotEmpty List<DimOperationDetailDTO> details) {
        int size = details.size();
        Deque<Integer> bracketStack = new ArrayDeque<>(size / 4 + 1);
        int variableCount = 0;
        ParseState state = ParseState.START;

        for (int i = 0; i < size; i++) {
            DimOperationDetailDTO current = details.get(i);
            DimOperationDetailTypeEnum type = current.getDimOperationDetailType();

            if (type == null) {
                throw new IllegalArgumentException("运算详情类型不能为空（位置：" + i + ")");
            }

            // 基本元素校验
            switch (type) {
                case VARIABLE:
                    variableCount++;
                    if (current.getDimVariableId() == null) {
                        throw new IllegalArgumentException("变量ID不能为空（位置：" + i + ")");
                    }
                    break;
                case CONSTANT:
                    // 校验常数值
                    BigDecimal constantValue = getConstantValueOptimized(current);
                    if (constantValue == null) {
                        String constantVal = current.getConstantVal();
                        throw new IllegalArgumentException("常数值格式不正确：" + constantVal + "（位置：" + i + ")");
                    }
                    break;
                case FUNDAMENTAL:
                    if (current.getFundamentalType() == null) {
                        throw new IllegalArgumentException("四则运算类型不能为空（位置：" + i + ")");
                    }
                    break;
                case BRACKET:
                    if (current.getBracketType() == null) {
                        throw new IllegalArgumentException("括号类型不能为空（位置：" + i + ")");
                    }
                    break;
                default:
                    throw new IllegalArgumentException("未知的运算详情类型：" + type + "（位置：" + i + ")");
            }

            // 基于状态机的语法校验
            switch (state) {
                case START:
                    handleStartState(current, i);
                    break;

                case AFTER_OPERAND:
                    handleAfterOperandState(current, i);
                    break;

                case AFTER_OPERATOR:
                    handleAfterOperatorState(current, i);
                    break;

                case AFTER_LEFT_BRACKET:
                    handleAfterLeftBracketState(current, i);
                    break;
            }

            // 状态转换
            switch (type) {
                case VARIABLE:
                case CONSTANT:
                    // 变量和常数都是操作数
                    state = ParseState.AFTER_OPERAND;
                    break;

                case FUNDAMENTAL:
                    state = ParseState.AFTER_OPERATOR;
                    break;

                case BRACKET:
                    if (isLeftBracket(current)) {
                        state = ParseState.AFTER_LEFT_BRACKET;
                        bracketStack.push(i);
                    } else {
                        state = ParseState.AFTER_OPERAND;
                        handleRightBracketOptimized(bracketStack, i);
                    }
                    break;
            }
        }

        // 最终状态校验
        if (state != ParseState.AFTER_OPERAND) {
            throw new IllegalArgumentException("表达式不能以运算符或左括号结尾");
        }

        if (!bracketStack.isEmpty()) {
            throw new IllegalArgumentException("存在未闭合的左括号（位置：" + bracketStack.pop() + ")");
        }

        if (variableCount == 0) {
            throw new IllegalArgumentException("表达式中至少需要一个变量");
        }
    }

    /**
     * 状态处理函数
     */
    private static void handleStartState(DimOperationDetailDTO current, int position) {
        if (isOperator(current)) {
            throw new IllegalArgumentException("表达式不能以运算符开头（位置：" + position + ")");
        }
        if (isRightBracket(current)) {
            throw new IllegalArgumentException("表达式不能以右括号开头（位置：" + position + ")");
        }
    }

    private static void handleAfterOperandState(DimOperationDetailDTO current, int position) {
        if (isLeftBracket(current)) {
            throw new IllegalArgumentException("操作数后不能直接跟左括号（位置：" + position + ")");
        }
        if (isVariable(current)) {
            throw new IllegalArgumentException("操作数后不能直接跟变量（位置：" + position + ")");
        }
        if (isConstant(current)) {
            throw new IllegalArgumentException("操作数后不能直接跟常数（位置：" + position + ")");
        }
    }

    private static void handleAfterOperatorState(DimOperationDetailDTO current, int position) {
        if (isOperator(current)) {
            throw new IllegalArgumentException("不能连续出现运算符（位置：" + position + ")");
        }
        if (isRightBracket(current)) {
            throw new IllegalArgumentException("运算符后不能直接跟右括号（位置：" + position + ")");
        }
    }

    private static void handleAfterLeftBracketState(DimOperationDetailDTO current, int position) {
        if (isOperator(current)) {
            throw new IllegalArgumentException("左括号后不能直接跟运算符（位置：" + position + ")");
        }
        if (isRightBracket(current)) {
            throw new IllegalArgumentException("括号内不能为空（位置：" + (position - 1) + "和" + position + ")");
        }
    }

    /**
     * 括号处理函数
     */
    private static void handleRightBracketOptimized(Deque<Integer> bracketStack, int position) {
        if (bracketStack.isEmpty()) {
            throw new IllegalArgumentException("右括号没有匹配的左括号（位置：" + position + ")");
        }
        bracketStack.pop();
    }

    /**
     * 辅助函数
     */
    private static boolean isLeftBracket(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.BRACKET &&
                dto.getBracketType() == DimOperationBracketTypeEnum.LEFT;
    }

    private static boolean isRightBracket(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.BRACKET &&
                dto.getBracketType() == DimOperationBracketTypeEnum.RIGHT;
    }

    private static boolean isOperator(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.FUNDAMENTAL;
    }

    private static boolean isVariable(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.VARIABLE;
    }

    private static boolean isConstant(DimOperationDetailDTO dto) {
        return dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.CONSTANT;
    }

    /**
     * 执行表达式 - 无跟踪版本
     */
    public static BigDecimal executeExpression(@NotEmpty List<DimOperationDetailDTO> details,
                                               @NotNull Map<Long, BigDecimal> variableValueMap) {
        return executeExpressionWithOptimization(details, variableValueMap,
                TraceConfig.disabled(), OptimizationConfig.basic()).getResult();
    }

    /**
     * 执行表达式 - 带跟踪版本
     */
    public static ExecutionResult executeExpressionWithTrace(@NotEmpty List<DimOperationDetailDTO> details,
                                                             @NotNull Map<Long, BigDecimal> variableValueMap,
                                                             TraceConfig traceConfig) {
        return executeExpressionWithOptimization(details, variableValueMap, traceConfig, OptimizationConfig.basic());
    }

    /**
     * 执行表达式 - 完整版本（带跟踪和优化）
     */
    public static ExecutionResult executeExpressionWithOptimization(@NotEmpty List<DimOperationDetailDTO> details,
                                                                    @NotNull Map<Long, BigDecimal> variableValueMap,
                                                                    TraceConfig traceConfig,
                                                                    OptimizationConfig optimizationConfig) {
        // 获取读锁，确保在维护期间可以并发执行
        maintenanceReadLock.lock();
        ExecutionContext context = null;
        try {
            // 自动内存监控和维护
            checkAndPerformAutoMaintenance();

            context = new ExecutionContext(traceConfig);
            context.trace("开始执行表达式，包含 " + details.size() + " 个元素");

            // 输入验证
            if (details.isEmpty()) {
                throw new IllegalArgumentException("表达式不能为空");
            }

            // 表达式优化
            List<DimOperationDetailDTO> optimizedDetails = details;
            if (optimizationConfig.isEnabled()) {
                context.trace("开始表达式优化");
                optimizedDetails = optimizeExpression(details, optimizationConfig, context);
                context.trace("表达式优化完成，优化后包含 " + optimizedDetails.size() + " 个元素");
            }

            // 生成缓存键
            String cacheKey = generateCacheKey(optimizedDetails, variableValueMap.keySet());

            context.trace("开始转换为后缀表达式");

            // 内存优化：避免在 lambda 中持有大对象引用
            final List<DimOperationDetailDTO> finalOptimizedDetails = optimizedDetails;
            List<Object> postfix = POSTFIX_CACHE.get(cacheKey, key -> {
                // 创建新的轻量级上下文，避免持有原始 context 引用
                ExecutionContext lightContext = new ExecutionContext(TraceConfig.disabled());
                try {
                    return toPostfixWithTrace(finalOptimizedDetails, variableValueMap, lightContext);
                } finally {
                    // 立即清理轻量级上下文
                    lightContext.cleanup();
                }
            });

            if (postfix != null && !postfix.isEmpty()) {
                context.trace("使用缓存的后缀表达式，包含 " + postfix.size() + " 个元素");
            } else {
                context.trace("后缀表达式转换完成，包含 " + postfix.size() + " 个元素");
            }

            context.trace("开始计算后缀表达式");

            // 执行后缀表达式
            BigDecimal result = evaluatePostfixWithTrace(postfix, context);

            context.trace("表达式计算完成，结果: " + result);

            // 收集内存使用信息
            long memoryUsed = context.getCurrentLogSize();
            boolean truncated = context.isLogTruncated();

            ExecutionResult executionResult = new ExecutionResult(
                    result,
                    context.getFullTrace(),
                    context.getElapsedTimeMs(),
                    truncated,
                    memoryUsed
            );

            // 内存清理：执行完成后清理上下文
            context.cleanup();

            return executionResult;

        } catch (Exception e) {
            // 异常情况下也要清理资源
            try {
                if (context != null) {
                    context.trace("执行过程中发生异常: " + e.getMessage());
                    context.cleanup();
                }
            } catch (Exception cleanupException) {
                // 忽略清理过程中的异常
                log.error("[DimOperation] 清理资源时发生异常", cleanupException);
            }
            throw e;
        } finally {
            // 释放读锁
            maintenanceReadLock.unlock();
        }
    }

    /**
     * 执行结果封装
     */
    @Getter
    public static class ExecutionResult {
        private final BigDecimal result;
        private final String traceLog;
        private final long executionTimeMs;
        private final int traceLogSize;
        private final boolean logTruncated;
        private final long memoryUsedBytes;

        public ExecutionResult(BigDecimal result, String traceLog, long executionTimeMs) {
            this(result, traceLog, executionTimeMs, false, 0);
        }

        public ExecutionResult(BigDecimal result, String traceLog, long executionTimeMs,
                               boolean logTruncated, long memoryUsedBytes) {
            this.result = result;
            this.traceLog = traceLog;
            this.executionTimeMs = executionTimeMs;
            this.traceLogSize = traceLog != null ? traceLog.length() : 0;
            this.logTruncated = logTruncated;
            this.memoryUsedBytes = memoryUsedBytes;
        }

        /**
         * 获取内存使用情况（KB）
         */
        public double getMemoryUsedKB() {
            return memoryUsedBytes / 1024.0;
        }

        /**
         * 获取执行摘要
         */
        public String getSummary() {
            StringBuilder sb = new StringBuilder();
            sb.append("结果: ").append(result);
            sb.append(", 执行时间: ").append(executionTimeMs).append("ms");
            sb.append(", 日志大小: ").append(traceLogSize).append("字符");
            if (logTruncated) {
                sb.append(" (已截断)");
            }
            if (memoryUsedBytes > 0) {
                sb.append(", 内存使用: ").append(String.format("%.1f", getMemoryUsedKB())).append("KB");
            }
            return sb.toString();
        }

        @Override
        public String toString() {
            return getSummary();
        }
    }

    /**
     * 带跟踪的后缀表达式计算
     */
    private static BigDecimal evaluatePostfixWithTrace(List<Object> postfix, ExecutionContext context) {
        // 预估栈大小
        Deque<BigDecimal> stack = new ArrayDeque<>(postfix.size() / 2 + 1);

        int tokenIndex = 0;
        for (Object token : postfix) {
            tokenIndex++;

            if (token instanceof BigDecimal) {
                BigDecimal value = (BigDecimal) token;
                stack.push(value);
                context.trace("压入操作数: " + value + " (栈大小: " + stack.size() + ")");

            } else if (token instanceof DimOperationFundamentalTypeEnum) {
                if (stack.size() < 2) {
                    throw new IllegalStateException("栈中操作数不足，位置: " + tokenIndex);
                }

                DimOperationFundamentalTypeEnum op = (DimOperationFundamentalTypeEnum) token;
                BigDecimal b = stack.pop();
                BigDecimal a = stack.pop();

                context.trace("执行运算: " + a + " " + op.getSymbol() + " " + b);

                BigDecimal result = op.apply(a, b);
                stack.push(result);

                context.trace("运算结果: " + result + " (栈大小: " + stack.size() + ")");
            } else {
                throw new IllegalStateException("未知的token类型: " + token.getClass().getSimpleName() + ", 位置: " + tokenIndex);
            }
        }

        if (stack.size() != 1) {
            throw new IllegalStateException("表达式计算错误，结果栈中有 " + stack.size() + " 个元素，期望1个");
        }

        return stack.pop();
    }

    /**
     * 带跟踪的中缀转后缀表达式
     */
    private static List<Object> toPostfixWithTrace(List<DimOperationDetailDTO> details,
                                                   Map<Long, BigDecimal> varMap,
                                                   ExecutionContext context) {
        // 预估容量
        List<Object> output = new ArrayList<>(details.size());
        Deque<Object> operators = new ArrayDeque<>(details.size() / 2);

        int position = 0;
        for (DimOperationDetailDTO dto : details) {
            position++;
            DimOperationDetailTypeEnum type = dto.getDimOperationDetailType();

            switch (type) {
                case VARIABLE:
                    Long varId = dto.getDimVariableId();
                    if (!varMap.containsKey(varId)) {
                        throw new IllegalArgumentException("变量ID未提供数值: " + varId + ", 位置: " + position);
                    }
                    BigDecimal value = varMap.get(varId);
                    output.add(value);
                    context.trace("处理变量 ID=" + varId + ", 值=" + value + " (位置: " + position + ")");
                    break;

                case CONSTANT:
                    // 处理常数
                    BigDecimal constantValue = getConstantValueOptimized(dto);
                    if (constantValue == null) {
                        String constantVal = dto.getConstantVal();
                        throw new IllegalArgumentException("常数值格式不正确: " + constantVal + ", 位置: " + position);
                    }
                    output.add(constantValue);
                    context.trace("处理常数 值=" + constantValue + " (位置: " + position + ")");
                    break;

                case FUNDAMENTAL:
                    DimOperationFundamentalTypeEnum op = dto.getFundamentalType();
                    if (op == null) {
                        throw new IllegalArgumentException("运算符不能为空, 位置: " + position);
                    }

                    // 减少类型检查
                    while (!operators.isEmpty()) {
                        Object topOp = operators.peek();
                        if (topOp instanceof DimOperationFundamentalTypeEnum) {
                            DimOperationFundamentalTypeEnum topOpEnum = (DimOperationFundamentalTypeEnum) topOp;
                            if (op.getPrecedence() <= topOpEnum.getPrecedence()) {
                                output.add(operators.pop());
                                context.trace("弹出高优先级运算符: " + topOpEnum.getSymbol());
                            } else {
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                    operators.push(op);
                    context.trace("压入运算符: " + op.getSymbol() + " (位置: " + position + ")");
                    break;

                case BRACKET:
                    DimOperationBracketTypeEnum bracketType = dto.getBracketType();
                    if (bracketType == null) {
                        throw new IllegalArgumentException("括号类型不能为空, 位置: " + position);
                    }

                    if (bracketType == DimOperationBracketTypeEnum.LEFT) {
                        operators.push(LEFT_BRACKET);
                        context.trace("压入左括号 (位置: " + position + ")");
                    } else {
                        boolean foundLeftBracket = false;
                        while (!operators.isEmpty()) {
                            Object topOperator = operators.peek();
                            if (LEFT_BRACKET.equals(topOperator)) {
                                operators.pop(); // 弹出左括号
                                foundLeftBracket = true;
                                break;
                            } else {
                                output.add(operators.pop());
                                context.trace("弹出括号内运算符: " + topOperator);
                            }
                        }
                        if (!foundLeftBracket) {
                            throw new IllegalArgumentException("右括号没有匹配的左括号, 位置: " + position);
                        }
                        context.trace("处理右括号 (位置: " + position + ")");
                    }
                    break;

                default:
                    throw new IllegalArgumentException("未知的操作类型: " + type + ", 位置: " + position);
            }
        }

        // 弹出剩余的运算符
        while (!operators.isEmpty()) {
            Object op = operators.pop();
            if (LEFT_BRACKET.equals(op)) {
                throw new IllegalArgumentException("存在未匹配的左括号");
            }
            output.add(op);
            context.trace("弹出剩余运算符: " + op);
        }

        context.trace("中缀转后缀完成，输出: " + output);
        return output;
    }


    public static void main(String[] args) {
        // 创建表达式: (价格 + 运费) × 数量
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expression.add(createVariable("价格", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("运费", 2L));
        expression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("数量", 3L));

        validateDimOperationDetails(expression);

        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("2.0"),
                2L, new BigDecimal("3.0"),
                3L, new BigDecimal("4.0")
        );

        // 创建包含常数的表达式: (价格 + 10.5) × 数量 - 5
        List<DimOperationDetailDTO> constantExpression = new ArrayList<>();
        constantExpression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        constantExpression.add(createVariable("价格", 1L));
        constantExpression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        constantExpression.add(createConstant("10.5"));
        constantExpression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        constantExpression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        constantExpression.add(createVariable("数量", 3L));
        constantExpression.add(createOperator(DimOperationFundamentalTypeEnum.SUBTRACT));
        constantExpression.add(createConstant("5"));

        validateDimOperationDetails(constantExpression);

        Map<Long, BigDecimal> constantValues = Map.of(
                1L, new BigDecimal("2.0"),
                3L, new BigDecimal("4.0")
        );

        // 测试无跟踪版本
        System.out.println("=== 无跟踪版本 ===");
        BigDecimal result = executeExpression(expression, values);
        System.out.println("计算结果: " + result);

        // 测试常数表达式
        System.out.println("\n=== 常数表达式测试 ===");
        System.out.println("表达式: " + printExpression(constantExpression));
        BigDecimal constantResult = executeExpression(constantExpression, constantValues);
        System.out.println("计算结果: " + constantResult + " (期望: 45.0)");

        // 测试 DEBUG 级别跟踪版本
        System.out.println("\n=== DEBUG 级别跟踪版本 ===");
        ExecutionResult debugResult = executeExpressionWithTrace(expression, values, TraceConfig.debug());
        System.out.println("计算结果: " + debugResult.getResult());
        System.out.println("执行时间: " + debugResult.getExecutionTimeMs() + "ms");

        // 测试 INFO 级别跟踪版本
        System.out.println("\n=== INFO 级别跟踪版本 ===");
        ExecutionResult infoResult = executeExpressionWithTrace(expression, values, TraceConfig.info());
        System.out.println("计算结果: " + infoResult.getResult());
        System.out.println("执行时间: " + infoResult.getExecutionTimeMs() + "ms");

        // 测试详细跟踪版本
        System.out.println("\n=== 详细跟踪版本 ===");
        ExecutionResult detailedResult = executeExpressionWithTrace(expression, values,
                TraceConfig.detailed(TraceConfig.LogLevel.DEBUG));
        System.out.println("计算结果: " + detailedResult.getResult());
        System.out.println("执行时间: " + detailedResult.getExecutionTimeMs() + "ms");

        // 测试链式配置
        System.out.println("\n=== 链式配置示例 ===");
        ExecutionResult chainResult = executeExpressionWithTrace(expression, values,
                TraceConfig.info().withTimestamp(true).withStackTrace(false));
        System.out.println("链式配置结果: " + chainResult.getResult());
        System.out.println("执行时间: " + chainResult.getExecutionTimeMs() + "ms");

        // 测试优化功能
        System.out.println("\n=== 优化功能测试 ===");
        testOptimizationFeatures();
        testOptimization(expression, values);

        // 测试 BigDecimal 优化
        System.out.println("\n=== BigDecimal 优化测试 ===");
        testBigDecimalOptimization();

        // 性能测试
        System.out.println("\n=== 性能测试 ===");
        performanceTest(expression, values);

        // 缓存统计
        System.out.println("\n=== 缓存统计 ===");
        System.out.println(getCacheStats());
    }

    /**
     * 表达式优化
     */
    private static List<DimOperationDetailDTO> optimizeExpression(List<DimOperationDetailDTO> details,
                                                                  OptimizationConfig config,
                                                                  ExecutionContext context) {
        String cacheKey = generateOptimizationCacheKey(details, config);

        // 尝试从缓存获取优化后的表达式
        List<DimOperationDetailDTO> cached = OPTIMIZED_EXPRESSION_CACHE.getIfPresent(cacheKey);
        if (cached != null) {
            context.trace("使用缓存的优化表达式");
            return cached;
        }

        List<DimOperationDetailDTO> optimized = new ArrayList<>(details);

        // 1. 移除冗余括号
        if (config.isRedundantBracketRemoval()) {
            context.trace("执行冗余括号移除优化");
            optimized = removeRedundantBrackets(optimized);
        }

        // 2. 常量折叠（如果有常量表达式）
        if (config.isConstantFolding()) {
            context.trace("执行常量折叠优化");
            optimized = foldConstants(optimized);
        }

        // 3. 代数简化
        if (config.isAlgebraicSimplification()) {
            context.trace("执行代数简化优化");
            optimized = simplifyAlgebraicallyMultiPass(optimized);
        }

        // 缓存优化结果
        OPTIMIZED_EXPRESSION_CACHE.put(cacheKey, optimized);

        return optimized;
    }

    /**
     * 移除冗余括号
     */
    private static List<DimOperationDetailDTO> removeRedundantBrackets(List<DimOperationDetailDTO> details) {
        List<DimOperationDetailDTO> result = new ArrayList<>();
        Deque<Integer> bracketStack = new ArrayDeque<>();
        Map<Integer, Integer> bracketPairs = new HashMap<>();

        // 第一遍：找出所有括号对
        for (int i = 0; i < details.size(); i++) {
            DimOperationDetailDTO dto = details.get(i);
            if (isLeftBracket(dto)) {
                bracketStack.push(i);
            } else if (isRightBracket(dto)) {
                if (!bracketStack.isEmpty()) {
                    int leftPos = bracketStack.pop();
                    bracketPairs.put(leftPos, i);
                }
            }
        }

        // 第二遍：判断哪些括号是冗余的
        Set<Integer> redundantBrackets = new HashSet<>();
        for (Map.Entry<Integer, Integer> pair : bracketPairs.entrySet()) {
            int leftPos = pair.getKey();
            int rightPos = pair.getValue();

            // 简单的冗余检查：如果括号内只有一个操作数（变量或常数），则认为是冗余的
            if (rightPos - leftPos == 2) {
                DimOperationDetailDTO middle = details.get(leftPos + 1);
                if (isVariable(middle) || isConstant(middle)) {
                    redundantBrackets.add(leftPos);
                    redundantBrackets.add(rightPos);
                }
            }
        }

        // 第三遍：构建结果，跳过冗余括号
        for (int i = 0; i < details.size(); i++) {
            if (!redundantBrackets.contains(i)) {
                result.add(details.get(i));
            }
        }

        return result;
    }

    /**
     * 常量折叠优化
     * 识别并计算常量子表达式，将其替换为单个常量值
     */
    private static List<DimOperationDetailDTO> foldConstants(List<DimOperationDetailDTO> details) {
        if (details.size() < 3) {
            return details; // 太短的表达式无需优化
        }

        // 首先尝试高级常量折叠（处理括号等复杂情况）
        List<DimOperationDetailDTO> result = foldConstantsAdvanced(details);

        // 如果高级折叠没有改变表达式，尝试基础折叠
        if (result.size() == details.size() && result.equals(details)) {
            result = foldConstantsBasic(details);
        }

        return result;
    }

    /**
     * 基础常量折叠 - 处理简单的常量表达式
     */
    private static List<DimOperationDetailDTO> foldConstantsBasic(List<DimOperationDetailDTO> details) {
        List<DimOperationDetailDTO> result = new ArrayList<>();
        int i = 0;

        while (i < details.size()) {
            // 寻找常量子表达式的起始位置
            int constantStart = findConstantExpressionStart(details, i);

            if (constantStart != -1) {
                // 找到常量表达式的结束位置
                int constantEnd = findConstantExpressionEnd(details, constantStart);

                if (constantEnd > constantStart + 2) { // 至少包含 常量 运算符 常量
                    // 提取常量子表达式
                    List<DimOperationDetailDTO> constantExpr = details.subList(constantStart, constantEnd + 1);

                    // 尝试计算常量表达式
                    try {
                        BigDecimal constantResult = evaluateConstantExpression(constantExpr);

                        // 添加之前的非常量部分
                        for (int j = i; j < constantStart; j++) {
                            result.add(details.get(j));
                        }

                        // 用计算结果替换常量表达式
                        result.add(createConstant(constantResult.toString()));

                        i = constantEnd + 1;
                        continue;
                    } catch (Exception e) {
                        // 如果计算失败，保持原样
                        log.error("[DimOperation] 常量折叠失败: {}", e.getMessage(), e);
                    }
                }
            }

            // 没有找到可优化的常量表达式，添加当前元素
            result.add(details.get(i));
            i++;
        }

        return result;
    }

    /**
     * 寻找常量表达式的起始位置
     */
    private static int findConstantExpressionStart(List<DimOperationDetailDTO> details, int startIndex) {
        for (int i = startIndex; i < details.size(); i++) {
            DimOperationDetailDTO dto = details.get(i);
            if (isConstant(dto)) {
                // 检查是否可以形成常量表达式
                if (i + 2 < details.size() &&
                        isOperator(details.get(i + 1)) &&
                        isConstant(details.get(i + 2))) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * 寻找常量表达式的结束位置
     */
    private static int findConstantExpressionEnd(List<DimOperationDetailDTO> details, int startIndex) {
        int i = startIndex;

        // 跳过第一个常量
        if (i < details.size() && isConstant(details.get(i))) {
            i++;
        }

        // 寻找连续的 运算符-常量 模式
        while (i + 1 < details.size()) {
            if (isOperator(details.get(i)) && isConstant(details.get(i + 1))) {
                i += 2; // 跳过运算符和常量
            } else {
                break;
            }
        }

        return i - 1; // 返回最后一个常量的位置
    }

    /**
     * 计算常量表达式的值
     */
    private static BigDecimal evaluateConstantExpression(List<DimOperationDetailDTO> constantExpr) {
        if (constantExpr.isEmpty()) {
            throw new IllegalArgumentException("常量表达式不能为空");
        }

        // 如果表达式包含括号，需要使用完整的表达式计算逻辑
        if (containsBrackets(constantExpr)) {
            return evaluateConstantExpressionWithBrackets(constantExpr);
        }

        // 简单表达式：考虑运算符优先级
        return evaluateSimpleConstantExpression(constantExpr);
    }

    /**
     * 检查表达式是否包含括号
     */
    private static boolean containsBrackets(List<DimOperationDetailDTO> expr) {
        return expr.stream().anyMatch(dto ->
                dto.getDimOperationDetailType() == DimOperationDetailTypeEnum.BRACKET);
    }

    /**
     * 计算包含括号的常量表达式
     */
    private static BigDecimal evaluateConstantExpressionWithBrackets(List<DimOperationDetailDTO> constantExpr) {
        // 创建一个空的变量映射，因为这里只有常量
        Map<Long, BigDecimal> emptyVarMap = new HashMap<>();

        // 使用现有的后缀表达式计算逻辑
        ExecutionContext tempContext = new ExecutionContext(TraceConfig.disabled());
        try {
            List<Object> postfix = toPostfixWithTrace(constantExpr, emptyVarMap, tempContext);
            return evaluatePostfixWithTrace(postfix, tempContext);
        } finally {
            tempContext.cleanup();
        }
    }

    /**
     * 计算简单常量表达式（无括号，考虑运算符优先级）
     */
    private static BigDecimal evaluateSimpleConstantExpression(List<DimOperationDetailDTO> constantExpr) {
        if (constantExpr.size() == 1) {
            BigDecimal value = getConstantValueOptimized(constantExpr.get(0));
            if (value == null) {
                throw new IllegalArgumentException("无效的常数值: " + constantExpr.get(0).getConstantVal());
            }
            return value;
        }

        // 使用栈来处理运算符优先级
        Deque<BigDecimal> operandStack = new ArrayDeque<>();
        Deque<DimOperationFundamentalTypeEnum> operatorStack = new ArrayDeque<>();

        for (int i = 0; i < constantExpr.size(); i++) {
            DimOperationDetailDTO dto = constantExpr.get(i);

            if (isConstant(dto)) {
                BigDecimal value = getConstantValueOptimized(dto);
                if (value == null) {
                    throw new IllegalArgumentException("无效的常数值: " + dto.getConstantVal());
                }
                operandStack.push(value);
            } else if (isOperator(dto)) {
                DimOperationFundamentalTypeEnum currentOp = dto.getFundamentalType();

                // 处理运算符优先级
                while (!operatorStack.isEmpty() &&
                        currentOp.getPrecedence() <= operatorStack.peek().getPrecedence()) {
                    applyOperator(operandStack, operatorStack.pop());
                }

                operatorStack.push(currentOp);
            }
        }

        // 处理剩余的运算符
        while (!operatorStack.isEmpty()) {
            applyOperator(operandStack, operatorStack.pop());
        }

        if (operandStack.size() != 1) {
            throw new IllegalStateException("常量表达式计算错误，结果栈中有 " + operandStack.size() + " 个元素");
        }

        return operandStack.pop();
    }

    /**
     * 应用运算符到操作数栈
     */
    private static void applyOperator(Deque<BigDecimal> operandStack, DimOperationFundamentalTypeEnum operator) {
        if (operandStack.size() < 2) {
            throw new IllegalStateException("操作数不足，无法应用运算符: " + operator.getSymbol());
        }

        BigDecimal b = operandStack.pop();
        BigDecimal a = operandStack.pop();
        BigDecimal result = operator.apply(a, b);
        operandStack.push(result);
    }

    /**
     * 增强的常量折叠 - 处理更复杂的情况
     */
    private static List<DimOperationDetailDTO> foldConstantsAdvanced(List<DimOperationDetailDTO> details) {
        List<DimOperationDetailDTO> result = new ArrayList<>();
        int i = 0;

        while (i < details.size()) {
            // 寻找括号内的常量表达式
            if (isLeftBracket(details.get(i))) {
                int bracketEnd = findMatchingRightBracket(details, i);
                if (bracketEnd != -1) {
                    List<DimOperationDetailDTO> bracketContent = details.subList(i + 1, bracketEnd);

                    // 检查括号内是否全是常量表达式
                    if (isAllConstants(bracketContent)) {
                        try {
                            BigDecimal bracketResult = evaluateConstantExpression(bracketContent);
                            result.add(createConstant(bracketResult.toString()));
                            i = bracketEnd + 1;
                            continue;
                        } catch (Exception e) {
                            log.error("[DimOperation] 括号内常量折叠失败: {}", e.getMessage(), e);
                        }
                    }
                }
            }

            // 处理普通的常量表达式
            int constantStart = findConstantExpressionStart(details, i);
            if (constantStart != -1) {
                int constantEnd = findConstantExpressionEnd(details, constantStart);
                if (constantEnd > constantStart + 2) {
                    List<DimOperationDetailDTO> constantExpr = details.subList(constantStart, constantEnd + 1);
                    try {
                        BigDecimal constantResult = evaluateConstantExpression(constantExpr);

                        // 添加之前的非常量部分
                        for (int j = i; j < constantStart; j++) {
                            result.add(details.get(j));
                        }

                        result.add(createConstant(constantResult.toString()));
                        i = constantEnd + 1;
                        continue;
                    } catch (Exception e) {
                        log.error("[DimOperation] 常量折叠失败: {}", e.getMessage(), e);
                    }
                }
            }

            result.add(details.get(i));
            i++;
        }

        return result;
    }

    /**
     * 寻找匹配的右括号
     */
    private static int findMatchingRightBracket(List<DimOperationDetailDTO> details, int leftBracketIndex) {
        int bracketCount = 1;
        for (int i = leftBracketIndex + 1; i < details.size(); i++) {
            if (isLeftBracket(details.get(i))) {
                bracketCount++;
            } else if (isRightBracket(details.get(i))) {
                bracketCount--;
                if (bracketCount == 0) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * 检查表达式是否全部由常量和运算符组成
     */
    private static boolean isAllConstants(List<DimOperationDetailDTO> expr) {
        return expr.stream().allMatch(dto ->
                isConstant(dto) || isOperator(dto));
    }

    /**
     * 代数简化优化
     * 应用数学恒等式简化表达式，如 x + 0 = x, x * 1 = x, x * 0 = 0 等
     */
    private static List<DimOperationDetailDTO> simplifyAlgebraically(List<DimOperationDetailDTO> details) {
        if (details.size() < 3) {
            return details; // 太短的表达式无需简化
        }

        List<DimOperationDetailDTO> result = new ArrayList<>();
        int i = 0;

        while (i < details.size()) {
            // 检查是否可以应用代数简化规则
            AlgebraicSimplification simplification = findAlgebraicSimplification(details, i);

            if (simplification != null) {
                // 添加简化前的元素
                for (int j = i; j < simplification.startIndex; j++) {
                    result.add(details.get(j));
                }

                // 添加简化后的结果
                result.addAll(simplification.simplifiedElements);

                i = simplification.endIndex + 1;
            } else {
                // 没有找到可简化的模式，添加当前元素
                result.add(details.get(i));
                i++;
            }
        }

        return result;
    }

    /**
     * 代数简化结果封装类
     */
    private static class AlgebraicSimplification {
        final int startIndex;
        final int endIndex;
        final List<DimOperationDetailDTO> simplifiedElements;

        AlgebraicSimplification(int startIndex, int endIndex, List<DimOperationDetailDTO> simplifiedElements) {
            this.startIndex = startIndex;
            this.endIndex = endIndex;
            this.simplifiedElements = simplifiedElements;
        }
    }

    /**
     * 寻找可以应用的代数简化规则
     */
    private static AlgebraicSimplification findAlgebraicSimplification(List<DimOperationDetailDTO> details, int startIndex) {
        // 检查三元组模式：操作数 运算符 操作数
        if (startIndex + 2 < details.size()) {
            DimOperationDetailDTO left = details.get(startIndex);
            DimOperationDetailDTO operator = details.get(startIndex + 1);
            DimOperationDetailDTO right = details.get(startIndex + 2);

            if ((isVariable(left) || isConstant(left)) &&
                    isOperator(operator) &&
                    (isVariable(right) || isConstant(right))) {

                return applyAlgebraicRules(left, operator, right, startIndex);
            }
        }

        return null;
    }

    /**
     * 应用具体的代数简化规则
     */
    private static AlgebraicSimplification applyAlgebraicRules(DimOperationDetailDTO left,
                                                               DimOperationDetailDTO operator,
                                                               DimOperationDetailDTO right,
                                                               int startIndex) {
        DimOperationFundamentalTypeEnum opType = operator.getFundamentalType();

        // 规则1: x + 0 = x 或 0 + x = x
        if (opType == DimOperationFundamentalTypeEnum.ADD) {
            if (isZeroConstant(right)) {
                return new AlgebraicSimplification(startIndex, startIndex + 2, Arrays.asList(left));
            }
            if (isZeroConstant(left)) {
                return new AlgebraicSimplification(startIndex, startIndex + 2, Arrays.asList(right));
            }
        }

        // 规则2: x - 0 = x
        if (opType == DimOperationFundamentalTypeEnum.SUBTRACT) {
            if (isZeroConstant(right)) {
                return new AlgebraicSimplification(startIndex, startIndex + 2, Arrays.asList(left));
            }
            // 规则2.1: 0 - x = -x (这里简化为保持原样，因为没有负号运算符)
        }

        // 规则3: x * 1 = x 或 1 * x = x
        if (opType == DimOperationFundamentalTypeEnum.MULTIPLY) {
            if (isOneConstant(right)) {
                return new AlgebraicSimplification(startIndex, startIndex + 2, Arrays.asList(left));
            }
            if (isOneConstant(left)) {
                return new AlgebraicSimplification(startIndex, startIndex + 2, Arrays.asList(right));
            }

            // 规则4: x * 0 = 0 或 0 * x = 0
            if (isZeroConstant(right) || isZeroConstant(left)) {
                return new AlgebraicSimplification(startIndex, startIndex + 2,
                        Arrays.asList(createConstant("0")));
            }
        }

        // 规则5: x / 1 = x
        if (opType == DimOperationFundamentalTypeEnum.DIVIDE) {
            if (isOneConstant(right)) {
                return new AlgebraicSimplification(startIndex, startIndex + 2, Arrays.asList(left));
            }
            // 规则5.1: 0 / x = 0 (x != 0)
            if (isZeroConstant(left) && !isZeroConstant(right)) {
                return new AlgebraicSimplification(startIndex, startIndex + 2,
                        Arrays.asList(createConstant("0")));
            }
        }

        // 检查更复杂的规则
        return applyAdvancedAlgebraicRules(left, operator, right, startIndex);
    }

    /**
     * 应用高级代数简化规则
     */
    private static AlgebraicSimplification applyAdvancedAlgebraicRules(DimOperationDetailDTO left,
                                                                       DimOperationDetailDTO operator,
                                                                       DimOperationDetailDTO right,
                                                                       int startIndex) {
        DimOperationFundamentalTypeEnum opType = operator.getFundamentalType();

        // 规则6: 相同变量的运算 (需要检查变量ID)
        if (isVariable(left) && isVariable(right) &&
                Objects.equals(left.getDimVariableId(), right.getDimVariableId())) {

            if (opType == DimOperationFundamentalTypeEnum.SUBTRACT) {
                // x - x = 0
                return new AlgebraicSimplification(startIndex, startIndex + 2,
                        Arrays.asList(createConstant("0")));
            }
            if (opType == DimOperationFundamentalTypeEnum.DIVIDE) {
                // x / x = 1 (假设 x != 0)
                return new AlgebraicSimplification(startIndex, startIndex + 2,
                        Arrays.asList(createConstant("1")));
            }
        }

        // 规则7: 常量运算的进一步优化
        if (isConstant(left) && isConstant(right)) {
            try {
                BigDecimal leftVal = getConstantValueOptimized(left);
                BigDecimal rightVal = getConstantValueOptimized(right);

                if (leftVal != null && rightVal != null) {
                    BigDecimal result = opType.apply(leftVal, rightVal);
                    return new AlgebraicSimplification(startIndex, startIndex + 2,
                            Arrays.asList(createConstant(result.toString())));
                }
            } catch (Exception e) {
                log.error("[DimOperation] 常量运算简化失败: {}", e.getMessage(), e);
            }
        }

        return null;
    }

    /**
     * 多轮代数简化 - 重复应用简化规则直到无法进一步简化
     */
    private static List<DimOperationDetailDTO> simplifyAlgebraicallyMultiPass(List<DimOperationDetailDTO> details) {
        List<DimOperationDetailDTO> current = details;
        List<DimOperationDetailDTO> previous;
        int maxPasses = 5; // 防止无限循环
        int passCount = 0;

        do {
            previous = current;
            current = simplifyAlgebraically(current);
            passCount++;
        } while (!current.equals(previous) && passCount < maxPasses);

        if (passCount >= maxPasses) {
            log.debug("[DimOperation] 代数简化达到最大轮次限制: {}", maxPasses);
        }

        return current;
    }

    /**
     * 检查是否为常量0
     */
    private static boolean isZeroConstant(DimOperationDetailDTO dto) {
        return isZeroConstantOptimized(dto);
    }

    /**
     * 检查是否为常量1
     */
    private static boolean isOneConstant(DimOperationDetailDTO dto) {
        return isOneConstantOptimized(dto);
    }

    /**
     * 生成缓存键 - 线程安全的版本
     */
    private static String generateCacheKey(List<DimOperationDetailDTO> details, Set<Long> variableIds) {
        // 使用更简化的缓存键策略，提高命中率
        int expressionHash = 0;
        int variableHash = 0;

        // 计算表达式结构的哈希值（不包含变量值）
        for (DimOperationDetailDTO dto : details) {
            DimOperationDetailTypeEnum type = dto.getDimOperationDetailType();
            switch (type) {
                case VARIABLE:
                    expressionHash = expressionHash * 31 + 1; // 变量统一标识
                    break;
                case CONSTANT:
                    // 常数需要包含值的哈希，因为不同的常数值会影响缓存
                    String constantVal = dto.getConstantVal();
                    if (constantVal != null) {
                        expressionHash = expressionHash * 31 + (constantVal.hashCode() + 5);
                    } else {
                        expressionHash = expressionHash * 31 + 5;
                    }
                    break;
                case FUNDAMENTAL:
                    DimOperationFundamentalTypeEnum fundType = dto.getFundamentalType();
                    if (fundType != null) {
                        expressionHash = expressionHash * 31 + (fundType.ordinal() + 10);
                    }
                    break;
                case BRACKET:
                    DimOperationBracketTypeEnum bracketType = dto.getBracketType();
                    if (bracketType != null) {
                        expressionHash = expressionHash * 31 + (bracketType.ordinal() + 20);
                    }
                    break;
            }
        }

        // 计算变量ID集合的哈希值
        if (!variableIds.isEmpty()) {
            variableHash = variableIds.hashCode();
        }

        // 组合哈希值生成简洁的缓存键
        return "E" + expressionHash + "_V" + variableHash;
    }

    /**
     * 生成缓存键
     */
    private static String generateOptimizationCacheKey(List<DimOperationDetailDTO> details, OptimizationConfig config) {
        // 使用哈希值生成更简洁的缓存键
        int configHash = 0;
        if (config.isConstantFolding()) configHash += 1;
        if (config.isRedundantBracketRemoval()) configHash += 2;
        if (config.isAlgebraicSimplification()) configHash += 4;

        int detailsHash = 0;
        for (DimOperationDetailDTO dto : details) {
            detailsHash = detailsHash * 31 + dto.getDimOperationDetailType().ordinal();
            if (dto.getDimVariableId() != null) {
                detailsHash = detailsHash * 31 + dto.getDimVariableId().hashCode();
            } else if (dto.getConstantVal() != null) {
                detailsHash = detailsHash * 31 + dto.getConstantVal().hashCode();
            } else if (dto.getFundamentalType() != null) {
                detailsHash = detailsHash * 31 + dto.getFundamentalType().ordinal();
            } else if (dto.getBracketType() != null) {
                detailsHash = detailsHash * 31 + dto.getBracketType().ordinal();
            }
        }

        return "OPT_" + configHash + "_" + detailsHash;
    }

    /**
     * 获取缓存统计信息
     */
    public static String getCacheStats() {
        return String.format(
                "Postfix Cache: %s, Optimization Cache: %s",
                POSTFIX_CACHE.stats(),
                OPTIMIZED_EXPRESSION_CACHE.stats()
        );
    }

    /**
     * 清空缓存 - 线程安全版本
     */
    public static void clearCache() {
        maintenanceWriteLock.lock();
        try {
            POSTFIX_CACHE.invalidateAll();
            OPTIMIZED_EXPRESSION_CACHE.invalidateAll();
            // 强制垃圾回收缓存相关的内存
            POSTFIX_CACHE.cleanUp();
            OPTIMIZED_EXPRESSION_CACHE.cleanUp();
            log.info("[DimOperation] 缓存已清空");
        } finally {
            maintenanceWriteLock.unlock();
        }
    }

    /**
     * 获取内存使用情况
     */
    public static String getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        return String.format(
                "内存使用: %dMB/%dMB (最大: %dMB), 后缀缓存: %d项, 优化缓存: %d项",
                usedMemory / 1024 / 1024,
                totalMemory / 1024 / 1024,
                maxMemory / 1024 / 1024,
                POSTFIX_CACHE.estimatedSize(),
                OPTIMIZED_EXPRESSION_CACHE.estimatedSize()
        );
    }

    /**
     * 手动触发缓存清理 - 线程安全版本
     */
    public static void performMaintenance() {
        maintenanceWriteLock.lock();
        try {
            POSTFIX_CACHE.cleanUp();
            OPTIMIZED_EXPRESSION_CACHE.cleanUp();

            // 清理符号缓存中的无用条目
            if (SYMBOL_CACHE.size() > 100) {
                // 如果符号缓存过大，清理一部分
                SYMBOL_CACHE.clear();
            }

            // 清理 BigDecimal 常用值缓存（保留预定义的常用值）
            cleanupBigDecimalCache();

            log.debug("[DimOperation] 缓存维护完成");
            // 不强制垃圾回收，让JVM自己决定
        } finally {
            maintenanceWriteLock.unlock();
        }
    }

    /**
     * 清理 BigDecimal 缓存，保留常用值
     */
    private static void cleanupBigDecimalCache() {
        // 如果缓存过大，清理非预定义的值
        if (COMMON_BIGDECIMAL_CACHE.size() > 50) {
            // 保留预定义的常用值
            Set<String> predefinedKeys = Set.of("0", "0.0", "1", "1.0", "2", "2.0",
                    "3", "3.0", "4", "4.0", "5", "5.0",
                    "10", "10.0");

            COMMON_BIGDECIMAL_CACHE.entrySet().removeIf(entry ->
                    !predefinedKeys.contains(entry.getKey()));

            log.debug("[DimOperation] BigDecimal 缓存已清理，保留 {} 个预定义值",
                    COMMON_BIGDECIMAL_CACHE.size());
        }
    }

    /**
     * 获取详细的缓存统计信息
     */
    public static String getDetailedCacheStats() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 缓存详细统计 ===\n");

        // 后缀表达式缓存统计
        sb.append("后缀表达式缓存:\n");
        sb.append("  大小: ").append(POSTFIX_CACHE.estimatedSize()).append(" 项\n");
        sb.append("  统计: ").append(POSTFIX_CACHE.stats()).append("\n");

        // 优化表达式缓存统计
        sb.append("优化表达式缓存:\n");
        sb.append("  大小: ").append(OPTIMIZED_EXPRESSION_CACHE.estimatedSize()).append(" 项\n");
        sb.append("  统计: ").append(OPTIMIZED_EXPRESSION_CACHE.stats()).append("\n");

        // 符号缓存统计
        sb.append("符号缓存:\n");
        sb.append("  大小: ").append(SYMBOL_CACHE.size()).append(" 项\n");

        // BigDecimal 缓存统计
        sb.append("BigDecimal 常用值缓存:\n");
        sb.append("  大小: ").append(COMMON_BIGDECIMAL_CACHE.size()).append(" 项\n");
        sb.append("  缓存键: ").append(COMMON_BIGDECIMAL_CACHE.keySet()).append("\n");

        return sb.toString();
    }

    /**
     * 内存健康检查
     */
    public static MemoryHealthReport checkMemoryHealth() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        long postfixCacheSize = POSTFIX_CACHE.estimatedSize();
        long optimizedCacheSize = OPTIMIZED_EXPRESSION_CACHE.estimatedSize();
        int symbolCacheSize = SYMBOL_CACHE.size();

        MemoryHealthStatus status;
        List<String> recommendations = new ArrayList<>();

        // 内存使用率检查
        if (memoryUsagePercent > 90) {
            status = MemoryHealthStatus.CRITICAL;
            recommendations.add("内存使用率过高(" + String.format("%.1f", memoryUsagePercent) + "%)，建议立即清理缓存");
        } else if (memoryUsagePercent > 75) {
            status = MemoryHealthStatus.WARNING;
            recommendations.add("内存使用率较高(" + String.format("%.1f", memoryUsagePercent) + "%)，建议执行维护");
        } else {
            status = MemoryHealthStatus.HEALTHY;
        }

        // 缓存大小检查
        if (postfixCacheSize > 400) {
            recommendations.add("后缀表达式缓存过大(" + postfixCacheSize + "项)，建议清理");
        }
        if (optimizedCacheSize > 150) {
            recommendations.add("优化表达式缓存过大(" + optimizedCacheSize + "项)，建议清理");
        }
        if (symbolCacheSize > 80) {
            recommendations.add("符号缓存过大(" + symbolCacheSize + "项)，建议清理");
        }

        return new MemoryHealthReport(status, memoryUsagePercent,
                postfixCacheSize, optimizedCacheSize, symbolCacheSize, recommendations);
    }

    /**
     * 内存健康状态枚举
     */
    public enum MemoryHealthStatus {
        HEALTHY("健康"),
        WARNING("警告"),
        CRITICAL("严重");

        private final String description;

        MemoryHealthStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 内存健康报告
     */
    public static class MemoryHealthReport {
        private final MemoryHealthStatus status;
        private final double memoryUsagePercent;
        private final long postfixCacheSize;
        private final long optimizedCacheSize;
        private final int symbolCacheSize;
        private final List<String> recommendations;

        public MemoryHealthReport(MemoryHealthStatus status, double memoryUsagePercent,
                                  long postfixCacheSize, long optimizedCacheSize, int symbolCacheSize,
                                  List<String> recommendations) {
            this.status = status;
            this.memoryUsagePercent = memoryUsagePercent;
            this.postfixCacheSize = postfixCacheSize;
            this.optimizedCacheSize = optimizedCacheSize;
            this.symbolCacheSize = symbolCacheSize;
            this.recommendations = new ArrayList<>(recommendations);
        }

        public MemoryHealthStatus getStatus() {
            return status;
        }

        public double getMemoryUsagePercent() {
            return memoryUsagePercent;
        }

        public long getPostfixCacheSize() {
            return postfixCacheSize;
        }

        public long getOptimizedCacheSize() {
            return optimizedCacheSize;
        }

        public int getSymbolCacheSize() {
            return symbolCacheSize;
        }

        public List<String> getRecommendations() {
            return new ArrayList<>(recommendations);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== 内存健康报告 ===\n");
            sb.append("状态: ").append(status.getDescription()).append("\n");
            sb.append("内存使用率: ").append(String.format("%.1f", memoryUsagePercent)).append("%\n");
            sb.append("后缀缓存: ").append(postfixCacheSize).append(" 项\n");
            sb.append("优化缓存: ").append(optimizedCacheSize).append(" 项\n");
            sb.append("符号缓存: ").append(symbolCacheSize).append(" 项\n");

            if (!recommendations.isEmpty()) {
                sb.append("建议:\n");
                for (String recommendation : recommendations) {
                    sb.append("  - ").append(recommendation).append("\n");
                }
            }

            return sb.toString();
        }
    }

    /**
     * 自动内存监控和维护 - 线程安全版本
     */
    private static void checkAndPerformAutoMaintenance() {
        // 原子性增加执行计数器
        long currentCount = executionCounter.incrementAndGet();
        long currentTime = System.currentTimeMillis();

        // 检查是否需要执行维护
        boolean needMaintenance = false;

        // 条件1：执行次数达到阈值
        if (currentCount % MAINTENANCE_EXECUTION_THRESHOLD == 0) {
            needMaintenance = true;
        }

        if (!needMaintenance) {
            // 条件2：时间间隔达到阈值
            if (currentTime - lastMaintenanceTime > MAINTENANCE_INTERVAL_MS) {
                needMaintenance = true;
            }
        }
        MemoryHealthReport healthReport = null;
        if (!needMaintenance) {
            // 条件3：内存使用率过高
            healthReport = checkMemoryHealth();
            if (healthReport.getStatus() == MemoryHealthStatus.WARNING ||
                    healthReport.getStatus() == MemoryHealthStatus.CRITICAL) {
                needMaintenance = true;
            }
        }

        if (needMaintenance) {
            // 尝试获取写锁，如果获取不到说明有其他线程在维护，跳过
            if (maintenanceWriteLock.tryLock()) {
                try {
                    // 双重检查，避免重复维护
                    if (currentTime - lastMaintenanceTime > MAINTENANCE_INTERVAL_MS / 2) {
                        performAutoMaintenance(healthReport);
                        lastMaintenanceTime = currentTime;
                    }
                } finally {
                    maintenanceWriteLock.unlock();
                }
            }
        }
    }

    /**
     * 执行自动维护 - 已在写锁保护下执行
     */
    private static void performAutoMaintenance(MemoryHealthReport healthReport) {
        try {
            log.debug("[DimOperation] 开始自动维护，当前状态: {}", healthReport.getStatus().getDescription());

            // 根据健康状态决定维护策略
            switch (healthReport.getStatus()) {
                case CRITICAL:
                    // 严重情况：清空所有缓存（不需要再获取锁）
                    POSTFIX_CACHE.invalidateAll();
                    OPTIMIZED_EXPRESSION_CACHE.invalidateAll();
                    POSTFIX_CACHE.cleanUp();
                    OPTIMIZED_EXPRESSION_CACHE.cleanUp();
                    log.warn("[DimOperation] 内存使用严重，已清空所有缓存");
                    break;

                case WARNING:
                    // 警告情况：执行标准维护（不需要再获取锁）
                    POSTFIX_CACHE.cleanUp();
                    OPTIMIZED_EXPRESSION_CACHE.cleanUp();
                    if (SYMBOL_CACHE.size() > 100) {
                        SYMBOL_CACHE.clear();
                    }
                    log.info("[DimOperation] 内存使用警告，已执行维护");
                    break;

                case HEALTHY:
                    // 健康情况：轻量级清理
                    POSTFIX_CACHE.cleanUp();
                    OPTIMIZED_EXPRESSION_CACHE.cleanUp();
                    log.debug("[DimOperation] 执行轻量级维护");
                    break;
            }

            // 记录维护后的状态
            MemoryHealthReport afterReport = checkMemoryHealth();
            log.debug("[DimOperation] 维护完成，内存使用率: {}% -> {}%",
                    healthReport.getMemoryUsagePercent(), afterReport.getMemoryUsagePercent());

        } catch (Exception e) {
            log.error("[DimOperation] 自动维护过程中发生异常", e);
        }
    }

    /**
     * 获取执行统计信息 - 线程安全版本
     */
    public static String getExecutionStats() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastMaintenance = currentTime - lastMaintenanceTime;
        long currentCount = executionCounter.get();

        return String.format(
                "执行统计: 总执行次数=%d, 距离上次维护=%d分钟, 下次维护倒计时=%d次执行",
                currentCount,
                timeSinceLastMaintenance / 60000,
                MAINTENANCE_EXECUTION_THRESHOLD - (currentCount % MAINTENANCE_EXECUTION_THRESHOLD)
        );
    }

    /**
     * 重置执行统计 - 线程安全版本
     */
    public static void resetExecutionStats() {
        executionCounter.set(0);
        lastMaintenanceTime = System.currentTimeMillis();
        log.info("[DimOperation] 执行统计已重置");
    }

    /**
     * 全面测试优化功能 - 常量折叠和代数简化
     */
    private static void testOptimizationFeatures() {
        System.out.println("\n=== 优化功能全面测试 ===");

        // 测试1: 常量折叠
        System.out.println("\n--- 测试1: 常量折叠 ---");
        testConstantFolding();

        // 测试2: 代数简化
        System.out.println("\n--- 测试2: 代数简化 ---");
        testAlgebraicSimplification();

        // 测试3: 复合优化
        System.out.println("\n--- 测试3: 复合优化 ---");
        testCombinedOptimization();

        // 测试4: 括号优化
        System.out.println("\n--- 测试4: 括号优化 ---");
        testBracketOptimization();
    }

    /**
     * 测试常量折叠功能
     */
    private static void testConstantFolding() {
        // 创建包含常量表达式的测试用例: x + (2 + 3) * 4
        List<DimOperationDetailDTO> expr = new ArrayList<>();
        expr.add(createVariable("x", 1L));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expr.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expr.add(createConstant("2"));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expr.add(createConstant("3"));
        expr.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expr.add(createConstant("4"));

        Map<Long, BigDecimal> values = Map.of(1L, new BigDecimal("10"));

        System.out.println("原始表达式: " + printExpression(expr));

        ExecutionResult result = executeExpressionWithOptimization(
                expr, values, TraceConfig.debug(), OptimizationConfig.basic());
        System.out.println("计算结果: " + result.getResult() + " (期望: 30)");

        ExecutionResult fullResult = executeExpressionWithOptimization(
                expr, values, TraceConfig.debug(), OptimizationConfig.full());
        System.out.println("完整优化结果: " + fullResult.getResult());
    }

    /**
     * 测试代数简化功能
     */
    private static void testAlgebraicSimplification() {
        // 测试用例1: x + 0
        List<DimOperationDetailDTO> expr1 = Arrays.asList(
                createVariable("x", 1L),
                createOperator(DimOperationFundamentalTypeEnum.ADD),
                createConstant("0")
        );

        // 测试用例2: x * 1
        List<DimOperationDetailDTO> expr2 = Arrays.asList(
                createVariable("x", 1L),
                createOperator(DimOperationFundamentalTypeEnum.MULTIPLY),
                createConstant("1")
        );

        // 测试用例3: x * 0
        List<DimOperationDetailDTO> expr3 = Arrays.asList(
                createVariable("x", 1L),
                createOperator(DimOperationFundamentalTypeEnum.MULTIPLY),
                createConstant("0")
        );

        // 测试用例4: x - x
        List<DimOperationDetailDTO> expr4 = Arrays.asList(
                createVariable("x", 1L),
                createOperator(DimOperationFundamentalTypeEnum.SUBTRACT),
                createVariable("x", 1L)
        );

        Map<Long, BigDecimal> values = Map.of(1L, new BigDecimal("5"));

        System.out.println("测试 x + 0:");
        System.out.println("  原始: " + printExpression(expr1));
        ExecutionResult result1 = executeExpressionWithOptimization(
                expr1, values, TraceConfig.disabled(), OptimizationConfig.full());
        System.out.println("  结果: " + result1.getResult() + " (期望: 5)");

        System.out.println("测试 x * 1:");
        System.out.println("  原始: " + printExpression(expr2));
        ExecutionResult result2 = executeExpressionWithOptimization(
                expr2, values, TraceConfig.disabled(), OptimizationConfig.full());
        System.out.println("  结果: " + result2.getResult() + " (期望: 5)");

        System.out.println("测试 x * 0:");
        System.out.println("  原始: " + printExpression(expr3));
        ExecutionResult result3 = executeExpressionWithOptimization(
                expr3, values, TraceConfig.disabled(), OptimizationConfig.full());
        System.out.println("  结果: " + result3.getResult() + " (期望: 0)");

        System.out.println("测试 x - x:");
        System.out.println("  原始: " + printExpression(expr4));
        ExecutionResult result4 = executeExpressionWithOptimization(
                expr4, values, TraceConfig.disabled(), OptimizationConfig.full());
        System.out.println("  结果: " + result4.getResult() + " (期望: 0)");
    }

    /**
     * 测试复合优化功能
     */
    private static void testCombinedOptimization() {
        // 创建复杂表达式: (x + 0) * (2 + 3) + (y * 1) - (z - z)
        List<DimOperationDetailDTO> expr = new ArrayList<>();
        expr.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expr.add(createVariable("x", 1L));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expr.add(createConstant("0"));
        expr.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expr.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expr.add(createConstant("2"));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expr.add(createConstant("3"));
        expr.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expr.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expr.add(createVariable("y", 2L));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expr.add(createConstant("1"));
        expr.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.SUBTRACT));
        expr.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expr.add(createVariable("z", 3L));
        expr.add(createOperator(DimOperationFundamentalTypeEnum.SUBTRACT));
        expr.add(createVariable("z", 3L));
        expr.add(createBracket(DimOperationBracketTypeEnum.RIGHT));

        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("2"),
                2L, new BigDecimal("3"),
                3L, new BigDecimal("4")
        );

        System.out.println("复杂表达式: " + printExpression(expr));

        ExecutionResult noOptResult = executeExpressionWithOptimization(
                expr, values, TraceConfig.disabled(), OptimizationConfig.disabled());
        System.out.println("无优化结果: " + noOptResult.getResult());

        ExecutionResult fullOptResult = executeExpressionWithOptimization(
                expr, values, TraceConfig.debug(), OptimizationConfig.full());
        System.out.println("完整优化结果: " + fullOptResult.getResult());
        System.out.println("期望结果: " + (2 * 5 + 3 - 0) + " = 13");
    }

    /**
     * 测试括号优化功能
     */
    private static void testBracketOptimization() {
        // 创建包含冗余括号的表达式: (x) + (y) * (z)
        List<DimOperationDetailDTO> expr = Arrays.asList(
                createBracket(DimOperationBracketTypeEnum.LEFT),
                createVariable("x", 1L),
                createBracket(DimOperationBracketTypeEnum.RIGHT),
                createOperator(DimOperationFundamentalTypeEnum.ADD),
                createBracket(DimOperationBracketTypeEnum.LEFT),
                createVariable("y", 2L),
                createBracket(DimOperationBracketTypeEnum.RIGHT),
                createOperator(DimOperationFundamentalTypeEnum.MULTIPLY),
                createBracket(DimOperationBracketTypeEnum.LEFT),
                createVariable("z", 3L),
                createBracket(DimOperationBracketTypeEnum.RIGHT)
        );

        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("1"),
                2L, new BigDecimal("2"),
                3L, new BigDecimal("3")
        );

        System.out.println("冗余括号表达式: " + printExpression(expr));

        ExecutionResult result = executeExpressionWithOptimization(
                expr, values, TraceConfig.debug(), OptimizationConfig.basic());
        System.out.println("优化后结果: " + result.getResult() + " (期望: 7)");
    }

    /**
     * 测试 BigDecimal 优化效果
     */
    private static void testBigDecimalOptimization() {
        System.out.println("\n--- BigDecimal 对象创建优化测试 ---");

        // 创建包含大量常数的表达式
        List<DimOperationDetailDTO> expr = new ArrayList<>();

        // 添加常用常数值，测试缓存效果
        String[] commonValues = {"0", "1", "2", "3", "4", "5", "10", "0.0", "1.0"};

        for (int i = 0; i < commonValues.length; i++) {
            if (i > 0) {
                expr.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
            }
            expr.add(createConstant(commonValues[i]));
        }

        System.out.println("测试表达式: " + printExpression(expr));

        // 测试多次访问相同常数值的性能
        long startTime = System.nanoTime();

        for (int i = 0; i < 1000; i++) {
            for (DimOperationDetailDTO dto : expr) {
                if (isConstant(dto)) {
                    // 使用优化版本获取 BigDecimal
                    BigDecimal value = getConstantValueOptimized(dto);
                    // 模拟使用该值
                    if (value != null) {
                        value.toString();
                    }
                }
            }
        }

        long endTime = System.nanoTime();
        long optimizedTime = endTime - startTime;

        System.out.println("优化版本耗时: " + (optimizedTime / 1_000_000.0) + " ms");

        // 测试传统方式的性能（仅作对比）
        startTime = System.nanoTime();

        for (int i = 0; i < 1000; i++) {
            for (DimOperationDetailDTO dto : expr) {
                if (isConstant(dto)) {
                    try {
                        // 传统方式：每次都创建新的 BigDecimal
                        BigDecimal value = new BigDecimal(dto.getConstantVal());
                        value.toString();
                    } catch (NumberFormatException e) {
                        // 忽略错误
                    }
                }
            }
        }

        endTime = System.nanoTime();
        long traditionalTime = endTime - startTime;

        System.out.println("传统方式耗时: " + (traditionalTime / 1_000_000.0) + " ms");
        System.out.println("性能提升: " + String.format("%.1f", (double) traditionalTime / optimizedTime) + "x");

        // 显示缓存统计
        System.out.println("BigDecimal 缓存大小: " + COMMON_BIGDECIMAL_CACHE.size());
        System.out.println("缓存内容: " + COMMON_BIGDECIMAL_CACHE.keySet());

        // 测试 DTO 内置缓存
        System.out.println("\n--- DTO 内置缓存测试 ---");
        DimOperationDetailDTO testDto = createConstant("123.456");

        startTime = System.nanoTime();
        for (int i = 0; i < 10000; i++) {
            testDto.getConstantValueAsBigDecimal();
        }
        endTime = System.nanoTime();

        System.out.println("DTO 缓存访问 10000 次耗时: " + (endTime - startTime) / 1_000_000.0 + " ms");

        // 测试缓存失效
        testDto.setConstantVal("789.123");
        BigDecimal newValue = testDto.getConstantValueAsBigDecimal();
        System.out.println("缓存失效测试 - 新值: " + newValue);
    }

    /**
     * 测试优化功能
     */
    private static void testOptimization(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> values) {
        // 测试基本优化
        ExecutionResult basicResult = executeExpressionWithOptimization(
                expression, values, TraceConfig.debug(), OptimizationConfig.basic());
        System.out.println("基本优化结果: " + basicResult.getResult());
        System.out.println("基本优化执行时间: " + basicResult.getExecutionTimeMs() + "ms");

        // 测试完整优化
        ExecutionResult fullResult = executeExpressionWithOptimization(
                expression, values, TraceConfig.debug(), OptimizationConfig.full());
        System.out.println("完整优化结果: " + fullResult.getResult());
        System.out.println("完整优化执行时间: " + fullResult.getExecutionTimeMs() + "ms");

        // 测试冗余括号优化
        List<DimOperationDetailDTO> redundantExpression = new ArrayList<>();
        redundantExpression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        redundantExpression.add(createVariable("单独变量", 10L));
        redundantExpression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        redundantExpression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        redundantExpression.add(createVariable("乘数", 11L));

        Map<Long, BigDecimal> redundantValues = Map.of(
                10L, new BigDecimal("5"),
                11L, new BigDecimal("3")
        );

        System.out.println("原始表达式: " + printExpression(redundantExpression));
        ExecutionResult optimizedResult = executeExpressionWithOptimization(
                redundantExpression, redundantValues, TraceConfig.debug(), OptimizationConfig.basic());
        System.out.println("优化后结果: " + optimizedResult.getResult());
    }

    /**
     * 性能测试方法
     */
    private static void performanceTest(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> values) {
        int iterations = 10000;

        // 测试无跟踪版本性能
        long startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeExpression(expression, values);
        }
        long noTraceTime = (System.nanoTime() - startTime) / 1_000_000;

        // 测试带跟踪版本性能
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeExpressionWithTrace(expression, values, TraceConfig.disabled());
        }
        long withTraceTime = (System.nanoTime() - startTime) / 1_000_000;

        // 测试优化版本性能
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeExpressionWithOptimization(expression, values, TraceConfig.disabled(), OptimizationConfig.basic());
        }
        long optimizedTime = (System.nanoTime() - startTime) / 1_000_000;

        // 测试缓存效果（第二次运行）
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            executeExpressionWithOptimization(expression, values, TraceConfig.disabled(), OptimizationConfig.basic());
        }
        long cachedTime = (System.nanoTime() - startTime) / 1_000_000;

        System.out.println("执行 " + iterations + " 次:");
        System.out.println("无跟踪版本: " + noTraceTime + "ms");
        System.out.println("带跟踪版本(禁用): " + withTraceTime + "ms");
        System.out.println("优化版本(首次): " + optimizedTime + "ms");
        System.out.println("优化版本(缓存): " + cachedTime + "ms");
        System.out.println("缓存提升: " + (optimizedTime - cachedTime) + "ms (" +
                String.format("%.2f", (double) (optimizedTime - cachedTime) / optimizedTime * 100) + "%)");
    }

    // 辅助创建方法
    private static DimOperationDetailDTO createVariable(String name, Long id) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.VARIABLE);
        dto.setDimVariableName(name);
        dto.setDimVariableId(id);
        return dto;
    }

    private static DimOperationDetailDTO createOperator(DimOperationFundamentalTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.FUNDAMENTAL);
        dto.setFundamentalType(type);
        return dto;
    }

    private static DimOperationDetailDTO createBracket(DimOperationBracketTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.BRACKET);
        dto.setBracketType(type);
        return dto;
    }

    private static DimOperationDetailDTO createConstant(String value) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.CONSTANT);
        dto.setConstantVal(value);
        return dto;
    }

    /**
     * 并发执行多个表达式 - 支持批量计算
     */
    public static List<ExecutionResult> executeExpressionsConcurrently(
            @NotEmpty List<List<DimOperationDetailDTO>> expressionsList,
            @NotNull List<Map<Long, BigDecimal>> variableValueMapsList,
            TraceConfig traceConfig,
            OptimizationConfig optimizationConfig) {

        if (expressionsList.size() != variableValueMapsList.size()) {
            throw new IllegalArgumentException("表达式列表和变量值列表大小不匹配");
        }

        // 使用并行流进行并发计算
        return expressionsList.parallelStream()
                .map(expressions -> {
                    int index = expressionsList.indexOf(expressions);
                    Map<Long, BigDecimal> variableValues = variableValueMapsList.get(index);
                    return executeExpressionWithOptimization(expressions, variableValues, traceConfig, optimizationConfig);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取缓存性能报告
     */
    public static CachePerformanceReport getCachePerformanceReport() {
        return new CachePerformanceReport(
                POSTFIX_CACHE.stats(),
                OPTIMIZED_EXPRESSION_CACHE.stats(),
                SYMBOL_CACHE.size()
        );
    }

    /**
     * 缓存性能报告类
     */
    public static class CachePerformanceReport {
        private final CacheStats postfixStats;
        private final CacheStats optimizedStats;
        private final int symbolCacheSize;

        public CachePerformanceReport(CacheStats postfixStats,
                                      CacheStats optimizedStats,
                                      int symbolCacheSize) {
            this.postfixStats = postfixStats;
            this.optimizedStats = optimizedStats;
            this.symbolCacheSize = symbolCacheSize;
        }

        public double getPostfixHitRate() {
            return postfixStats.hitRate();
        }

        public double getOptimizedHitRate() {
            return optimizedStats.hitRate();
        }

        public long getPostfixHitCount() {
            return postfixStats.hitCount();
        }

        public long getOptimizedHitCount() {
            return optimizedStats.hitCount();
        }

        public boolean isPerformanceGood() {
            return getPostfixHitRate() > 0.7 && getOptimizedHitRate() > 0.6;
        }

        @Override
        public String toString() {
            return String.format(
                    "缓存性能报告:\n" +
                            "  后缀表达式缓存: 命中率=%.2f%%, 命中次数=%d, 请求次数=%d\n" +
                            "  优化表达式缓存: 命中率=%.2f%%, 命中次数=%d, 请求次数=%d\n" +
                            "  符号缓存大小: %d\n" +
                            "  整体性能: %s",
                    getPostfixHitRate() * 100, getPostfixHitCount(), postfixStats.requestCount(),
                    getOptimizedHitRate() * 100, getOptimizedHitCount(), optimizedStats.requestCount(),
                    symbolCacheSize,
                    isPerformanceGood() ? "良好" : "需要优化"
            );
        }
    }

    /**
     * 智能缓存调优 - 根据性能自动调整缓存策略
     */
    public static void performIntelligentCacheTuning() {
        CachePerformanceReport report = getCachePerformanceReport();

        log.info("[DimOperation] 缓存性能分析: {}", report);

        // 如果命中率过低，建议调整策略
        if (report.getPostfixHitRate() < 0.5) {
            log.warn("[DimOperation] 后缀表达式缓存命中率过低({}%), 建议检查表达式复用情况",
                    report.getPostfixHitRate() * 100);
        }

        if (report.getOptimizedHitRate() < 0.4) {
            log.warn("[DimOperation] 优化表达式缓存命中率过低({}%), 建议检查优化策略",
                    report.getOptimizedHitRate() * 100);
        }

        // 如果符号缓存过大，清理
        if (report.symbolCacheSize > 200) {
            SYMBOL_CACHE.clear();
            log.info("[DimOperation] 符号缓存过大，已清理");
        }
    }

    /**
     * 获取线程安全状态报告
     */
    public static String getThreadSafetyReport() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 线程安全状态报告 ===\n");
        sb.append("执行计数器: AtomicLong (线程安全)\n");
        sb.append("缓存实现: Caffeine (线程安全)\n");
        sb.append("符号缓存: ConcurrentHashMap (线程安全)\n");
        sb.append("维护锁: ReentrantReadWriteLock (读写分离)\n");
        sb.append("当前读锁持有数: ").append(maintenanceLock.getReadLockCount()).append("\n");
        sb.append("当前写锁状态: ").append(maintenanceLock.isWriteLocked() ? "已锁定" : "未锁定").append("\n");
        sb.append("等待线程数: ").append(maintenanceLock.getQueueLength()).append("\n");
        return sb.toString();
    }

}