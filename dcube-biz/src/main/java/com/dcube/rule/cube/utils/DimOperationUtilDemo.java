package com.dcube.rule.cube.utils;

import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationFundamentalTypeEnum;
import com.dcube.rule.cube.dto.DimOperationDetailDTO;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;

/**
 * DimOperationUtil 线程安全演示程序
 */
public class DimOperationUtilDemo {

    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== DimOperationUtil 线程安全演示 ===\n");

        // 创建测试表达式: (a + b) * c
        List<DimOperationDetailDTO> expression = createTestExpression();
        Map<Long, BigDecimal> variables = Map.of(
                1L, new BigDecimal("10"),
                2L, new BigDecimal("20"),
                3L, new BigDecimal("5")
        );

        // 1. 基本功能测试
        System.out.println("1. 基本功能测试:");
        testBasicFunctionality(expression, variables);

        // 2. 并发安全性测试
        System.out.println("\n2. 并发安全性测试:");
        testConcurrentSafety(expression, variables);

        // 3. 缓存性能测试
        System.out.println("\n3. 缓存性能测试:");
        testCachePerformance(expression, variables);

        // 4. 内存管理测试
        System.out.println("\n4. 内存管理测试:");
        testMemoryManagement(expression, variables);

        // 5. 批量并发测试
        System.out.println("\n5. 批量并发测试:");
        testBatchConcurrency();

        System.out.println("\n=== 演示完成 ===");
    }

    private static void testBasicFunctionality(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> variables) {
        try {
            // 测试基本执行
            DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithOptimization(
                    expression, variables,
                    DimOperationUtil.TraceConfig.info(),
                    DimOperationUtil.OptimizationConfig.basic()
            );

            System.out.println("  表达式: (a + b) * c = (10 + 20) * 5");
            System.out.println("  计算结果: " + result.getResult());
            System.out.println("  执行时间: " + result.getExecutionTimeMs() + "ms");
            System.out.println("  内存使用: " + String.format("%.2f", result.getMemoryUsedKB()) + "KB");

        } catch (Exception e) {
            System.err.println("  基本功能测试失败: " + e.getMessage());
        }
    }

    private static void testConcurrentSafety(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> variables) throws InterruptedException {
        int threadCount = 10;
        int executionsPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        List<Future<Boolean>> futures = new ArrayList<>();

        long startTime = System.currentTimeMillis();

        // 启动多个线程并发执行
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            Future<Boolean> future = executor.submit(() -> {
                try {
                    BigDecimal expectedResult = new BigDecimal("150");
                    for (int j = 0; j < executionsPerThread; j++) {
                        DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithOptimization(
                                expression, variables,
                                DimOperationUtil.TraceConfig.disabled(),
                                DimOperationUtil.OptimizationConfig.basic()
                        );

                        if (!expectedResult.equals(result.getResult())) {
                            System.err.println("  线程 " + threadId + " 结果不一致: " + result.getResult());
                            return false;
                        }
                    }
                    return true;
                } finally {
                    latch.countDown();
                }
            });
            futures.add(future);
        }

        // 等待所有线程完成
        boolean success = latch.await(30, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();

        if (success) {
            // 检查所有结果
            boolean allSuccess = true;
            for (Future<Boolean> future : futures) {
                try {
                    if (!future.get()) {
                        allSuccess = false;
                        break;
                    }
                } catch (Exception e) {
                    allSuccess = false;
                    System.err.println("  线程执行异常: " + e.getMessage());
                }
            }

            if (allSuccess) {
                System.out.println("  ✓ 并发安全性测试通过");
                System.out.println("  线程数: " + threadCount + ", 每线程执行: " + executionsPerThread + " 次");
                System.out.println("  总执行时间: " + (endTime - startTime) + "ms");
                System.out.println("  平均每次执行: " + String.format("%.2f", (double)(endTime - startTime) / (threadCount * executionsPerThread)) + "ms");
            } else {
                System.err.println("  ✗ 并发安全性测试失败");
            }
        } else {
            System.err.println("  ✗ 并发测试超时");
        }

        executor.shutdown();
    }

    private static void testCachePerformance(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> variables) {
        // 清空缓存
        DimOperationUtil.clearCache();

        int iterations = 1000;

        // 第一次执行（冷缓存）
        long startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            DimOperationUtil.executeExpressionWithOptimization(
                    expression, variables,
                    DimOperationUtil.TraceConfig.disabled(),
                    DimOperationUtil.OptimizationConfig.basic()
            );
        }
        long coldTime = System.nanoTime() - startTime;

        // 第二次执行（热缓存）
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            DimOperationUtil.executeExpressionWithOptimization(
                    expression, variables,
                    DimOperationUtil.TraceConfig.disabled(),
                    DimOperationUtil.OptimizationConfig.basic()
            );
        }
        long hotTime = System.nanoTime() - startTime;

        // 获取缓存性能报告
        DimOperationUtil.CachePerformanceReport report = DimOperationUtil.getCachePerformanceReport();

        System.out.println("  冷缓存执行时间: " + (coldTime / 1_000_000) + "ms");
        System.out.println("  热缓存执行时间: " + (hotTime / 1_000_000) + "ms");
        System.out.println("  缓存加速比: " + String.format("%.2f", (double)coldTime / hotTime) + "x");
        System.out.println("  缓存命中率: " + String.format("%.2f", report.getPostfixHitRate() * 100) + "%");
        System.out.println("  缓存性能: " + (report.isPerformanceGood() ? "良好" : "需要优化"));
    }

    private static void testMemoryManagement(List<DimOperationDetailDTO> expression, Map<Long, BigDecimal> variables) {
        // 执行大量计算以测试内存管理
        for (int i = 0; i < 500; i++) {
            DimOperationUtil.executeExpressionWithOptimization(
                    expression, variables,
                    DimOperationUtil.TraceConfig.debug().withTimestamp(true),
                    DimOperationUtil.OptimizationConfig.full()
            );

            // 每100次检查一次内存状态
            if (i % 100 == 0) {
                DimOperationUtil.MemoryHealthReport healthReport = DimOperationUtil.checkMemoryHealth();
                if (healthReport.getStatus() != DimOperationUtil.MemoryHealthStatus.HEALTHY) {
                    System.out.println("  内存状态警告: " + healthReport.getStatus().getDescription());
                }
            }
        }

        // 最终内存健康检查
        DimOperationUtil.MemoryHealthReport finalReport = DimOperationUtil.checkMemoryHealth();
        System.out.println("  最终内存状态: " + finalReport.getStatus().getDescription());
        System.out.println("  内存使用率: " + String.format("%.1f", finalReport.getMemoryUsagePercent()) + "%");

        // 执行智能调优
        DimOperationUtil.performIntelligentCacheTuning();
        System.out.println("  ✓ 内存管理测试完成，已执行智能调优");
    }

    private static void testBatchConcurrency() {
        int batchSize = 20;
        List<List<DimOperationDetailDTO>> expressions = new ArrayList<>();
        List<Map<Long, BigDecimal>> variableMaps = new ArrayList<>();

        // 准备批量数据
        for (int i = 0; i < batchSize; i++) {
            expressions.add(createTestExpression());
            variableMaps.add(Map.of(
                    1L, new BigDecimal("10"),
                    2L, new BigDecimal("20"),
                    3L, new BigDecimal("5")
            ));
        }

        // 执行批量并发计算
        long startTime = System.currentTimeMillis();
        List<DimOperationUtil.ExecutionResult> results = DimOperationUtil.executeExpressionsConcurrently(
                expressions, variableMaps,
                DimOperationUtil.TraceConfig.disabled(),
                DimOperationUtil.OptimizationConfig.basic()
        );
        long endTime = System.currentTimeMillis();

        // 验证结果
        boolean allCorrect = true;
        BigDecimal expectedResult = new BigDecimal("150");
        for (DimOperationUtil.ExecutionResult result : results) {
            if (!expectedResult.equals(result.getResult())) {
                allCorrect = false;
                break;
            }
        }

        if (allCorrect) {
            System.out.println("  ✓ 批量并发测试通过");
            System.out.println("  批量大小: " + batchSize);
            System.out.println("  执行时间: " + (endTime - startTime) + "ms");
            System.out.println("  平均每个: " + String.format("%.2f", (double)(endTime - startTime) / batchSize) + "ms");
        } else {
            System.err.println("  ✗ 批量并发测试失败");
        }

        // 显示线程安全状态
        String safetyReport = DimOperationUtil.getThreadSafetyReport();
        System.out.println("  线程安全状态: 正常");
    }

    private static List<DimOperationDetailDTO> createTestExpression() {
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expression.add(createVariable("a", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("b", 2L));
        expression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("c", 3L));
        return expression;
    }

    private static DimOperationDetailDTO createVariable(String name, Long id) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.VARIABLE);
        dto.setDimVariableName(name);
        dto.setDimVariableId(id);
        return dto;
    }

    private static DimOperationDetailDTO createOperator(DimOperationFundamentalTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.FUNDAMENTAL);
        dto.setFundamentalType(type);
        return dto;
    }

    private static DimOperationDetailDTO createBracket(DimOperationBracketTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.BRACKET);
        dto.setBracketType(type);
        return dto;
    }

    private static DimOperationDetailDTO createConstant(String value) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.CONSTANT);
        dto.setConstantVal(value);
        return dto;
    }
}
