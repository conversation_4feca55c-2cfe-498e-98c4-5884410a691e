package com.dcube.rule.cube.utils;

import com.dcube.rule.cube.constants.enums.DimOperationBracketTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationDetailTypeEnum;
import com.dcube.rule.cube.constants.enums.DimOperationFundamentalTypeEnum;
import com.dcube.rule.cube.dto.DimOperationDetailDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 内存泄露修复测试类
 * 
 * 验证内存泄露修复的效果和自动维护功能
 */
public class MemoryLeakFixTest {

    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== 内存泄露修复测试开始 ===");
        System.out.println("初始状态: " + DimOperationUtil.getMemoryUsage());
        
        // 测试1：大量表达式执行
        testMassiveExecution();
        
        // 测试2：并发执行
        testConcurrentExecution();
        
        // 测试3：长时间运行
        testLongRunning();
        
        // 测试4：大日志测试
        testLargeLogGeneration();
        
        // 测试5：内存监控功能
        testMemoryMonitoring();
        
        // 测试6：自动维护功能
        testAutoMaintenance();
        
        System.out.println("\n=== 内存泄露修复测试完成 ===");
        System.out.println("最终状态: " + DimOperationUtil.getMemoryUsage());
        System.out.println("执行统计: " + DimOperationUtil.getExecutionStats());
        
        // 最终健康检查
        DimOperationUtil.MemoryHealthReport finalReport = DimOperationUtil.checkMemoryHealth();
        System.out.println("\n" + finalReport);
    }

    /**
     * 测试1：大量表达式执行
     */
    private static void testMassiveExecution() {
        System.out.println("\n1. 大量表达式执行测试（内存保护验证）");
        System.out.println("执行前: " + DimOperationUtil.getMemoryUsage());
        
        List<DimOperationDetailDTO> expression = createComplexExpression();
        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("100"),
                2L, new BigDecimal("20"),
                3L, new BigDecimal("3"),
                4L, new BigDecimal("2")
        );
        
        // 执行5000次表达式计算，验证内存不会无限增长
        for (int i = 0; i < 5000; i++) {
            DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithTrace(
                    expression, values, DimOperationUtil.TraceConfig.disabled());
            
            // 每1000次检查一次内存和结果
            if (i % 1000 == 0) {
                System.out.println("执行 " + i + " 次后: " + DimOperationUtil.getMemoryUsage());
                System.out.println("  结果摘要: " + result.getSummary());
                if (result.isLogTruncated()) {
                    System.out.println("  ✓ 日志截断功能正常工作");
                }
            }
        }
        
        System.out.println("执行后: " + DimOperationUtil.getMemoryUsage());
        System.out.println("缓存统计: " + DimOperationUtil.getCacheStats());
    }

    /**
     * 测试2：并发执行
     */
    private static void testConcurrentExecution() throws InterruptedException {
        System.out.println("\n2. 并发执行测试（线程安全验证）");
        System.out.println("并发前: " + DimOperationUtil.getMemoryUsage());
        
        ExecutorService executor = Executors.newFixedThreadPool(10);
        
        for (int i = 0; i < 50; i++) {
            final int threadId = i;
            executor.submit(() -> {
                List<DimOperationDetailDTO> expression = createRandomExpression(threadId);
                Map<Long, BigDecimal> values = Map.of(
                        1L, new BigDecimal(threadId * 10),
                        2L, new BigDecimal(threadId * 5),
                        3L, new BigDecimal(threadId * 2)
                );
                
                for (int j = 0; j < 50; j++) {
                    DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithTrace(
                            expression, values, DimOperationUtil.TraceConfig.disabled());
                    
                    // 验证结果完整性
                    if (result.getResult() == null) {
                        System.err.println("线程 " + threadId + " 执行 " + j + " 次时结果为空");
                    }
                }
            });
        }
        
        executor.shutdown();
        executor.awaitTermination(30, TimeUnit.SECONDS);
        
        System.out.println("并发后: " + DimOperationUtil.getMemoryUsage());
        System.out.println("执行统计: " + DimOperationUtil.getExecutionStats());
    }

    /**
     * 测试3：长时间运行
     */
    private static void testLongRunning() throws InterruptedException {
        System.out.println("\n3. 长时间运行测试（自动维护验证）");
        System.out.println("开始前: " + DimOperationUtil.getMemoryUsage());
        
        List<DimOperationDetailDTO> expression = createComplexExpression();
        Map<Long, BigDecimal> values = Map.of(
                1L, new BigDecimal("50"),
                2L, new BigDecimal("10"),
                3L, new BigDecimal("5"),
                4L, new BigDecimal("2")
        );
        
        // 模拟长时间运行，观察自动维护
        for (int i = 0; i < 20; i++) {
            DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithTrace(
                    expression, values, DimOperationUtil.TraceConfig.disabled());
            
            System.out.println("第 " + (i + 1) + " 次: " + DimOperationUtil.getMemoryUsage());
            System.out.println("  执行统计: " + DimOperationUtil.getExecutionStats());
            
            Thread.sleep(50); // 模拟时间间隔
        }
    }

    /**
     * 测试4：大日志生成测试
     */
    private static void testLargeLogGeneration() {
        System.out.println("\n4. 大日志生成测试（日志截断验证）");
        System.out.println("测试前: " + DimOperationUtil.getMemoryUsage());
        
        // 创建一个会生成大量日志的复杂表达式
        List<DimOperationDetailDTO> largeExpression = createLargeExpression();
        Map<Long, BigDecimal> values = createLargeValueMap();
        
        DimOperationUtil.ExecutionResult result = DimOperationUtil.executeExpressionWithTrace(
                largeExpression, values, 
                DimOperationUtil.TraceConfig.disabled());
        
        System.out.println("大表达式执行完成");
        System.out.println("结果摘要: " + result.getSummary());
        System.out.println("日志截断: " + (result.isLogTruncated() ? "是" : "否"));
        System.out.println("内存使用: " + String.format("%.1f KB", result.getMemoryUsedKB()));
        System.out.println("测试后: " + DimOperationUtil.getMemoryUsage());
    }

    /**
     * 测试5：内存监控功能
     */
    private static void testMemoryMonitoring() {
        System.out.println("\n5. 内存监控功能测试");
        
        // 获取详细缓存统计
        System.out.println(DimOperationUtil.getDetailedCacheStats());
        
        // 健康检查
        DimOperationUtil.MemoryHealthReport report = DimOperationUtil.checkMemoryHealth();
        System.out.println(report);
        
        // 根据建议执行操作
        if (!report.getRecommendations().isEmpty()) {
            System.out.println("根据建议执行维护...");
            DimOperationUtil.performMaintenance();
            
            // 再次检查
            DimOperationUtil.MemoryHealthReport afterReport = DimOperationUtil.checkMemoryHealth();
            System.out.println("维护后状态:");
            System.out.println(afterReport);
        }
    }

    /**
     * 测试6：自动维护功能
     */
    private static void testAutoMaintenance() {
        System.out.println("\n6. 自动维护功能测试");
        
        // 重置统计，模拟新的执行周期
        DimOperationUtil.resetExecutionStats();
        System.out.println("重置后统计: " + DimOperationUtil.getExecutionStats());
        
        // 执行大量操作触发自动维护
        List<DimOperationDetailDTO> expression = createSimpleExpression();
        Map<Long, BigDecimal> values = Map.of(1L, new BigDecimal("10"), 2L, new BigDecimal("5"));
        
        System.out.println("开始执行大量操作以触发自动维护...");
        for (int i = 0; i < 1100; i++) { // 超过维护阈值
            DimOperationUtil.executeExpressionWithTrace(expression, values, DimOperationUtil.TraceConfig.disabled());
            
            if (i % 200 == 0) {
                System.out.println("执行 " + i + " 次: " + DimOperationUtil.getExecutionStats());
            }
        }
        
        System.out.println("最终统计: " + DimOperationUtil.getExecutionStats());
    }

    // 辅助方法
    private static List<DimOperationDetailDTO> createSimpleExpression() {
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createVariable("a", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("b", 2L));
        return expression;
    }

    private static List<DimOperationDetailDTO> createComplexExpression() {
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        // (a + b) * c / d
        expression.add(createBracket(DimOperationBracketTypeEnum.LEFT));
        expression.add(createVariable("a", 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("b", 2L));
        expression.add(createBracket(DimOperationBracketTypeEnum.RIGHT));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("c", 3L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.DIVIDE));
        expression.add(createVariable("d", 4L));
        return expression;
    }

    private static List<DimOperationDetailDTO> createRandomExpression(int seed) {
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        expression.add(createVariable("x" + seed, 1L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.ADD));
        expression.add(createVariable("y" + seed, 2L));
        expression.add(createOperator(DimOperationFundamentalTypeEnum.MULTIPLY));
        expression.add(createVariable("z" + seed, 3L));
        return expression;
    }

    private static List<DimOperationDetailDTO> createLargeExpression() {
        List<DimOperationDetailDTO> expression = new ArrayList<>();
        
        // 创建一个包含30个变量的复杂表达式
        for (int i = 1; i <= 30; i++) {
            if (i > 1) {
                expression.add(createOperator(i % 2 == 0 ? 
                    DimOperationFundamentalTypeEnum.ADD : DimOperationFundamentalTypeEnum.MULTIPLY));
            }
            expression.add(createVariable("var" + i, (long) i));
        }
        
        return expression;
    }

    private static Map<Long, BigDecimal> createLargeValueMap() {
        Map<Long, BigDecimal> values = new java.util.HashMap<>();
        for (long i = 1; i <= 30; i++) {
            values.put(i, new BigDecimal(i));
        }
        return values;
    }

    private static DimOperationDetailDTO createVariable(String name, Long id) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.VARIABLE);
        dto.setDimVariableName(name);
        dto.setDimVariableId(id);
        return dto;
    }

    private static DimOperationDetailDTO createOperator(DimOperationFundamentalTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.FUNDAMENTAL);
        dto.setFundamentalType(type);
        return dto;
    }

    private static DimOperationDetailDTO createBracket(DimOperationBracketTypeEnum type) {
        DimOperationDetailDTO dto = new DimOperationDetailDTO();
        dto.setDimOperationDetailType(DimOperationDetailTypeEnum.BRACKET);
        dto.setBracketType(type);
        return dto;
    }
}
