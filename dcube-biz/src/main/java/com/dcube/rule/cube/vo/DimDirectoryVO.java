package com.dcube.rule.cube.vo;

import com.dcube.rule.cube.domain.DimRuleIndicatorOperation;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class DimDirectoryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 维度目录名称
     */
    @Schema(description = "维度目录名称")
    private String dimDirectoryName;

    /**
     * 维度目录类型（分组或维度实例）
     */
    @Schema(description = "维度目录类型（分组或维度实例）")
    private String dimDirectoryType;

    /**
     * 维度类型（维度实例属性::主数据维度或一般维度）
     */
    @Schema(description = "维度类型（维度实例属性::主数据维度或一般维度）")
    private String dimType;

    /**
     * 显示序号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "显示序号")
    private Integer indexNo;

    /**
     * 上级目录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "上级目录ID")
    private Long parentId;

    /**
     * 成员数量
     */
    @Schema(description = "成员数量")
    private Long itemSize;

    /**
     * 选中成员数量
     */
    @Schema(description = "选中成员数量")
    private Long checkedItemSize;

    /**
     * 指标运算
     */
    @Schema(description = "指标运算")
    private DimRuleIndicatorOperation indicatorOperation;

//    /**
//     * 维度
//     */
//    @Schema(description = "维度")
//    private List<DimInstanceVO> dimInstanceList;


}
