package com.dcube.rule.cube.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class DimInstanceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 指标运算规则id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "指标运算规则id")
    private Long indicatorOperationId;

    /**
     * 维度目录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "维度目录ID")
    private Long dimDirectoryId;

    /**
     * 维度成员名称
     */
    @Schema(description = "维度成员名称")
    private String dimName;

    /**
     * 维度成员代码
     */
    @Schema(description = "维度成员代码")
    private String dimCode;

    /**
     * 显示序号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "显示序号")
    private Integer indexNo;

    /**
     * 上级维度成员ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键ID")
    private Long parentId;

    /**
     * 节点路径
     */
    @Schema(description = "节点路径")
    private String ancestors;

    /**
     * 是否叶子
     */
    @Schema(description = "是否叶子")
    private String isLeaf;

    /**
     * 是否选择
     */
    @Schema(description = "是否选择")
    private boolean checked;

}
