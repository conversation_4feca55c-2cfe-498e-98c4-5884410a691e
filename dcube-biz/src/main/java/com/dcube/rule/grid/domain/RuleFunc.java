package com.dcube.rule.grid.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 规则函数对象 cube_rule_func
 */
@Data
@TableName("cube_rule_func")
public class RuleFunc extends Model<RuleFunc> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JSONField(serialize = false)
    @JsonIgnore
    private Long id;

    /**
     * 函数名称
     */
    private String funcName;

    /**
     * 函数值
     */
    private String funcVal;

    /**
     * 中文描述
     */
    private String funcDesc;

    private String alias;

    /**
     * 函数序号
     */
    @JSONField(serialize = false)
    @JsonIgnore
    private Integer funcSeq;

    /**
     * 函数分组
     */
    @JSONField(serialize = false)
    @JsonIgnore
    private Long funcGroup;

    /**
     * 分组名称
     */
    @JSONField(serialize = false)
    @JsonIgnore
    @TableField(exist = false)
    private String groupName;

    /**
     * 范围（1二维，2多维）
     */
    private String funcScope;

    /**
     * 分组序号
     */
    @TableField(exist = false)
    @JSONField(serialize = false)
    @JsonIgnore
    private Integer groupSeq;

    @TableField(exist = false)
    private List<RuleFunc> ruleFuncList = Collections.emptyList();

}
