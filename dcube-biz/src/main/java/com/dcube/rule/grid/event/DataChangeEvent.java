package com.dcube.rule.grid.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 数据修改事件
 */
@Getter
public class DataChangeEvent extends ApplicationEvent {

    private final String type;
    private final String taskId;
    private final Integer tableId;

    public DataChangeEvent(List<Object[]> source, String type, Integer tableId, String taskId) {
        super(source);
        this.type = type;
        this.tableId = tableId;
        this.taskId = taskId;
    }

    public List<Object[]> getRowData() {
        return (List<Object[]>) this.source;
    }

}
