package com.dcube.rule.grid.operator;

import cn.hutool.core.date.DateUtil;
import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.util.ExpressUtils;
import com.ql.util.express.Operator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.RoundingMode;
import java.util.Date;
import java.util.Map;

/**
 * 分摊（可以按权重或者均摊）
 */
@Slf4j
public class AllocaOperator extends Operator {

    /**
     * alloca(o("所属机构"),t("表1","机构"),t("表1","人力费用"),o("贷款金额"))
     *
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list == null) {
            throw new RuleExecutionException("alloca规则定义的参数不正确");
        }
        if (!(list[1] instanceof Map) || !(list[2] instanceof Map)) {
            throw new RuleExecutionException("alloca规则解析后类型不正确");
        }
        Number ret = null;
        String columnName = String.valueOf(list[0]);
        Long ruleId = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.RULE_ID);
        int length = list.length;
        if (length == 3) {
            Map<String, Object> map = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.ALLOCA_O_COUNT + ruleId);
            Object c = MapUtils.getObject(map, columnName);
            //均摊
            if (c == null) {
                ret = 0;
            } else {
                Number tVal = getVal(list, columnName);
                if (tVal == null) {
                    ret = 0;
                } else {
                    ret = BigDecimalUtils.convertDigDecimal(tVal).divide(BigDecimalUtils.convertDigDecimal(c), 10, RoundingMode.HALF_UP);
                }
            }
        } else if (length == 4) {
            Map<String, Object> map = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.ALLOCA_O_SUM + ruleId);
            Object s = MapUtils.getObject(map, columnName);
            //按权重
            if (s == null) {
                ret = 0;
            } else {
                Number tVal = getVal(list, columnName);
                if (tVal == null) {
                    ret = 0;
                } else {
                    ret = BigDecimalUtils.convertDigDecimal(tVal).multiply(BigDecimalUtils.convertDigDecimal(list[3]).divide(BigDecimalUtils.convertDigDecimal(s), 10, RoundingMode.HALF_UP));
                }
            }
        }
        return ret;
    }

    private static Number getVal(Object[] list, String columnName) {
        Number tVal;
        String uniqueKey = "";
        Map<String, Object> map1 = (Map<String, Object>) list[1];
        Map<String, Object> map2 = (Map<String, Object>) list[2];
        for (Map.Entry<String, Object> entry : map1.entrySet()) {
            String value = entry.getValue() instanceof Date ? DateUtil.formatDate((Date) entry.getValue()) : String.valueOf(entry.getValue());
            if (StringUtils.equals(value, columnName)) {
                uniqueKey = entry.getKey();
                break;
            }
        }
        tVal = MapUtils.getNumber(map2, uniqueKey);
        return tVal;
    }
}
