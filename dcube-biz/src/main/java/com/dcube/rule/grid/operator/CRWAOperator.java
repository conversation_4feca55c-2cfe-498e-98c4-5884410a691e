package com.dcube.rule.grid.operator;

import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.ql.util.express.Operator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 子表
 * cRWA(【本表计算列-贷款金额】,【本表计算列-风险权重】,【子表计算列-缓释工具金额】,【子表计算列-缓释工具权重】)
 */
@Slf4j
public class CRWAOperator extends Operator {


    /**
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list == null || list.length != 4) {
            throw new RuleExecutionException("cavgw规则定义的参数不正确");
        }
        if (list[2] instanceof Map && list[3] instanceof Map) {
            // 本表金额
            BigDecimal bigDecimal1 = BigDecimalUtils.convertDigDecimal(list[0]);
            // 本表权重
            BigDecimal bigDecimal2 = BigDecimalUtils.convertDigDecimal(list[1]);
            // 缓释工具金额
            Map map1 = (Map) list[2];
            // 缓释工具权重
            Map map2 = (Map) list[3];
            map1.replaceAll((k, v) -> BigDecimalUtils.convertDigDecimal(v));
            map2.replaceAll((k, v) -> BigDecimalUtils.convertDigDecimal(v));
            Map<String, BigDecimal> cBigDecimalMap1 = map1;
            Map<String, BigDecimal> cBigDecimalMap2 = map2;

            // 排序子表权重
            //先拿到map的键值对集合
            Set<Map.Entry<String, BigDecimal>> cBigDecimalMap2EntrySet = cBigDecimalMap2.entrySet();
            //将Set集合转化为List集合,为的是使用Collection中自带的工具类
            List<Map.Entry<String, BigDecimal>> cBigDecimalMap2List = new ArrayList<>(cBigDecimalMap2EntrySet);
            //使用Collection工具类对list进行排序
            cBigDecimalMap2List.sort(Map.Entry.comparingByValue());
            //创建一个LinkedHashMap,有序的插入数据
            Map<String, BigDecimal> cLinkedHashMap2 = new LinkedHashMap<>();
            for (Map.Entry<String, BigDecimal> entry : cBigDecimalMap2List) {
                cLinkedHashMap2.put(entry.getKey(), entry.getValue());
            }

            Map<String, BigDecimal> cLinkedHashMap1 = new LinkedHashMap<>();
            for (Map.Entry<String, BigDecimal> entry : cLinkedHashMap2.entrySet()) {
                cLinkedHashMap1.put(entry.getKey(), cBigDecimalMap1.get(entry.getKey()));
            }

            BigDecimal bigDecimalSum = new BigDecimal(0);
            BigDecimal ret = new BigDecimal(0);
            for (Map.Entry<String, BigDecimal> entry : cLinkedHashMap1.entrySet()) {
                BigDecimal num2 = MapUtils.getObject(cLinkedHashMap2, entry.getKey());
                // 权重大于本身的权重，跳过
                if (num2.compareTo(bigDecimal2) > 0) {
                    continue;
                }
                BigDecimal num1 = entry.getValue();
                BigDecimal bigDecimalSumTemp = bigDecimalSum.add(num1);
                // 判断子表金额是否超过本身
                if (bigDecimalSumTemp.compareTo(bigDecimal1) > 0) {
                    BigDecimal subtract = bigDecimal1.subtract(bigDecimalSum);
                    ret = ret.add(subtract.multiply(num2));
                    bigDecimalSum = bigDecimal1;
                    break;
                } else {
                    ret = ret.add(num1.multiply(num2));
                    bigDecimalSum = bigDecimalSumTemp;
                }
            }
            // 判断子表金额是否释放完
            if (bigDecimalSum.compareTo(bigDecimal1) < 0) {
                BigDecimal subtract = bigDecimal1.subtract(bigDecimalSum);
                //权重就是原来资产的权重
                ret = ret.add(subtract.multiply(bigDecimal2));
            }
            return ret;
        }
        return 0;
    }
}
