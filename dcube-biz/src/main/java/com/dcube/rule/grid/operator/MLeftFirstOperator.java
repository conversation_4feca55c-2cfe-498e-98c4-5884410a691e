package com.dcube.rule.grid.operator;

import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.ql.util.express.Operator;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 多列靠左优先
 */
@Slf4j
public class MLeftFirstOperator extends Operator {

    /**
     * mleftFirst(【参数1】,【参数2】,【参数3】...);
     * 说明：多个参数从左至右排先后优先级，如果有多个非空值，返回优先级最高的非空值
     *
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list == null || list.length == 0) {
            throw new RuleExecutionException("mavg规则定义的参数不能为空");
        }
        BigDecimal result = null;
        for (Object o : list) {
            if (o == null) {
                continue;
            }
            if (o instanceof Number) {
                result = BigDecimalUtils.convertDigDecimal(o);
                break;
            } else {
                // 不是数字返回0
                return BigDecimal.ZERO;
            }
        }

        return result == null ? BigDecimal.ZERO : result;
    }

}
