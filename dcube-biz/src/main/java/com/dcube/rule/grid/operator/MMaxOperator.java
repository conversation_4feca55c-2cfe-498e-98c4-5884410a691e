package com.dcube.rule.grid.operator;

import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.ql.util.express.Operator;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 多列最大值
 */
@Slf4j
public class MMaxOperator extends Operator {

    /**
     * mmax(【参数1】,【参数2】,【参数3】...);
     * 说明：返回多个参数中的最大值，空值不参与计算，如果都是空那就返回0
     *
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list == null || list.length == 0) {
            throw new RuleExecutionException("mmax规则定义的参数不能为空");
        }
        BigDecimal result = null;
        if (list[0] != null) {
            if (list[0] instanceof Number) {
                result = BigDecimalUtils.convertDigDecimal(list[0]);
            } else {
                // 不是数字返回0
                return BigDecimal.ZERO;
            }
        }
        for (int i = 1, len = list.length; i < len; i++) {
            if (list[i] == null) {
                continue;
            }
            if (list[i] instanceof Number) {
                BigDecimal num = BigDecimalUtils.convertDigDecimal(list[i]);
                if (result == null || num.compareTo(result) > 0) {
                    result = num;
                }
            } else {
                // 不是数字返回0
                return BigDecimal.ZERO;
            }
        }

        return result == null ? BigDecimal.ZERO : result;
    }

}
