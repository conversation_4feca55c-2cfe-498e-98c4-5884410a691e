package com.dcube.rule.grid.operator;

import com.dcube.rule.grid.exception.RuleExecutionException;
import com.ql.util.express.Operator;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 多列日期最大值
 */
@Slf4j
public class MmaxDateOperator extends Operator {

    /**
     * mmaxDate(【日期1】,【日期2】,【日期3】...);
     * 说明：返回多个日期中的最大值
     *
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list == null || list.length == 0) {
            throw new RuleExecutionException("mmaxDate规则定义的参数不能为空");
        }
        for (Object o : list) {
            if (o == null) {
                continue;
            }
            if (!(o instanceof Date)) {
                throw new RuleExecutionException("mmaxDate规则定义的参数必须是日期类型");
            }
        }
        Object result = list[0];
        for (int i = 1, len = list.length; i < len; i++) {
            if (result == null) {
                result = list[i];
            } else {
                if (list[i] != null) {
                    result = ((Date) result).compareTo((Date) list[i]) > 0 ? result : list[i];
                }
            }
        }
        return result;
    }

}
