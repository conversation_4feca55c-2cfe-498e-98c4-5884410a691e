package com.dcube.rule.grid.operator;

import cn.hutool.core.date.DateUtil;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.util.ExpressUtils;
import com.ql.util.express.Operator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Date;
import java.util.Map;

/**
 * 计数
 */
@Slf4j
public class VCountOperator extends Operator {

    /**
     * vcount(【它表计数列】, 【它表条件列1】, 【本表条件列1】, 【它表条件列2】, 【本表条件列2】…)
     *
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list == null) {
            throw new RuleExecutionException("vcount规则定义的参数不能为空");
        }

        int length = list.length;
        if (length % 2 != 1) {
            throw new RuleExecutionException("vcount规则定义的参数格式不正确");
        }
        Object ret;
        Long ruleId = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.RULE_ID);
        if (length == 1) {
            ret = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COUNT_T + ruleId);
        } else {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 2; i < length; i += 2) {
                if (list[i] instanceof Date) {
                    stringBuilder.append(DateUtil.formatDate((Date) list[i]));
                } else {
                    stringBuilder.append(list[i]);
                }
                if (i != length - 1) {
                    stringBuilder.append("_");
                }
            }
            Map<String, Object> map = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.COUNT_T + ruleId);
            ret = MapUtils.getObject(map, stringBuilder.toString());
        }
        if (ret == null) {
            return 0;
        }
        return ret;
    }


}
