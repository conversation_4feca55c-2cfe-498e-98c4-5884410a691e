package com.dcube.rule.grid.operator;

import cn.hutool.core.date.DateUtil;
import com.dcube.biz.util.BigDecimalUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.util.ExpressUtils;
import com.ql.util.express.Operator;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 跨表聚合汇总
 */
@Slf4j
public class VSumOperator extends Operator {

    /**
     * vsum(t("表1","贷款金额"),t("表1","所属机构"),o("机构"))
     * vsum(t("表1","贷款金额"),t("表1","所属机构"),o("机构"),t("表1","产品"),o("产品"))
     *
     * @param list
     * @return
     * @throws Exception
     */
    @Override
    public Object executeInner(Object[] list) throws Exception {
        if (list == null) {
            throw new RuleExecutionException("vsum规则定义的参数不正确");
        }
        Object ret = null;
        Long ruleId = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.RULE_ID);
        boolean useCache = true;
        Boolean cubeRule = ThreadLocalUtils.get(ExpressUtils.class, RuleConstant.CUBE_RULE);
        Map<String, Object> vsumCache = ThreadLocalUtils.getTtl(ExpressUtils.class, RuleConstant.VSUM_T_SUM + ruleId);
        if (Boolean.TRUE.equals(cubeRule)) {
            useCache = vsumCache != null;
        }
        if (useCache) {
            int length = list.length;
            if (length > 1) {
                StringBuilder columnNames = new StringBuilder();
                for (int i = 2; i < length; i += 2) {
                    if (list[i] instanceof Date) {
                        columnNames.append(DateUtil.formatDate((Date) list[i]));
                    } else {
                        columnNames.append(list[i]);
                    }
                    if (i != length - 1) {
                        columnNames.append("_");
                    }
                }
                String columnNamesString = columnNames.toString();
                ret = MapUtils.getObject(vsumCache, columnNamesString);
            } else {
                ret = vsumCache;
            }
        } else {
            // 第一个是map
            if (!(list[0] instanceof Map)) {
                throw new RuleExecutionException("vsum规则解析后类型不正确：第1个参数应为 Map");
            }
            // 所有奇数项必须是 Map
            for (int i = 1; i < list.length; i += 2) {
                if (!(list[i] instanceof Map)) {
                    throw new RuleExecutionException("vsum规则解析后类型不正确：第" + (i + 1) + "个参数应为 Map");
                }
            }

            List<Map<String, Object>> listMap = new ArrayList<>();
            listMap.add((Map<String, Object>) list[0]);
            int length = list.length;
            for (int i = 1; i < length; i += 2) {
                listMap.add((Map<String, Object>) list[i]);
            }
            // 构造 GroupKey 所需的维度值（偶数项）
            String[] groupFields = new String[(list.length - 1) / 2];
            for (int i = 2, j = 0; i < list.length; i += 2, j++) {
                Object val = list[i];
                if (val instanceof Date) {
                    groupFields[j] = DateUtil.formatDate((Date) val); // 你项目中已有的工具类
                } else {
                    groupFields[j] = val.toString();
                }
            }
            Map<GroupKey, BigDecimal> result = aggregate(listMap);
            return MapUtils.getNumber(result, new GroupKey(groupFields), BigDecimal.ZERO);
        }
        return ret;
    }

    @EqualsAndHashCode
    @ToString
    public static class GroupKey implements Serializable {
        private static final long serialVersionUID = 1L;

        private final String[] fields;
        private final int hashCode;

        public GroupKey(String[] fields) {
            this.fields = fields;
            this.hashCode = Arrays.hashCode(fields); // 缓存 hashCode 提高性能
        }

    }

    public static Map<GroupKey, BigDecimal> aggregate(List<Map<String, Object>> maps) {

        int valueMapIndex = maps.size();
        Map<String, Object> valueMap = maps.get(0);
        Set<String> keys = valueMap.keySet();

        Map<GroupKey, BigDecimal> result = new HashMap<>();

        for (String key : keys) {
            String[] fields = new String[valueMapIndex - 1];
            for (int i = 1; i < valueMapIndex; i++) {
                Object val = maps.get(i).get(key);
                fields[i - 1] = val == null ? "null" : val.toString();
            }

            GroupKey groupKey = new GroupKey(fields);
            BigDecimal value = BigDecimalUtils.convertDigDecimal(valueMap.get(key));

            result.merge(groupKey, value, BigDecimal::add);
        }

        return result;
    }

}
