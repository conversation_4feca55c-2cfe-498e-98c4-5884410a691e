package com.dcube.rule.grid.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.rule.grid.RuleGraph;
import com.dcube.rule.grid.domain.Rule;

import java.util.List;

public interface IRuleServiceHelper extends IService<Rule> {

    /**
     * 保存计算规则
     *
     * @param tableId    二维表ID
     * @param columnCode 列编码
     * @param express    计算规则
     * @return
     */
    int save(Integer tableId, String columnCode, String express);

    /**
     * 保存计算规则
     *
     * @param tableId     二维表ID
     * @param columnCode  列编码
     * @param express     计算规则
     * @param needExecute 是否需要执行规则
     * @return
     */
    int save(Integer tableId, String columnCode, String express, boolean needExecute);


    void doSaveRule(String taskId, Integer tableId, String columnCode, RuleGraph ruleGraph);

    /**
     * 执行规则计算
     *
     * @param tableId
     * @param taskId
     */
    void execute(Integer tableId, String taskId);

    /**
     * 执行规则计算
     *
     * @param tableIds
     * @param taskId
     */
    void execute(List<Integer> tableIds, String taskId);

    /**
     * 同步执行规则计算
     *
     * @param tableIds
     * @param taskId
     */
    void executeSync(List<Integer> tableIds, String taskId);

}
