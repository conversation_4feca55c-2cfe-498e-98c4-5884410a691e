package com.dcube.tran.backup;

import com.dcube.biz.domain.TableBackupConfig;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.util.JdbcUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class BackupRepositoryFactory {

    public static AbstractBackupRepository getBackupRepository(SourceConfigJson config, String tableName, Map<String, TableBackupConfig> backupConfigMap, int tableLevel, String tableComment) {
        AbstractBackupRepository repository;
        switch (config.getDatabaseProductName()) {
            case "DM DBMS":
                repository = new DmBackupRepository(JdbcUtils.DB_TYPE_DM, config, tableName, backupConfigMap, tableLevel, tableComment);
                break;
            case "MySQL":
                repository = new MysqlBackupRepository(JdbcUtils.DB_TYPE_MYSQL, config, tableName, backupConfigMap, tableLevel, tableComment);
                break;
            case "ClickHouse":
                repository = new ClickHouseBackupRepository(JdbcUtils.DB_TYPE_CLICKHOUSE, config, tableName, backupConfigMap, tableLevel, tableComment);
                break;
            case "PostgreSQL":
                repository = new PostgresBackupRepository(JdbcUtils.DB_TYPE_POSTGRESQL, config, tableName, backupConfigMap, tableLevel, tableComment);
                break;
            default:
                throw new RuntimeException(String.format("不支持的数据源类型[%s]", config.getDatabaseProductName()));
        }
        return repository;
    }
}
