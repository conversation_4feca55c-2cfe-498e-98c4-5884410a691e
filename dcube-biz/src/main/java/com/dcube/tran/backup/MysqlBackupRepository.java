package com.dcube.tran.backup;

import com.dcube.biz.constant.enums.TableBackupColumnTypeEnum;
import com.dcube.biz.domain.TableBackupConfig;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class MysqlBackupRepository extends AbstractBackupRepository {

    private static final String COLUMN_SQL_FORMAT = "%s %s %s comment '%s'";
    private static final String ADD_COLUMN_SQL_FORMAT = "alter table %s add column " + COLUMN_SQL_FORMAT;
    private static final String COMMENT_TABLE_SQL_FORMAT = "alter table %s comment %s";
    private static final String MODIFY_COLUMN_SQL_FORMAT = "alter table %s modify column " + COLUMN_SQL_FORMAT;

    MysqlBackupRepository(String dbType, SourceConfigJson config, String tableName, Map<String, TableBackupConfig> backupConfigMap, int tableLevel, String tableComment) {
        super(dbType, config, tableName, backupConfigMap, tableLevel, tableComment);
    }

    @Override
    public String getIntColumn() {
        return "integer(%s)";
    }

    @Override
    public String getBigintColumn() {
        return "bigint";
    }

    @Override
    public String getStringColumn() {
        return "varchar(%s)";
    }

    @Override
    public String getNumericColumn() {
        return "numeric(%s,%s)";
    }

    @Override
    public String getDatetimeColumn() {
        return "datetime";
    }

    @Override
    public void createTable() {
        List<String> columnList = buildCreateTableColumns();
        String sql = String.format("create table `%s` (%s) COMMENT ='%s'", getTableName(), String.join(",", columnList), getTableComment());
        super.getAbstractRepository().executeSql(sql);
    }

    private List<String> buildCreateTableColumns() {
        Map<String, TableBackupConfig> backupConfigMap = getBackupConfigMap();
        List<String> columns = new ArrayList<>(backupConfigMap.size());
        int tableLevel = getTableLevel();
        List<String> primaryKeyList = new ArrayList<>(tableLevel);
        int _isPrimaryKey = 0;
        for (Map.Entry<String, TableBackupConfig> entry : backupConfigMap.entrySet()) {
            boolean isPrimaryKey = _isPrimaryKey <= tableLevel;
            TableBackupConfig tableBackupConfig = entry.getValue();
            String columnName = tableBackupConfig.getBackupColumnName().toLowerCase();
            TableBackupColumnTypeEnum columnType = tableBackupConfig.getColumnType();
            if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
                tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
            }
            switch (columnType) {
                case INTEGER:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getIntColumn(), tableBackupConfig.getColumnLength()), isPrimaryKey ? NOT_NULL : NULL, tableBackupConfig.getRemark()));
                    break;
                case BIGINT:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getBigintColumn()), isPrimaryKey ? NOT_NULL : NULL, tableBackupConfig.getRemark()));
                    break;
                case VARCHAR:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getStringColumn(), tableBackupConfig.getColumnLength()), isPrimaryKey ? NOT_NULL : NULL, tableBackupConfig.getRemark()));
                    break;
                case NUMERIC:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getNumericColumn(), tableBackupConfig.getColumnLength(), NUMERIC_DEFAULT_SCALE), isPrimaryKey ? NOT_NULL : NULL, tableBackupConfig.getRemark()));
                    break;
                case DATETIME:
                    columns.add(String.format(COLUMN_SQL_FORMAT, columnName, String.format(getDatetimeColumn()), isPrimaryKey ? NOT_NULL : NULL, tableBackupConfig.getRemark()));
                    break;
                default:
                    log.error("Unsupported type: " + columnType);
                    break;
            }
            if (isPrimaryKey) {
                primaryKeyList.add(entry.getKey());
                _isPrimaryKey++;
            }
        }
        // 添加备份版本名称字段
        columns.add(String.format(COLUMN_SQL_FORMAT, "backup_version_name", String.format(getStringColumn(), 128), NOT_NULL, "备份版本名称"));
        // 添加数据日期字段
        columns.add(String.format(COLUMN_SQL_FORMAT, "base_data_dt", String.format(getDatetimeColumn()), NOT_NULL, "数据日期"));
        // 指定联合主键
        columns.add(String.format("PRIMARY KEY (backup_version_name,base_data_dt,%s) ", String.join(",", primaryKeyList)));
        return columns;
    }

    @Override
    public String genAddColumnSql(TableBackupConfig tableBackupConfig) {
        return genColumnSql(tableBackupConfig, ADD_COLUMN_SQL_FORMAT);
    }

    public String genModifyColumnSql(TableBackupConfig tableBackupConfig) {
        return genColumnSql(tableBackupConfig, MODIFY_COLUMN_SQL_FORMAT);
    }

    private String genColumnSql(TableBackupConfig tableBackupConfig, String format) {
        String sql = "";
        TableBackupColumnTypeEnum columnType = tableBackupConfig.getColumnType();
        String columnName = tableBackupConfig.getBackupColumnName().toLowerCase();
        if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
            tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
        }
        switch (columnType) {
            case INTEGER:
                sql = String.format(format, getTableName(), columnName, String.format(getIntColumn(), tableBackupConfig.getColumnLength()), NULL, tableBackupConfig.getRemark());
                break;
            case BIGINT:
                sql = String.format(format, getTableName(), columnName, String.format(getBigintColumn()), NULL, tableBackupConfig.getRemark());
                break;
            case VARCHAR:
                sql = String.format(format, getTableName(), columnName, String.format(getStringColumn(), tableBackupConfig.getColumnLength()), NULL, tableBackupConfig.getRemark());
                break;
            case NUMERIC:
                sql = String.format(format, getTableName(), columnName, String.format(getNumericColumn(), tableBackupConfig.getColumnLength(), NUMERIC_DEFAULT_SCALE), NULL, tableBackupConfig.getRemark());
                break;
            case DATETIME:
                sql = String.format(format, getTableName(), columnName, String.format(getDatetimeColumn()), NULL, tableBackupConfig.getRemark());
                break;
            default:
                log.error("Unsupported type: " + columnType);
                break;
        }
        return sql;
    }

    @Override
    public boolean checkColumnTypeName(List<String> sqlList, String columnType, TableBackupConfig tableBackupConfig, int columnSize, String remarks) {
        boolean flag = false;
        if (StringUtils.isEmpty(tableBackupConfig.getRemark())) {
            tableBackupConfig.setRemark(tableBackupConfig.getColumnName());
        }
        switch (tableBackupConfig.getColumnType()) {
            case INTEGER:
                flag = columnType.equalsIgnoreCase("INT") || columnType.equalsIgnoreCase("INTEGER");
                break;
            case BIGINT:
                flag = columnType.equalsIgnoreCase("BIGINT");
                break;
            case VARCHAR:
                flag = columnType.equalsIgnoreCase("VARCHAR");
                if (flag) {
                    // 判断字段长度是否一致，不一致需要使用alter
                    flag = checkColumnLength(sqlList, columnType, tableBackupConfig, columnSize);
                }
                break;
            case NUMERIC:
                flag = columnType.equalsIgnoreCase("NUMERIC") || columnType.equalsIgnoreCase("DECIMAL");
                if (flag) {
                    flag = checkColumnLength(sqlList, columnType, tableBackupConfig, columnSize);
                }
                break;
            case DATETIME:
                flag = columnType.equalsIgnoreCase("DATETIME") || columnType.equalsIgnoreCase("TIMESTAMP");
                break;
        }
        if (flag) {
            flag = StringUtils.equals(remarks, tableBackupConfig.getRemark());
        }
        if (!flag) {
            if (sqlList != null) {
                sqlList.add(genModifyColumnSql(tableBackupConfig));
            }
        }
        return flag;
    }

    @Override
    public String genTableCommentSql() {
        return String.format("alter table %s comment '%s'", getTableName().toLowerCase(), getTableComment());
    }

    private boolean checkColumnLength(List<String> sqlList, String columnType, TableBackupConfig tableBackupConfig, int columnSize) {
        boolean flag = true;
        // 判断字段长度是否一致，不一致需要使用alter
        if (tableBackupConfig.getColumnLength() != null && columnSize != tableBackupConfig.getColumnLength()) {
            flag = false;
            if (sqlList != null) {
                sqlList.add(genModifyColumnSql(tableBackupConfig));
            }
        }
        return flag;
    }

}
