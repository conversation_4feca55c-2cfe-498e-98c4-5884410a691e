package com.dcube.tran.execution;

import com.alibaba.fastjson2.JSON;
import com.dcube.biz.json.GroupDistinctJson;
import com.dcube.biz.util.MemGridUtils;
import com.dcube.biz.vo.TableVo;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.common.utils.uuid.IdUtils;
import com.dcube.grid.TableMetaData;
import com.dcube.rule.grid.constants.RuleConstant;
import com.dcube.rule.grid.constants.enums.RuleOperatorEnum;
import com.dcube.rule.grid.exception.RuleExecutionException;
import com.dcube.rule.grid.service.IRuleService;
import com.dcube.rule.grid.util.ExpressUtils;
import com.dcube.tran.element.Column;
import com.dcube.tran.element.DefaultRecord;
import com.dcube.tran.element.LongColumn;
import com.dcube.tran.element.Record;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ConditionTranExecution extends AbstractTranExecution {

    public ConditionTranExecution(Map<String, Object> tranParasMap) {
        super(tranParasMap);
        assert null != tranParasMap;
        IRuleService ruleService = SpringUtils.getBean(IRuleService.class);
        this.mainKey = (String) tranParasMap.get("mainKey");
        this.conditionRule = ruleService.handleRuleExpress((String) tranParasMap.get("condition_rule"));
        List<GroupDistinctJson> ojsonList = JSON.parseArray((String) tranParasMap.get("mapping"), GroupDistinctJson.class);
        this.jsonList = ojsonList.stream().filter(x -> StringUtils.isNotEmpty(x.getSColumnCode()) && StringUtils.isNotEmpty(x.getOColumnCode())).collect(Collectors.toList());
        this.isKeyMapping = this.jsonList.stream().anyMatch(x -> x.getOColumnCode().equalsIgnoreCase(this.mainKey));
        if (StringUtils.isNotEmpty(this.conditionRule)) {
            this.tableVo = (TableVo) tranParasMap.get("tableVo");
            this.ruleId = IdUtils.getSnowflakeNextIdStr();
            this.expressUtils = SpringUtils.getBean(ExpressUtils.class);
            this.indexColumnMap = (Map<Integer, String>) tranParasMap.get("indexColumnMap");
            this.indexExpressMap = (Map<Integer, String>) tranParasMap.get("indexExpressMap");
            this.indexRuleIdMap = (Map<Integer, Long>) tranParasMap.get("indexRuleIdMap");
        }
    }

    private final String mainKey;
    private final String conditionRule;
    private final List<GroupDistinctJson> jsonList;
    private TableVo tableVo;
    private String ruleId;
    private ExpressUtils expressUtils;
    private Map<Integer, String> indexColumnMap;
    private Map<Integer, String> indexExpressMap;
    private Map<Integer, Long> indexRuleIdMap;

    private final Boolean isKeyMapping;

    @Setter
    @Getter
    private int index;

    @Override
    public List<Record> doTran(Record record) {
        // 执行规则
        List<Column> columnList = record.getColumnList();
        if (StringUtils.isNotEmpty(conditionRule)) {
            int rowSize = columnList.size();
            Object[] row = new Object[rowSize];
            int i = 0;
            TableMetaData tableMetaData = MemGridUtils.getTableMetaData(tableVo.getMemTableName());
            for (String columnName : tableMetaData.getColumnNames()) {
                boolean match = false;
                for (Column column : columnList) {
                    if (columnName.equals(column.getColumnName())) {
                        row[i] = getValue(tableMetaData.getColumnTypes().get(i), column);
                        match = true;
                        break;
                    }
                }
                if (!match) {
                    row[i] = null;
                }
                i++;
            }
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.CURRENT_TABLE_ID, tableVo.getId());
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_MAP, indexColumnMap);
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_EXPRESS_MAP, indexExpressMap);
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.COLUMN_INDEX_RULE_ID_MAP, indexRuleIdMap);
            Map<String, Integer> reverseIndexColumnMap = indexColumnMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2));
            ThreadLocalUtils.set(ExpressUtils.class, RuleConstant.REVERSE_COLUMN_INDEX_MAP, reverseIndexColumnMap);
            Map<String, Object> context = Maps.newHashMapWithExpectedSize(4);
            context.put(RuleOperatorEnum.O.name(), row);// 当前表字段及值
            context.put(RuleConstant.RULE_ID, ruleId);// 当前规则id
            context.put(RuleConstant.RULE_EXPRESS, conditionRule);// 规则
            Object executed = expressUtils.execute(conditionRule, context);
            if (!(executed instanceof Boolean)) {
                throw new RuleExecutionException("规则的计算结果不是布尔类型，请检查规则配置");
            }
            if (!Boolean.TRUE.equals(executed)) {
                return DefaultRecord.EMPTY_RECORD_LIST;
            }
        }

        List<Record> records = new ArrayList<>(1);
        DefaultRecord newRecord = new DefaultRecord();
        if (!isKeyMapping) {
            Column mainKeyColumn = new LongColumn();
            mainKeyColumn.setColumnName(mainKey);
            mainKeyColumn.setColumn(new BigInteger(String.valueOf(index++)));
            newRecord.addColumn(mainKeyColumn);
        }
        // 注意顺序
        for (Column column : columnList) {
            for (GroupDistinctJson json : jsonList) {
                if (column.getColumnName().equalsIgnoreCase(json.getSColumnCode())) {
                    column.setColumnName(json.getOColumnCode());
                    newRecord.addColumn(column);
                    break;
                }
            }
        }
        records.add(newRecord);
        return records;
    }

    protected Object getValue(Class clazz, Column columnValue) {
        boolean isNull = columnValue.getRawData()== null;
        if (Integer.class.equals(clazz)) {
            return isNull ? null : columnValue.asLong().intValue();
        } else if (Double.class.equals(clazz)) {
            return isNull ? null : columnValue.asDouble();
        } else if (String.class.equals(clazz)) {
            return isNull ? null : columnValue.asString();
        } else if (Date.class.equals(clazz)) {
            return isNull ? null : new java.sql.Timestamp(columnValue.asDate().getTime());
        }
        throw new IllegalArgumentException("Unsupported type: " + clazz.getName());
    }
}
