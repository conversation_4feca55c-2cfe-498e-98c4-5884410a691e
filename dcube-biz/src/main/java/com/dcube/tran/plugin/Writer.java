package com.dcube.tran.plugin;

import com.dcube.tran.base.BaseObject;
import com.dcube.tran.exchanger.RecordReceiver;
import com.dcube.tran.task.TaskHelp;

public abstract class Writer extends BaseObject {

    private volatile TaskHelp taskHelp;

    public abstract void startWrite(RecordReceiver lineReceiver);

    public boolean supportFailOver() {
        return false;
    }

    public TaskHelp getTaskHelp() {
        return taskHelp;
    }

    public void setTaskHelp(TaskHelp taskHelp) {
        this.taskHelp = taskHelp;
    }
}
