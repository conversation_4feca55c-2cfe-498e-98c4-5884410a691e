package com.dcube.tran.store.repository;

import com.dcube.biz.base.PageDto;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.biz.util.JdbcUtils;
import com.dcube.common.config.properties.GlobalExceptionProperties;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.ExceptionUtil;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.grid.TableMetaData;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;

/**
 * @创建人 zhouhx
 * @创建时间 2023/3/4 20:10
 * @描述 内存数据库
 */
@Slf4j
public class MemoryRepository extends AbstractRepository {

    MemoryRepository(String dbType, SourceConfigJson config, String tableName) {
        super(dbType, config, tableName);
    }

    @Override
    public void syncTable(String databaseName, TableMetaData tableMetaData) {

    }

    @Override
    public List<Map<String, Object>> listPage(String sql, PageDto pageDto) {
        return this.executeQuerySql(String.format("%s limit %s offset %s", sql, pageDto.getPageSize(), pageDto.getSkip()));
    }

    @Override
    public List<Map<String, Object>> avgAndSum(String sql) {
        return this.executeQuerySql(sql);
    }

    @Override
    public List<Map<String, Object>> executeQuerySql(String sql) {
        Connection conn = JdbcUtils.getGridConnection();
        PreparedStatement statement = null;
        ResultSet rs = null;
        List<Map<String, Object>> recordList;
        try {
            log.debug("执行executeQuerySql，sql = {}", sql);
            statement = conn.prepareStatement(sql);
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            rs = statement.executeQuery();
            // 计算执行耗时
            long costTime = System.currentTimeMillis() - startTime;
            log.info("executeQuery执行耗时 {} ms，SQL：{}", costTime, sql);
            // 判断是否为慢SQL并记录日志
            if (costTime >= 5000) {
                log.warn("⚠️慢SQL检测：执行耗时 {} ms，SQL：{}", costTime, sql);
            }
            recordList = super.convertList(rs);
        } catch (Exception ex) {
            log.error("执行executeQuerySql失败::{}", sql, ex);
            throw new ServiceException(ExceptionUtil.getLocalizedMessage(ex));
        } finally {
            JdbcUtils.close(conn, statement, rs);
        }
        return recordList;
    }

    @Override
    public long executeCountSql(String sql) {
        Connection conn = JdbcUtils.getGridConnection();
        PreparedStatement statement = null;
        ResultSet rs = null;
        long count = 0;
        try {
            log.debug("执行executeCountSql，sql = {}", sql);
            statement = conn.prepareStatement(sql);
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            rs = statement.executeQuery();
            // 计算执行耗时
            long costTime = System.currentTimeMillis() - startTime;
            // 判断是否为慢SQL并记录日志
            if (costTime > 30000) {
                log.warn("⚠️慢SQL检测：执行耗时 {} ms，SQL：{}", costTime, sql);
            }
            if (rs.next()) {
                count = rs.getLong(1);
            }
        } catch (Exception ex) {
            log.error("执行executeCountSql失败，sql = {}", sql, ex);
            throw new ServiceException(ExceptionUtil.getLocalizedMessage(ex));

        } finally {
            JdbcUtils.close(conn, statement, rs);
        }
        return count;
    }

    public int getMaxId(String sql) {
        Connection conn = JdbcUtils.getGridConnection();
        PreparedStatement statement = null;
        ResultSet rs = null;
        int maxId = 0;
        try {
            log.debug("执行getMaxId，sql = {}", sql);
            statement = conn.prepareStatement(sql);
            rs = statement.executeQuery();
            if (rs.next()) {
                maxId = rs.getObject(1) != null ? Integer.valueOf(rs.getObject(1).toString()) : 0;
            }
        } catch (Exception ex) {
            log.error("执行getMaxId，sql = {}", sql, ex);
            throw new ServiceException(ExceptionUtil.getLocalizedMessage(ex));

        } finally {
            JdbcUtils.close(conn, statement, rs);
        }
        return maxId;
    }

    public Map<String, Object> executeQuerySql(String sql, List<String> keyColumns, String valueColumn) {
        Connection conn = null;
        PreparedStatement statement = null;
        ResultSet rs = null;
        Map<String, Object> recordMap;
        try {
            conn = JdbcUtils.getGridConnection();
            log.debug("执行executeQuerySql，sql = {}", sql);
            statement = conn.prepareStatement(sql);
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            rs = statement.executeQuery();
            // 计算执行耗时
            long costTime = System.currentTimeMillis() - startTime;
            log.info("executeQuery执行耗时 {} ms，SQL：{}", costTime, sql);
            // 判断是否为慢SQL并记录日志
            if (costTime >= 5000) {
                log.warn("⚠️慢SQL检测：执行耗时 {} ms，SQL：{}", costTime, sql);
            }
            recordMap = super.convertMap(rs, keyColumns, valueColumn);
        } catch (Exception ex) {
            log.error("sql执行失败::{}", sql, ex);
            boolean showDetail = SpringUtils.getBean(GlobalExceptionProperties.class).isShowDetail();
            if (showDetail) {
                throw new ServiceException(ex);
            } else {
                throw new ServiceException("数据库操作失败，请联系系统管理员！");
            }
        } finally {
            JdbcUtils.close(conn, statement, rs);
        }
        return recordMap;
    }

}
