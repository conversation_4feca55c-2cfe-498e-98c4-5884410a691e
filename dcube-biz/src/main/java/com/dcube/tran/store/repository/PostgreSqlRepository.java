package com.dcube.tran.store.repository;

import com.dcube.biz.base.PageDto;
import com.dcube.biz.json.SourceConfigJson;
import com.dcube.common.config.properties.GlobalExceptionProperties;
import com.dcube.common.exception.ServiceException;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.grid.TableMetaData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public class PostgreSqlRepository extends AbstractRepository {

    PostgreSqlRepository(String dbType, SourceConfigJson config, String tableName) {
        super(dbType, config, tableName);
    }

    @Override
    public List<Map<String, Object>> listPage(String sql, PageDto pageDto) {
        // PostgreSQL分页语法: LIMIT pageSize OFFSET offset
        return this.executeQuerySql(String.format("%s LIMIT %s OFFSET %s", sql, pageDto.getPageSize(), pageDto.getSkip()));
    }

    @Override
    public List<Map<String, Object>> avgAndSum(String sql) {
        return this.executeQuerySql(sql);
    }

    // PostgreSQL列定义模板
    private static final String POSTGRESQL_PRIMARY_KEY = "PRIMARY KEY (%s)";
    private static final String POSTGRESQL_COLUMN_STRING_FORMAT = "%s varchar(%s)";
    private static final String POSTGRESQL_COLUMN_INT_FORMAT = "%s integer";
    private static final String POSTGRESQL_COLUMN_DOUBLE_FORMAT = "%s numeric(%s,%s)";
    private static final String POSTGRESQL_COLUMN_TIMESTAMP_FORMAT = "%s timestamp";
    private static final String POSTGRESQL_COLUMN_DATE_FORMAT = "%s date";
    private static final String POSTGRESQL_COLUMN_TEXT_FORMAT = "%s text";

    @Override
    public synchronized void syncTable(String databaseName, TableMetaData tableMetaData) {
        // 检查表是否存在
        boolean hitFlag = super.existsTableCheck(databaseName, null);
        if (hitFlag) {
            super.executeSql(String.format("DROP TABLE %s", getTableName().toLowerCase()));
        }

        List<String> columnList = buildCreateTableColumns(tableMetaData);
        String sql = String.format("CREATE TABLE %s (%s)",
                getTableName().toLowerCase(),
                String.join(", ", columnList));

        try {
            super.executeSql(sql);
        } catch (Exception ex) {
            log.error("syncTable executeSql:{} error", sql, ex);
            boolean showDetail = SpringUtils.getBean(GlobalExceptionProperties.class).isShowDetail();
            if (showDetail) {
                throw new ServiceException(ex);
            } else {
                throw new ServiceException("数据库操作失败，请联系系统管理员！");
            }
        }
    }

    private List<String> buildCreateTableColumns(TableMetaData tableMetaData) {
        List<String> columns = new ArrayList<>();
        for (int i = 0; i < tableMetaData.getColumnTypes().size(); i++) {
            String columnName = tableMetaData.getColumnNames().get(i).toLowerCase(); // 统一小写
            Class<?> clazz = tableMetaData.getColumnTypes().get(i);

            String columnDef;
            if (Integer.class == clazz) {
                columnDef = String.format(POSTGRESQL_COLUMN_INT_FORMAT, columnName);
            } else if (Double.class == clazz) {
                columnDef = String.format(POSTGRESQL_COLUMN_DOUBLE_FORMAT, columnName, 32, 8);
            } else if (Date.class == clazz) {
                // 根据实际需求选择timestamp或date
                columnDef = String.format(POSTGRESQL_COLUMN_TIMESTAMP_FORMAT, columnName);
            } else if (String.class == clazz) {
                columnDef = String.format(POSTGRESQL_COLUMN_STRING_FORMAT, columnName, 255);
            } else {
                // 其他类型默认为text
                columnDef = String.format(POSTGRESQL_COLUMN_TEXT_FORMAT, columnName);
                log.warn("Unsupported type: {}, converted to text", clazz.getSimpleName());
            }
            columns.add(columnDef);
        }

        // 添加主键（假设第一个字段为主键）
        if (!tableMetaData.getColumnNames().isEmpty()) {
            String primaryKey = String.format(
                    POSTGRESQL_PRIMARY_KEY,
                    tableMetaData.getColumnNames().get(0).toLowerCase()
            );
            columns.add(primaryKey);
        }

        return columns;
    }

    @Override
    public int getMaxId(String sql) {
        return 0;
    }
}
