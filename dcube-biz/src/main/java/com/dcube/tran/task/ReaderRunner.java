package com.dcube.tran.task;

import com.dcube.tran.exchanger.RecordSender;
import com.dcube.tran.plugin.Reader;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CountDownLatch;

@Slf4j
public class ReaderRunner implements Runnable {

    private Reader reader;

    private RecordSender recordSender;

    private CountDownLatch countDownLatch;

    public void setRecordSender(RecordSender recordSender) {
        this.recordSender = recordSender;
    }

    public ReaderRunner(Reader reader) {
        super();
        this.reader = reader;
    }

    @Override
    public void run() {
        try {
            assert null != this.recordSender;
            reader.startRead(recordSender);
            recordSender.terminate();
            log.info("reader runner [{}] is finished .", Thread.currentThread().getName());
        } catch (Exception e) {
            log.error("reader runner exception ", e);
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }

    public void shutdown() {
        recordSender.shutdown();
    }

    public CountDownLatch getCountDownLatch() {
        return countDownLatch;
    }

    public void setCountDownLatch(CountDownLatch countDownLatch) {
        this.countDownLatch = countDownLatch;
    }
}
