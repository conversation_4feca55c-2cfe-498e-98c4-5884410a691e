package com.dcube.tran.task;

import com.dcube.common.aspect.ThreadLocalCacheAspect;
import com.dcube.tran.execution.AbstractTranExecution;
import com.dcube.tran.plugin.Reader;
import com.dcube.tran.plugin.Writer;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.Semaphore;

@Slf4j
public class TaskSingleContainer {

    public static int TASK_COUNT = 1;

    private static final int MAX_TASK_NUMBER = 10;

    private static final Semaphore SEMAPHORE = new Semaphore(MAX_TASK_NUMBER, true);

    private String taskId;

    private int taskGroupId;

    private Reader reader;

    private Writer writer;

    private List<AbstractTranExecution> tranExecutionList;

    private TaskSingleContainer() {

    }


    public TaskSingleContainer(String taskId, int taskGroupId, final Reader reader, final Writer writer, final List<AbstractTranExecution> tranExecutionList) {
        assert writer != null;
        this.taskId = taskId;
        this.taskGroupId = taskGroupId;
        this.reader = reader;
        this.writer = writer;
        this.tranExecutionList = tranExecutionList;
    }

    public void run() {
        try {
            ThreadLocalCacheAspect.init();
            ThreadLocalCacheAspect.initTtl();
            SEMAPHORE.acquire();
            log.info("taskGroupId[{}] running on [{}].", this.taskGroupId, TASK_COUNT++);
            TaskExecutor taskExecutor = new TaskExecutor(this.taskGroupId, this.taskId, this.reader, this.writer, this.tranExecutionList);
            taskExecutor.doStart();
        } catch (Exception e) {
            log.error("taskGroupId[{}] sleeping is error. ", this.taskGroupId);
        } finally {
            SEMAPHORE.release();
        }
    }
}
