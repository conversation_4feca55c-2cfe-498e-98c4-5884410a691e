package com.dcube.common.aspect;

import com.dcube.common.annotation.ThreadLocalCache;
import com.dcube.common.enums.ThreadLocalCacheType;
import com.dcube.common.utils.CacheNull;
import com.dcube.common.utils.ThreadLocalUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collection;


@Aspect
@Order(0)
@Component
public class ThreadLocalCacheAspect {

    @Pointcut("within(com.dcube..*) && @annotation(com.dcube.common.annotation.ThreadLocalCache)")
    public void threadLocalCache() {
    }

//    @Pointcut("within(com.dcube..*) && @annotation(com.dcube.common.annotation.ThreadLocalCache.ThreadLocalCaches)")
//    public void threadLocalCaches() {
//    }

    private static final String INIT = "init";
    private static final String INIT_TTL = "init_ttl";

    public static void init() {
        ThreadLocalUtils.set(ThreadLocalCacheAspect.class, INIT, Boolean.TRUE);
    }

    public static void initTtl() {
        ThreadLocalUtils.setTtl(ThreadLocalCacheAspect.class, INIT_TTL, Boolean.TRUE);
    }

    private static boolean isInit() {
        return Boolean.TRUE.equals(ThreadLocalUtils.get(ThreadLocalCacheAspect.class, INIT));
    }

    private static boolean isInitTtl() {
        return Boolean.TRUE.equals(ThreadLocalUtils.getTtl(ThreadLocalCacheAspect.class, INIT_TTL));
    }

    @Around("threadLocalCache()")
    public Object aroundThreadLocalCache(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature;
        if (!(signature instanceof MethodSignature)) {
            throw new IllegalArgumentException("该注解只能用于方法");
        }
        methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        if (method.isAnnotationPresent(ThreadLocalCache.class)) {
            ThreadLocalCache threadLocalCache = method.getAnnotation(ThreadLocalCache.class);
            Object value = null;
            if (ThreadLocalCacheType.TTL == threadLocalCache.value()) {
                if (!isInitTtl()) {
                    return joinPoint.proceed();
                }
                Object target = joinPoint.getTarget();
                Class<?> targetClass = target.getClass();
                Object[] args = joinPoint.getArgs();
                // key为方法名_参数1_参数2
                StringBuilder keyValueSb = new StringBuilder(methodSignature.getName());
                for (Object arg : args) {
                    if (arg instanceof Collection) {
                        for (Object o : (Collection) arg) {
                            keyValueSb.append("_").append(o);
                        }
                    } else {
                        keyValueSb.append("_").append(arg);
                    }
                }
                String keyValue = keyValueSb.toString();
                value = ThreadLocalUtils.getTtl(targetClass, keyValue);
                if (value == null) {
                    value = joinPoint.proceed();
                    if (value == null) {
                        ThreadLocalUtils.setTtl(targetClass, keyValue, CacheNull.INSTANCE);
                    } else {
                        ThreadLocalUtils.setTtl(targetClass, keyValue, value);
                    }
                } else if (value == CacheNull.INSTANCE) {
                    return null;
                }
                return value;
            } else {
                if (!isInit()) {
                    return joinPoint.proceed();
                }
                Object target = joinPoint.getTarget();
                Class<?> targetClass = target.getClass();
                Object[] args = joinPoint.getArgs();
                // key为方法名_参数1_参数2
                StringBuilder keyValueSb = new StringBuilder(methodSignature.getName());
                for (Object arg : args) {
                    if (arg instanceof Collection) {
                        for (Object o : (Collection) arg) {
                            keyValueSb.append("_").append(o);
                        }
                    } else {
                        keyValueSb.append("_").append(arg);
                    }
                }
                String keyValue = keyValueSb.toString();
                value = ThreadLocalUtils.get(targetClass, keyValue);
                if (value == null) {
                    value = joinPoint.proceed();
                    if (value == null) {
                        ThreadLocalUtils.set(targetClass, keyValue, CacheNull.INSTANCE);
                    } else {
                        ThreadLocalUtils.set(targetClass, keyValue, value);
                    }
                } else if (value == CacheNull.INSTANCE) {
                    return null;
                }
                return value;
            }
        }
        return joinPoint.proceed();
    }

//    @Around("threadLocalCaches()")
//    public Object aroundThreadLocalCaches(ProceedingJoinPoint joinPoint) throws Throwable {
//        Signature signature = joinPoint.getSignature();
//        MethodSignature methodSignature;
//        if (!(signature instanceof MethodSignature)) {
//            throw new IllegalArgumentException("该注解只能用于方法");
//        }
//        methodSignature = (MethodSignature) signature;
//        Method method = methodSignature.getMethod();
//        Object target = joinPoint.getTarget();
//        Class<?> targetClass = target.getClass();
//        Object[] args = joinPoint.getArgs();
//        // key为方法名_参数1_参数2
//        StringBuilder keyValueSb = new StringBuilder(methodSignature.getName());
//        for (Object arg : args) {
//            if (arg instanceof Collection) {
//                for (Object o : (Collection) arg) {
//                    keyValueSb.append("_").append(o);
//                }
//            } else {
//                keyValueSb.append("_").append(arg);
//            }
//        }
//        String keyValue = keyValueSb.toString();
//        if (method.isAnnotationPresent(ThreadLocalCache.ThreadLocalCaches.class)) {
//            ThreadLocalCache.ThreadLocalCaches threadLocalCaches = AnnotationUtils.findAnnotation(method, ThreadLocalCache.ThreadLocalCaches.class);
//            Object value = null;
//            List<ThreadLocalCache> cacheList = new ArrayList<>(Arrays.asList(threadLocalCaches.value()));
//            // 以tl为准
//            Optional<ThreadLocalCache> tlOptional = cacheList.stream().filter(v -> v.value() == ThreadLocalCacheType.TL).findFirst();
//            if (tlOptional.isPresent()) {
//                if (!isInit()) {
//                    return joinPoint.proceed();
//                }
//                value = ThreadLocalUtils.get(targetClass, keyValue);
//                if (value == null) {
//                    value = joinPoint.proceed();
//                } else if (value == CacheNull.INSTANCE) {
//                    value = null;
//                }
//            }
//            // tl没找到，找ttl
//            if (value == null) {
//                Optional<ThreadLocalCache> ttlOptional = cacheList.stream().filter(v -> v.value() == ThreadLocalCacheType.TTL).findFirst();
//                if (ttlOptional.isPresent()) {
//                    if (isInitTtl()) {
//                        value = ThreadLocalUtils.getTtl(targetClass, keyValue);
//                        if (value == CacheNull.INSTANCE) {
//                            value = null;
//                        }
//                    }
//                }
//            }
//            if (value == null) {
//                ThreadLocalUtils.set(targetClass, keyValue, CacheNull.INSTANCE);
//            } else {
//                ThreadLocalUtils.set(targetClass, keyValue, value);
//            }
//        }
//        return joinPoint.proceed();
//    }
}
