package com.dcube.common.config;

import cn.hutool.crypto.asymmetric.RSA;
import com.dcube.common.config.properties.RSAProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RSAConfig {

    @Bean
    public RSA rsa(RSAProperties rsaProperties) {
        return new RSA(rsaProperties.getPrivateKey(), rsaProperties.getPublicKey());
    }

}
