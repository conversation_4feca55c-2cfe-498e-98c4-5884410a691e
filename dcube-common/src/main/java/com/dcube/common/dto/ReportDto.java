package com.dcube.common.dto;

import com.dcube.common.constant.enums.JobOperateTypeEnums;
import com.dcube.common.constant.enums.StateEnum;
import com.dcube.common.constant.enums.TaskStatusEnums;
import com.dcube.common.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@ToString
@Accessors(chain = true)
@EqualsAndHashCode
public class ReportDto<T> {

    public ReportDto() {
    }

    public ReportDto(String taskId, String taskName, long totalRecord, T currentRecord) {
        this.state = StateEnum.RUNNING.getCode();
        this.taskId = taskId;
        this.taskName = taskName;
        this.totalRecord = totalRecord;
        this.currentRecord = currentRecord;
        this.percentage = 0L;
        this.recordSpeed = 0D;
        this.startTime = System.currentTimeMillis();
        this.currentTime = this.startTime;
        this.sec = 0L;

    }

    private String state;

    private String taskId;

    private String taskName;

    private Long totalRecord;

    private T currentRecord;

    private Long percentage;

    private Double recordSpeed;

    private Long startTime;

    private Long currentTime;

    private Long sec;

    /**
     * 异常信息
     */
    private String msg;
    /**
     * 进度
     */
    private String progress;
    /**
     * 任务状态
     */
    private TaskStatusEnums status;
    /**
     * 任务类型
     */
    private JobOperateTypeEnums type;
    /**
     * 批次状态
     */
    private StateEnum batchStatus;
    /**
     * 耗时
     */
    private Long cost;

    /**
     * 存活检查次数
     */
    private int aliveCheckCount;

    public ReportDto<T> setStatus(TaskStatusEnums status) {
        if (this.status != TaskStatusEnums.ERROR) {
            this.status = status;
        }
        return this;
    }

    public ReportDto<T> setMsg(String msg) {
        if (StringUtils.isEmpty(this.msg) && StringUtils.isNotEmpty(msg)) {
            this.msg = msg;
        }
        return this;
    }

    public TaskStatusEnums getStatus() {
        if (this.status == TaskStatusEnums.COMPLETE) {
            if (this.batchStatus == StateEnum.RUNNING) {
                return TaskStatusEnums.START;
            } else if (this.batchStatus == StateEnum.SUCCEEDED) {
                return TaskStatusEnums.COMPLETE;
            }
        }
        return status;
    }

}
