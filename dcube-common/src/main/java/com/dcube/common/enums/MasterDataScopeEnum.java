package com.dcube.common.enums;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
public enum MasterDataScopeEnum {

    /**
     * 全部
     */
    ALL(0, "全部"),

    /**
     * 自己
     */
    SELF(1, "自己"),

    /**
     * 机构（不含下级）
     */
    ORG(2, "机构（不含下级）"),

    /**
     * 机构（含下级）
     */
    ORG_INCLUDE(3, "机构（含下级）");

    private final Integer code;

    private final String description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static List<Map<String, Object>> toList() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MasterDataScopeEnum enumItem : MasterDataScopeEnum.values()) {
            Map<String, Object> entry = new HashMap<>();
            entry.put("id", enumItem.getCode());
            entry.put("description", enumItem.getDescription());
            list.add(entry);
        }
        return list;
    }
}
