package com.dcube.common.enums;

/**
 * @创建人 zhouhx
 * @创建时间 2024/6/12 11:49
 * @描述 系统变量枚举
 */
public enum SystemVariable {

    CURRENT_TIME("CURRENT_TIME","获取当前时间"),
    CURRENT_DATE("CURRENT_DATE","获取当前日期"),
    CURRENT_USER("CURRENT_USER","获取当前登录账号"),

    CURRENT_USER_NAME("CURRENT_USER_NAME","获取当前登录用户姓名"),
    CURRENT_DEPARTMENT("CURRENT_DEPARTMENT","获取当前登录用户部门名称");

    private final String variableName;
    private final String desc;

    SystemVariable(String variableName, String desc) {
        this.variableName = variableName;
        this.desc = desc;
    }

    public String getVariableName() {
        return variableName;
    }

    public String getDesc() {
        return desc;
    }
}
