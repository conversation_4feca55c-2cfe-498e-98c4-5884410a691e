package com.dcube.common.utils.file;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.nio.file.Files;
import java.rmi.ServerException;
import java.util.Set;

/**
 * @创建人 zhouhx
 * @创建时间 2023/12/2 20:27
 * @描述
 */
@Slf4j
public class ZipFileUtils {

    /**
     * 解压并过滤指定后缀名称的文件
     *
     * @param zipFile
     * @param outputDir
     * @param filterFileSuffix
     * @throws IOException
     */
    public static String unzipAndFilterFile(File zipFile, File outputDir, Set<String> filterFileSuffix) throws IOException {
        StringBuilder outputFileDir = new StringBuilder(outputDir.getAbsolutePath());
        if (CollectionUtils.isEmpty(filterFileSuffix)) {
            log.error("文件后缀不能为空。");
            throw new ServerException("文件后缀不能为空。");
        }
        try (InputStream fis = Files.newInputStream(zipFile.toPath());
             ZipArchiveInputStream zis = new ZipArchiveInputStream(fis, "UTF-8")) {
            ArchiveEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File outputFile = new File(outputDir, entry.getName().replaceAll("\\s", ""));
                if (entry.isDirectory()) {
                    outputFile.mkdirs();
                    outputFileDir.append("/").append(entry.getName());
                } else {
                    String fileSuffix = FilenameUtils.getExtension(outputFile.getName().replaceAll("\\s", ""));
                    if (filterFileSuffix.contains(fileSuffix)) {
                        writeFile(zis, outputFile);
                    }
                }
            }
        }
        return outputFileDir.toString();
    }

    /**
     * 解压所有文件
     *
     * @param zipFile
     * @param outputDir
     * @throws IOException
     */
    public static void unzip(File zipFile, File outputDir) throws IOException {
        try (InputStream fis = Files.newInputStream(zipFile.toPath());
             ZipArchiveInputStream zis = new ZipArchiveInputStream(fis)) {
            ArchiveEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File outputFile = new File(outputDir, entry.getName().replaceAll("\\s", ""));
                if (entry.isDirectory()) {
                    outputFile.mkdirs();
                } else {
                    writeFile(zis, outputFile);
                }
            }
        }
    }

    /**
     * 写入文件
     *
     * @param zis
     * @param outputFile
     * @throws IOException
     */
    private static void writeFile(ZipArchiveInputStream zis, File outputFile) throws IOException {
        outputFile.getParentFile().mkdirs();
        try (OutputStream fos = Files.newOutputStream(outputFile.toPath())) {
            IOUtils.copy(zis, fos);
        }
    }

    /**
     * 压缩文件
     *
     * @param inputFile
     * @param outFile
     * @throws IOException
     */
    public static void zip(File inputFile, File outFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outFile.getName());
             ZipArchiveOutputStream zaos = new ZipArchiveOutputStream(fos);
             InputStream inputStream = Files.newInputStream(inputFile.toPath())) {
            ZipArchiveEntry entry = new ZipArchiveEntry(inputFile, inputFile.getName());
            zaos.putArchiveEntry(entry);
            IOUtils.copy(inputStream, zaos);
            zaos.closeArchiveEntry();
        }
    }

    public static void main(String[] args) throws IOException {

    }
}
