package com.dcube.common.utils.mdc;

import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.uuid.IdUtils;
import org.slf4j.MDC;

import java.util.Map;

/**
 * MDC日志追踪工具类
 */
public class MdcUtil {
    public static final String TRACE_ID = "traceId";
    public static final String IP = "IP";

    public static String generateTraceId() {
        String traceId = getTraceId();
        if (StringUtils.isNotEmpty(traceId)) {
            return traceId;
        }
        return IdUtils.getSnowflakeNextIdStr();
    }

    public static String getTraceId() {
        return MDC.get(TRACE_ID);
    }

    public static void setTraceId(String traceId) {
        MDC.put(TRACE_ID, traceId);
    }

    public static void setIp(String ip) {
        MDC.put(IP, ip);
    }

    public static void setContextMap(Map<String, String> context) {
        MDC.setContextMap(context);
    }

    public static void clear() {
        MDC.clear();
    }
}
