package com.dcube.common.utils.redisson;

import com.dcube.common.exception.RedissonLockException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Component
public class RedissonLockUtil {

    private final RedissonClient redissonClient;

    public RedissonLockUtil(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 带返回值的分布式锁操作（使用毫秒单位）
     *
     * @param lockKey      锁的key
     * @param waitTime     等待时间
     * @param leaseTime    锁持有时间
     * @param action       要执行的操作
     * @param failHandler 获取锁失败时的处理
     * @return 业务执行结果
     */
    public <T> T tryLockWithResult(String lockKey,
                                   long waitTime,
                                   long leaseTime,
                                   Supplier<T> action,
                                   Supplier<T> failHandler) {
        return tryLockWithResult(
                lockKey,
                waitTime,
                leaseTime,
                TimeUnit.MILLISECONDS,
                action,
                failHandler
        );
    }

    /**
     * 带返回值的分布式锁操作
     *
     * @param lockKey      锁的key
     * @param waitTime     等待时间
     * @param leaseTime    锁持有时间
     * @param unit         时间单位
     * @param action       要执行的操作
     * @param failHandler 获取锁失败时的处理
     * @return 业务执行结果
     */
    public <T> T tryLockWithResult(String lockKey,
                                   long waitTime,
                                   long leaseTime,
                                   TimeUnit unit,
                                   Supplier<T> action,
                                   Supplier<T> failHandler) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean acquired = false;
        try {
            acquired = lock.tryLock(waitTime, leaseTime, unit);
            if (acquired) {
                try {
                    return action.get();
                } finally {
                    unlock(lock);
                }
            }
            return failHandler.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RedissonLockException("Lock acquisition interrupted", e);
        } finally {
            if (acquired) {
                unlock(lock);
            }
        }
    }

    /**
     * 释放锁
     */
    private void unlock(RLock lock) {
        if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    /**
     * 不带返回值的分布式锁操作
     *
     * @param lockKey      锁的key
     * @param waitTime     等待时间（毫秒）
     * @param leaseTime   锁持有时间（毫秒）
     * @param action       要执行的操作
     * @param failHandler 获取锁失败时的处理
     */
    public void tryLock(String lockKey, long waitTime, long leaseTime,
                        Runnable action, Runnable failHandler) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(waitTime, leaseTime, TimeUnit.MILLISECONDS)) {
                try {
                    action.run();
                } finally {
                    unlock(lock);
                }
            } else {
                failHandler.run();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RedissonLockException("Lock acquisition interrupted", e);
        } finally {
            unlock(lock);
        }
    }

}