package com.dcube.framework.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.dcube.common.core.domain.model.LoginUser;
import com.dcube.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Configuration;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Configuration
public class MybatisMetaObjectConfig implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        this.setFieldValByName("createTime", new Date(), metaObject);
        this.setFieldValByName("updateTime", new Date(), metaObject);
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (Objects.nonNull(loginUser)) {
                this.setFieldValByName("createBy", ObjectUtils.isEmpty(loginUser) ? "" : loginUser.getUsername(), metaObject);
                this.setFieldValByName("updateBy", ObjectUtils.isEmpty(loginUser) ? "" : loginUser.getUsername(), metaObject);
            }
        } catch (Exception e) {
            log.error("mybatis填充insertField时出现异常：", e);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("updateTime", new Date(), metaObject);
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (Objects.nonNull(loginUser)) {
                this.setFieldValByName("updateBy", ObjectUtils.isEmpty(loginUser) ? "" : loginUser.getUsername(), metaObject);
            }
        } catch (Exception e) {
            log.error("mybatis填充updateField时出现异常：", e);
        }
    }
}

