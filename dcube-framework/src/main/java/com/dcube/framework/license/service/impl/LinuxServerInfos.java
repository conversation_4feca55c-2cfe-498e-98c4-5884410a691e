package com.dcube.framework.license.service.impl;

import com.dcube.common.utils.StringUtils;
import com.dcube.framework.license.service.AbstractServerInfos;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用于获取客户Linux服务器的基本信息
 */
@Slf4j
public class LinuxServerInfos extends AbstractServerInfos {
    @Override
    protected Set<String> getIpAddress() throws Exception {
        Set<String> result = null;

        //获取所有网络接口
        List<InetAddress> inetAddresses = getLocalAllInetAddress();

        if(inetAddresses != null && inetAddresses.size() > 0){
            result = inetAddresses.stream().map(InetAddress::getHostAddress).distinct().map(String::toLowerCase).collect(Collectors.toSet());
        }

        return result;
    }


    protected Set<String> getMacAddress() throws Exception {
        Set<String> macs = new HashSet<>();
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();

            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();

                // 跳过回环接口、未启用的接口、虚拟接口和点对点接口
                if (networkInterface.isLoopback() || !networkInterface.isUp() ||
                        networkInterface.isVirtual() || networkInterface.isPointToPoint()) {
                    continue;
                }

                byte[] mac = networkInterface.getHardwareAddress();
                if (mac != null && mac.length > 0) {
                    macs.add(formatMacAddress(mac));
                }
            }

            if (macs.isEmpty()) {
                throw new Exception("无法获取有效的MAC地址");
            }

            log.info("获取的MAC地址为: {}", macs);
            return macs;
        } catch (SocketException e) {
            throw new Exception("获取网络接口时发生错误", e);
        }
    }

    /**
     * 格式化MAC地址为XX:XX:XX:XX:XX:XX格式，转小写
     * @param mac MAC地址字节数组
     * @return 格式化后的MAC地址字符串
     */
    private static String formatMacAddress(byte[] mac) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < mac.length; i++) {
            sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? ":" : ""));
        }
        return StringUtils.lowerCase(sb.toString());
    }
}
