package com.dcube.framework.license.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * @创建人 zhouhx
 * @创建时间 2023/8/24 16:34
 * @描述
 */
@Builder
@Data
public class LicenseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private CustomerVo customer;

    private Set<String> macAddress;

}
