package com.dcube.framework.netty;

import com.dcube.common.exception.ServiceException;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @创建人 zhouhx
 * @创建时间 2023/12/8 09:19
 * @描述
 */
@Component
public class PushMsgServiceImpl implements MsgService {

    /**
     * 推送给指定用户
     *
     * @param userId
     * @param msg
     */
    @Override
    public void pushToUser(String msg, Long userId) {
        Channel channel = NettyConfig.getChannel(userId);
        if (Objects.isNull(channel)) {
            throw new ServiceException("未连接socket服务器");
        }
        channel.writeAndFlush(new TextWebSocketFrame(msg));
    }

    /**
     * 推送给所有用户
     *
     * @param msg
     */
    @Override
    public void pushToAll(String msg) {
        NettyConfig.getChannelGroup().writeAndFlush(new TextWebSocketFrame(msg));
    }
}
