-- ----------------------------
-- Records of cube_dic_item
-- ----------------------------
BEGIN;
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('05a605fa864d3741239d37c6d33ea83f', '4f0249520a8051b9ee817e0d4d84e4a4', '1', '公司客户', '0', NULL,
        '2023-03-13 14:44:21', 'admin', '2023-03-13 14:44:21');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('09806d431b66d685a5f7e72e0c87c767', 'beb665369ea82a4d53b945869d1bf1d9', '3', '中国农业银行', NULL, NULL,
        '2023-02-15 14:58:07', 'admin', '2023-02-15 14:58:07');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('203578286ec171e9a6b152bfd946eb2b', 'cddb8f4c8f5085262c8efdfa0df70ba1', '2', '女', '0', NULL,
        '2023-02-15 14:31:01', 'admin', '2023-03-07 10:33:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('2ee9d27ae248296296ee33086f17221e', 'ff78c09bc2e9723597ec3c4e794a4173', '1', '是', '0', NULL,
        '2023-03-13 14:42:15', 'admin', '2023-03-13 14:42:15');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('2fc2a88674b1fa8cfef84c1bc31abb37', '8720f30bb4eb1714fd6c61267a918110', '1', '是', '0', NULL,
        '2023-03-13 14:43:07', 'admin', '2023-03-13 14:43:07');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('367c9ccf19b87d681aad2d0371b0eecb', 'ff78c09bc2e9723597ec3c4e794a4173', '0', '否', '0', NULL,
        '2023-03-13 14:42:15', 'admin', '2023-03-13 14:42:15');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('374c66b29d0499b3176a16c2fd5c6691', 'beb665369ea82a4d53b945869d1bf1d9', '1', '中国工商银行', '0', NULL,
        '2023-02-15 14:57:49', 'admin', '2023-02-15 14:58:07');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('379159d2afd1f828c311e9d4db591e60', 'cddb8f4c8f5085262c8efdfa0df70ba1', '1', '男', '0', NULL,
        '2023-02-15 14:31:01', 'admin', '2023-03-07 10:33:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('3a2476376709bacfecaa162d3a2d3aac', '377f1800cc43430ab5513857fac4db39', '产品A', '产品A', '0', NULL,
        '2023-07-22 09:33:27', 'admin', '2023-08-14 09:45:12');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('3d80e1a644d59fcd69c56052016baf10', '840054afc8528e69cea42bbc0a779acc', '003', '民企', '0', NULL,
        '2023-02-25 18:25:48', 'admin', '2023-02-25 18:26:36');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('41cdae86bc152168a22bdb241df209d0', '377f1800cc43430ab5513857fac4db39', '产品B', '产品B', '0', NULL,
        '2023-07-22 09:33:27', 'admin', '2023-08-14 09:45:12');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('4df21809408f1250df5d4cb148f8e360', 'e7c2be5b930c9603e2cc3b43527a8764', '01', '陕西', '0', NULL,
        '2023-06-01 11:00:53', 'admin', '2023-06-01 11:00:53');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('51355524dae7430ad20c900ede4810e6', '840054afc8528e69cea42bbc0a779acc', '002', '地方国企', '0', NULL,
        '2023-02-25 18:25:48', 'admin', '2023-02-25 18:26:36');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('54c3236d1fba6487d7b121b7a6d18473', 'b7e1154c90ad2c44d9ad5494069e243d', '华东', '华东', NULL, NULL,
        '2023-07-30 17:53:27', 'admin', '2023-08-14 09:40:12');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('5a82b47a6f94e3822fcb200f3a2dee5e', 'c328aeeb64ba2657da4297ecaaa21fb4', '1', '男', '0', NULL,
        '2023-03-13 14:41:35', 'admin', '2023-03-13 14:41:35');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('5af7a3f74f0d0a9429201e1cce5ee160', '5be46c74de392c1affa1d9d14e631437', 'FLOAT', '浮点型', '0', NULL,
        '2023-03-02 16:02:26', 'admin', '2023-03-02 16:02:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('5ce317d5bbcafaaaf8e8d93c5632a2de', '8e0d06c87cd5fb92782bdca14a15f08d', '男', '男', '0', NULL,
        '2023-08-14 09:46:18', 'admin', '2023-08-14 09:46:18');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('6b7256e5a362eca0abb5c1823450e06f', '4f0249520a8051b9ee817e0d4d84e4a4', '2', '零售客户', '0', NULL,
        '2023-03-13 14:44:21', 'admin', '2023-03-13 14:44:21');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('6fe29bcfb054b23e75eb70174243addd', 'c8caae7f49e22f91d93a8936e68502bc', '是', '是', '0', NULL,
        '2023-08-14 09:46:49', 'admin', '2023-08-14 09:46:49');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('7173e145d0423dda81f72b548307f526', 'e14347c3146fe49462f44cf8bf1ebd52', '3', '奥迪', NULL, NULL,
        '2023-02-15 15:20:22', 'admin', '2023-02-15 15:21:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('71dd83514bb5318a06c2645c1e97e4ff', '377f1800cc43430ab5513857fac4db39', '产品C', '产品C', '0', NULL,
        '2023-07-22 09:33:27', 'admin', '2023-08-14 09:45:12');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('71f7f7aef4a2458fd80d2af36dcb3367', '4f0249520a8051b9ee817e0d4d84e4a4', '3', '小微客户', '0', NULL,
        '2023-03-13 14:44:21', 'admin', '2023-03-13 14:44:21');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('7398842064c056a2bde2e3724f2a62c1', '5be46c74de392c1affa1d9d14e631437', 'DECIMAL', '金额', '0', NULL,
        '2023-03-02 16:02:26', 'admin', '2023-03-02 16:02:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('73cb6933274df695d5e1b7a6c7d2a7a4', 'e7c2be5b930c9603e2cc3b43527a8764', '02', '湖北', '0', NULL,
        '2023-06-01 11:00:53', 'admin', '2023-06-01 11:00:53');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('765da5c104825a338d33fe27e8997970', '8e0d06c87cd5fb92782bdca14a15f08d', '女', '女', '0', NULL,
        '2023-08-14 09:46:18', 'admin', '2023-08-14 09:46:18');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('82ea088d56449f4b19fb92fcd962ee16', '8720f30bb4eb1714fd6c61267a918110', '0', '否', '0', NULL,
        '2023-03-13 14:43:07', 'admin', '2023-03-13 14:43:07');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('89e9f93814e857d0703e3f666fe2ec7f', 'b7e1154c90ad2c44d9ad5494069e243d', '华北', '华北', '0', NULL,
        '2023-07-30 17:52:59', 'admin', '2023-08-14 09:40:12');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('8be477517443788df867532779979d2b', 'b7e1154c90ad2c44d9ad5494069e243d', '西北', '西北', '0', NULL,
        '2023-07-30 17:52:59', 'admin', '2023-08-14 09:40:12');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('8d5efa31e6f25d952369066a23bdc01c', '97abf9858cc3ea9a5630662f16d8e927', '01', '是', '0', NULL,
        '2023-06-06 15:16:31', 'admin', '2023-06-06 15:16:31');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('8f0dd4d6cf0154e5d6fe26afe2542b8b', '840054afc8528e69cea42bbc0a779acc', '001', '央企', '0', NULL,
        '2023-02-25 18:25:48', 'admin', '2023-02-25 18:26:36');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('936b11f08337b2cc6b360bf19ae73fb4', 'c328aeeb64ba2657da4297ecaaa21fb4', '2', '女', '0', NULL,
        '2023-03-13 14:41:35', 'admin', '2023-03-13 14:41:35');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('96f485b7871677ccd5b609647ee0ebb4', 'e14347c3146fe49462f44cf8bf1ebd52', '1', '奔驰', NULL, NULL,
        '2023-02-15 15:21:26', 'admin', '2023-02-15 15:21:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('a00f5529cb09210102e377d0d20bbbdb', '97abf9858cc3ea9a5630662f16d8e927', '02', '否', '0', NULL,
        '2023-06-06 15:16:31', 'admin', '2023-06-06 15:16:31');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('a01ec30b7be17d8b84f1235534e6c4fb', 'e7c2be5b930c9603e2cc3b43527a8764', '03', '湖南', '0', NULL,
        '2023-06-01 11:00:53', 'admin', '2023-06-01 11:00:53');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('ac74d52cd493a7acdeb0d24a97c95959', 'e14347c3146fe49462f44cf8bf1ebd52', '2', '宝马', NULL, NULL,
        '2023-02-15 15:20:12', 'admin', '2023-02-15 15:21:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('ae9ccbfd5a851e355756cb818fad3b1f', '5be46c74de392c1affa1d9d14e631437', 'DATE', '日期', '0', NULL,
        '2023-03-02 16:02:26', 'admin', '2023-03-02 16:02:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('b7292431898214d9ae6794e0777b0817', 'b7e1154c90ad2c44d9ad5494069e243d', '东北', '东北', '0', NULL,
        '2023-07-30 17:52:59', 'admin', '2023-08-14 09:40:12');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('cc994fe9bdbf1aa3397275dd4f0121c7', '0f31272081cf6c8921e72ebc6a409bc8', 'GROUP', '分组', '0', NULL,
        '2023-02-28 19:22:18', 'admin', '2023-02-28 19:30:17');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('dc8ac39e85be10060c8a00f753b2695e', 'e7c2be5b930c9603e2cc3b43527a8764', '04', '吉林', '0', NULL,
        '2023-06-01 11:00:53', 'admin', '2023-06-01 11:00:53');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('e32efb0971660bc9fd636e2496f6ff57', 'beb665369ea82a4d53b945869d1bf1d9', '2', '中国建设银行', '0', NULL,
        '2023-02-15 14:57:49', 'admin', '2023-02-15 14:58:07');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('e88885232b0b985268f21a3b1ae662ec', '5be46c74de392c1affa1d9d14e631437', 'INT', '整数', '0', NULL,
        '2023-03-02 16:02:26', 'admin', '2023-03-02 16:02:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('fbfc23e880a1eae93e0e7b306d5062cb', 'c8caae7f49e22f91d93a8936e68502bc', '否', '否', '0', NULL,
        '2023-08-14 09:46:49', 'admin', '2023-08-14 09:46:49');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('fca997f2c96b4e43fcbabf96e4559f84', '5be46c74de392c1affa1d9d14e631437', 'VARCHAR', '字符', '0', NULL,
        '2023-03-02 16:02:26', 'admin', '2023-03-02 16:02:26');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('fcbb2720f9b6819eaf8a36a003c6be73', '0f31272081cf6c8921e72ebc6a409bc8', 'TABLE', '表', '0', NULL,
        '2023-02-28 19:22:18', 'admin', '2023-02-28 19:30:17');
INSERT INTO "cube_dic_item" ("id", "dic_id", "dic_code", "dic_label", "status", "create_by", "create_time",
                             "update_by", "update_time")
VALUES ('fd236cbf31b911371c8715cb54bca798', 'b7e1154c90ad2c44d9ad5494069e243d', '华南', '华南', '0', NULL,
        '2023-07-30 17:52:59', 'admin', '2023-08-14 09:40:12');


-- ----------------------------
-- Records of cube_group
-- ----------------------------

INSERT INTO "cube_group" ("id", "group_code", "group_name", "status", "create_by", "create_time", "update_by",
                          "update_time")
VALUES ('0447ac465927294ee153ae4ee05f175b', '数据字典', '数据字典', '0', NULL, '2023-07-22 09:08:40', 'admin',
        '2023-07-22 11:40:15');
INSERT INTO "cube_group" ("id", "group_code", "group_name", "status", "create_by", "create_time", "update_by",
                          "update_time")
VALUES ('1b3f4ace5a71f2d6e85d68eb68e68b53', '日期', '日期', '0', NULL, '2023-02-13 17:10:16', 'admin',
        '2023-03-13 14:44:58');
INSERT INTO "cube_group" ("id", "group_code", "group_name", "status", "create_by", "create_time", "update_by",
                          "update_time")
VALUES ('511dd862eb7b37141082a0b5802919ba', '通用', '通用', '0', NULL, '2023-02-11 14:27:04', 'admin',
        '2023-03-31 17:19:14');
INSERT INTO "cube_group" ("id", "group_code", "group_name", "status", "create_by", "create_time", "update_by",
                          "update_time")
VALUES ('7c8a84e530fcd75b65c80f64f6bd3bf9', '字典', '字典', '0', NULL, '2023-03-02 15:57:32', 'admin',
        '2023-03-13 14:41:03');
INSERT INTO "cube_group" ("id", "group_code", "group_name", "status", "create_by", "create_time", "update_by",
                          "update_time")
VALUES ('86d1a4e4b912dfdbd3ce439a92db9438', '金额', '金额', '0', NULL, '2023-02-13 15:08:51', 'admin',
        '2023-03-13 14:47:00');
INSERT INTO "cube_group" ("id", "group_code", "group_name", "status", "create_by", "create_time", "update_by",
                          "update_time")
VALUES ('c1bd00a05fe8552a98651c299fb75afb', '数字', '数字', NULL, NULL, '2023-02-13 15:45:11', 'admin',
        '2023-03-13 14:45:36');


-- ----------------------------
-- Records of cube_group_instance
-- ----------------------------

INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('2cc6d4bf4d99b6abcaac68f557d2805b', '511dd862eb7b37141082a0b5802919ba', '文本（居中）', NULL, 'N', 'VARCHAR', '',
        0, '10', '', '', '超算力即时分析平台实时', '超算力即时分析平台实时', 'middle', '0', NULL, '2023-08-15 15:46:52',
        'admin', '2023-08-15 15:46:52');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('321dc28d559b9666d51bcfcef36f35be', NULL, '性别', NULL, 'N', 'string', '', 0, '', '', '', NULL, NULL, NULL, '0',
        NULL, '2023-02-13 17:34:47', 'admin', '2023-02-13 17:34:47');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('377f1800cc43430ab5513857fac4db39', '0447ac465927294ee153ae4ee05f175b', '产品', NULL, 'Y', 'VARCHAR', '', 0,
        '10', '1', '2', '超算力即时分析平台实时', '超算力即时分析平台实时', 'left', '0', NULL, '2023-07-22 09:32:09',
        'admin', '2023-07-22 09:32:09');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('3b583866df798d08b8dd811f1f14fe0a', '511dd862eb7b37141082a0b5802919ba', '无', NULL, 'N', 'VARCHAR', '', 0, '',
        '', '', 'dcube', 'dcube', 'left', '0', NULL, '2023-03-31 17:18:57', 'admin', '2023-03-31 17:18:57');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('4a4af9712d445721a65db4ab4dca65b6', NULL, '性别', NULL, 'Y', 'string', '', 0, '', '', '', NULL, NULL, NULL, '0',
        NULL, '2023-02-13 17:37:51', 'admin', '2023-02-13 17:37:51');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('4b7445eeff16ba5c83868b392143401e', NULL, '性别', NULL, 'y', 'string', '', 0, '', '', '', NULL, NULL, NULL, '0',
        NULL, '2023-02-13 17:19:05', 'admin', '2023-02-13 17:19:05');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('4cfe4a565f7045142b927f2ef95d9ac6', '511dd862eb7b37141082a0b5802919ba', '文本（靠右）', NULL, 'N', 'VARCHAR', '',
        0, '10', '', '', '超算力即时分析平台实时', '超算力即时分析平台实时', 'left', '0', NULL, '2023-08-15 15:47:13',
        'admin', '2023-08-15 15:47:13');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('4f0249520a8051b9ee817e0d4d84e4a4', '7c8a84e530fcd75b65c80f64f6bd3bf9', '客户类型', NULL, 'Y', 'INTEGER', '', 0,
        '', '', '', NULL, NULL, NULL, '0', NULL, '2023-03-13 14:43:42', 'admin', '2023-03-13 14:43:42');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('84a2b30b397f0075291160adf1510cdb', '7c8a84e530fcd75b65c80f64f6bd3bf9', '测试', NULL, 'N', 'VARCHAR', '', 0,
        '21', '', '', '超算力即时分析平台实时', '超算力即时分析平台实时', 'left', '0', NULL, '2023-04-23 10:41:17',
        'admin', '2023-07-24 15:18:00');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('8720f30bb4eb1714fd6c61267a918110', '7c8a84e530fcd75b65c80f64f6bd3bf9', '是否', NULL, 'Y', 'DOUBLE', '', 0, '',
        '', '', NULL, NULL, NULL, '0', NULL, '2023-03-13 14:42:55', 'admin', '2023-03-13 14:42:55');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('893f64873d0e66042738a31a6fad2e09', 'c1bd00a05fe8552a98651c299fb75afb', '百分比', NULL, 'N', 'DOUBLE',
        'value*100', 2, '21', '', '%', '100000000.0123', '100000000.01', 'middle', '0', NULL, '2023-06-04 19:18:57',
        'admin', '2023-08-19 20:20:57');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('8e0d06c87cd5fb92782bdca14a15f08d', '0447ac465927294ee153ae4ee05f175b', '性别', NULL, 'Y', 'VARCHAR', '', 0,
        '10', '', '', '超算力即时分析平台实时', '超算力即时分析平台实时', 'middle', '0', NULL, '2023-08-14 09:45:58',
        'admin', '2023-08-14 09:45:58');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('989721432a2af1ae14173d2d50cdafc8', 'c1bd00a05fe8552a98651c299fb75afb', '四位小数', NULL, 'N', 'DOUBLE', '', 4,
        '20', '', '', '100000000.0123', '100,000,000.0123', 'right', '0', NULL, '2023-07-26 16:34:16', 'admin',
        '2023-08-18 23:44:10');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('9946e9e6519e204688f823bdcac1e2b4', 'c1bd00a05fe8552a98651c299fb75afb', '百分比（右）', NULL, 'N', 'DOUBLE',
        'value*100', 4, '21', '', '%', '100000000.0123', '100000000.0123', 'right', '0', NULL, '2023-06-04 19:53:14',
        'admin', '2023-08-19 20:20:45');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('ab8019d388f8aac49c5d636ebf7a3514', 'c1bd00a05fe8552a98651c299fb75afb', '两位小数', NULL, 'N', 'DOUBLE', '', 2,
        '20', '', '', '100000000.0123', '100,000,000.01', 'right', '0', NULL, '2023-06-01 10:55:52', 'admin',
        '2023-08-06 15:05:04');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('b7e1154c90ad2c44d9ad5494069e243d', '0447ac465927294ee153ae4ee05f175b', '区域', NULL, 'Y', 'VARCHAR', '', 0,
        '10', '', '', '超算力即时分析平台实时', '超算力即时分析平台实时', 'right', '0', NULL, '2023-07-30 17:51:56',
        'admin', '2023-07-30 17:51:56');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('bb668a473cfa8ad5b69b1cd8d55346d8', '86d1a4e4b912dfdbd3ce439a92db9438', '金额（元）', NULL, 'N', 'DOUBLE', '', 2,
        '20', '', '元', '100000000.0123', '100,000,000.01', 'right', '0', NULL, '2023-06-29 09:46:15', 'admin',
        '2023-09-02 10:24:17');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('bf55b3f9f83ec908c691e3f9f16dd923', 'e91e81af6043f8809749fae553c76f2a', '日期（中文）', NULL, 'N', 'DATE', '5', 0,
        '10', '67', '7', '1900/1/1', '1900年1月1日', 'middle', '0', NULL, '2023-07-17 14:25:33', 'admin',
        '2023-07-17 14:25:33');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('c328aeeb64ba2657da4297ecaaa21fb4', '7c8a84e530fcd75b65c80f64f6bd3bf9', '性别', NULL, 'Y', 'DOUBLE', '', 0, '',
        '', '', NULL, NULL, NULL, '0', NULL, '2023-03-13 14:41:20', 'admin', '2023-03-13 14:41:20');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('c8caae7f49e22f91d93a8936e68502bc', '0447ac465927294ee153ae4ee05f175b', '是否', NULL, 'Y', 'VARCHAR', '', 0,
        '10', '', '', '超算力即时分析平台实时', '超算力即时分析平台实时', 'right', '0', NULL, '2023-08-14 09:46:35',
        'admin', '2023-08-14 09:49:03');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('e7c2be5b930c9603e2cc3b43527a8764', '7c8a84e530fcd75b65c80f64f6bd3bf9', '籍贯', NULL, 'Y', 'VARCHAR', '', 0,
        '21', '', '', '超算力即时分析平台实时', '超算力即时分析平台实时', 'left', '0', NULL, '2023-06-01 11:00:10',
        'admin', '2023-07-24 15:17:03');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('e91e81af6043f8809749fae553c76f2a', '1b3f4ace5a71f2d6e85d68eb68e68b53', '日期（中文）', NULL, 'N', 'DATE', '', 0,
        '10', '', '', '1900/1/1', '1900年1月1日', 'middle', '0', NULL, '2023-04-26 07:11:22', 'admin',
        '2023-04-26 07:11:22');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('eac3e2e2d6c04ac878fe1fa3de6382f3', '86d1a4e4b912dfdbd3ce439a92db9438', '金额（万元）', NULL, 'N', 'DOUBLE',
        'value/10000', 2, '20', '￥', '万元', '100000000.0123', '100,000,000.01', 'right', '0', NULL,
        '2023-06-29 09:44:14', 'admin', '2023-07-30 17:38:59');
INSERT INTO "cube_group_instance" ("id", "group_id", "group_instance_name", "group_instance_desc", "dic_flag",
                                   "storage_type", "operational_rule", "decimal_places", "show_format",
                                   "prefix_char", "suffix_char", "raw_sample", "show_sample", "content_align",
                                   "status", "create_by", "create_time", "update_by", "update_time")
VALUES ('f815c763ee04f05e8c8d3bbea477b49d', 'c1bd00a05fe8552a98651c299fb75afb', '整数', NULL, 'N', 'INTEGER', '', 0,
        '21', '', '', '100000000', '100000000', 'left', '0', NULL, '2023-06-01 10:54:19', 'admin',
        '2023-06-01 10:54:19');


-- ----------------------------
-- Records of cube_rule_func
-- ----------------------------

INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (1, '跨表查找-单条件', 'vlookup(,,)',
        'vlookup(【它表目标列】, 【它表条件列1】, 【本表条件列1】);说明：单条件从其它表中查询数据。', 5, 1, 'vlookup', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (2, '线性插值', 'interp(,,)',
        'interp(【本表期限列】, 【它表期限列】, 【它表数值列】)；说明：用做银行FTP场景，未匹配到相关期限值的用线性插值法计算。',
        2, 1, 'interp', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (3, '分摊', 'alloca(,,,)',
        'alloca(【本表匹配列】, 【它表匹配列】, 【它表待摊数据列】, 【本表分摊动因列】)；说明：用【本表匹配列】和【它表匹配列】进行匹配，找到目标表中唯一的一条数据后，取【它表待摊数据列】数据，摊给本表中所有【本表匹配列】值相同的数据，如果有第四个参数【本表分摊动因列】，则按照动因列作为分摊动因来分摊，如果没有第四个参数，则符合条件的均摊。',
        2, 2, 'alloca', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (4, '汇总', 'vsum(,,,,)',
        'vsum(【它表计算列】，【它表条件列1】, 【本表条件列1】, 【它表条件列2】, 【本表条件列2】…)；说明：将它表符合条件的【它表计算列】值分别汇总。',
        1, 1, 'vsum', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (5, '绝对值', 'abs()', 'abs (【参数1】)；说明：返回【参数1】的绝对值。', 3, 1, 'abs', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (6, '指数', 'power(,)', 'power (【参数1】，【参数2】)；说明：返回以【参数1】为底，【参数2】次幂的值。', 3, 2, 'power', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (7, '字符串比较', 'exact(,)',
        'exact (【字符串1】,【字符串2】)；说明：比较 【字符串1】和【字符串2】是否相等，相等则返回true，不相等则返回false。', 6, 2,
        'exact', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (8, '平均', 'vavg(,,)',
        'vavg(【它表计算列】，【它表条件列1】, 【本表条件列1】, 【它表条件列2】, 【本表条件列2】…)；说明：将它表符合条件的【它表计算列】值分别求平均。',
        1, 2, 'vavg', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (13, '当日', 'today()', 'today()；说明：返回服务器当前的日期。', 4, 1, 'today', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (14, '字符串转日期', 'strToDate(,)',
        'strToDate(【参数1：日期字符串】,【参数2：格式字符串】)；说明：将日期字符串按照格式字符串样式转换为日期。', 4, 2,
        'strToDate', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (15, '日期转字符串', 'dateToStr(,)',
        'dateToStr(【参数1：日期】,【参数2：格式字符串】)；说明：将日期按照格式字符串样式转换为字符串。', 4, 3, 'dateToStr',
        '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (16, '日周初', 'monday()', 'monday(【参数1】)；说明：返回【参数1】所在这周的周一对应的日期。', 4, 4, 'monday', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (17, '日月初', 'beginMonth()', 'beginMonth(【参数1】)；说明：返回【参数1】所在这月月初对应的日期。', 4, 5, 'beginMonth',
        '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (18, '日季初', 'beginQuarter()', 'beginQuarter(【参数1】)；说明：返回【参数1】所在这季季初对应的日期。', 4, 6,
        'beginQuarter', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (19, '日年初', 'beginYear()', 'beginYear(【参数1】)；说明：返回【参数1】所在这年年初对应的日期。', 4, 7, 'beginYear',
        '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (20, '日加日', 'dateAddDate(,)', 'dateAddDate(【参数1】, 【参数2】)；说明：返回【参数1】加上整数【参数2】天数对应的日期。',
        4, 8, 'dateAddDate', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (21, '日加周', 'dateAddWeek(,)', 'dateAddWeek(【参数1】, 【参数2】)；说明：返回【参数1】加上整数【参数2】周数对应的日期。',
        4, 9, 'dateAddWeek', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (22, '日加月', 'dateAddMonth(,)',
        'dateAddMonth(【参数1】, 【参数2】)；说明：返回【参数1】加上整数【参数2】月数对应的日期。', 4, 10, 'dateAddMonth', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (23, '日加季', 'dateAddQuarter(,)',
        'dateAddQuarter(【参数1】, 【参数2】)；说明：返回【参数1】加上整数【参数2】季度数对应的日期。', 4, 11, 'dateAddQuarter',
        '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (24, '日加年', 'dateAddYear(,)', 'dateAddYear(【参数1】, 【参数2】)；说明：返回【参数1】加上整数【参数2】年数对应的日期。',
        4, 12, 'dateAddYear', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (25, '自然常数指数', 'exp()', 'exp(【参数1】)；说明：返回以自然常数e为底【参数1】次幂的值。', 3, 7, 'exp', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (26, '自然对数', 'ln()', 'ln(【参数1】)；说明：返回【参数1】的自然对数。', 3, 8, 'ln', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (27, '字符串截取', 'MID(,,)',
        'MID(【待截取字符串列】,【截取起始位置】,【截图长度】)；说明：在【待截取字符串】中，从【截取起始位置】截取长度为【截图长度】的一个字符串，原型函数是excel中的MID函数。',
        6, 3, 'MID', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (28, '字符串查找', 'FIND(,)',
        'FIND(【查找字符串】,【查找目标字符串】)；说明：在【查找目标字符串】中从左向右查找【查找字符串】，找到后返回第一个字符的位置，原型函数式excel中的FIND函数。',
        6, 4, 'FIND', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (29, '标准正态累积分布', 'NORMSDIST()', 'NORMSDIST (【参数1】)；说明：返回【参数1】标准正态累积分布函数值。', 3, 9,
        'NORMSDIST', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (30, '标准正态累积分布反函数', 'NORMSINV()', 'NORMSINV (【参数1】)；说明：返回【参数1】标准正态累积分布区间点。', 3, 10,
        'NORMSINV', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (31, '计数', 'vcount()',
        'vcount(【它表计算列】，【它表条件列1】, 【本表条件列1】, 【它表条件列2】, 【本表条件列2】…)；说明：分别统计它表中符合条件记录的行数。',
        1, 3, 'vcount', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (32, '最大值', 'vmax()',
        'vmax(【它表计算列】，【它表条件列1】, 【本表条件列1】, 【它表条件列2】, 【本表条件列2】…)；说明：将它表符合条件的【它表计算列】分别求最大值。',
        1, 4, 'vmax', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (33, '最小值', 'vmin()',
        'vmin(【它表计算列】，【它表条件列1】, 【本表条件列1】, 【它表条件列2】, 【本表条件列2】…)；说明：将它表符合条件的【它表计算列】分别求最小值。',
        1, 5, 'vmin', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (34, '加权平均', 'vavgw()',
        'vavgw(【它表求平均列】, 【它表权重列】, 【它表条件列1】, 【本表条件列1】, 【它表条件列2】, 【本表条件列2】…)；说明：将它表符合条件的【它表计算列】值分别按照权重列加权平均。',
        1, 6, 'vavgw', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (35, '空值赋值', 'nvl()',
        'nvl(【参数1】)；当参数为空值时，返回0；当参数不为空值时，返回参数值，参数必须为数值或整数型。', 5, 2, 'nvl', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (37, '日期相减', 'dateSubDate(,)', 'dateSubDate(【参数1】,【参数2】)；说明：返回【参数1】和【参数2】之间相差的天数。', 4,
        13, 'dateSubDate', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (38, '字符串转数字', 'strToNum()',
        'strToNum(参数1)；说明：把字符串中存储的数字，转换成数值型。如果字符串中有非数字的字符，则返回0。', 6, 5, 'strToNum',
        '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (39, '子表计数', 'ccount()', 'ccount(【子表计算列】)；说明：返回子记录数。', 8, 3, NULL, '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (40, '子表最大值', 'cmax()', 'cmax(【子表计算列】)；说明：返回子记录【子表计算列】的最大值。', 8, 4, NULL, '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (41, '子表最小值', 'cmin()', 'cmin(【子表计算列】)；说明：返回子记录【子表计算列】的最小值。', 8, 5, NULL, '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (42, '子表加权平均', 'cavgw(,)',
        'cavgw(【子表计算列】， 【子表加权列】)；说明：返回子记录【子表计算列】按照【子表加权列】加权后的平均值。', 8, 6, NULL,
        '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (43, '子表平均', 'cavg()', 'cavg(【子表计算列】)；说明：返回子记录【子表计算列】的平均值。', 8, 2, NULL, '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (44, '子表汇总', 'csum()', 'csum(【子表计算列】)；说明：返回子记录【子表计算列】的汇总值。', 8, 1, NULL, '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (45, '子表RWA', 'cRWA(,,,)',
        'cRWA(【本表计算列-贷款金额】,【本表计算列-风险权重】,【子表计算列-押品金额】,【子表计算列-押品权重】)；说明：适用于银行RWA（风险加权资产）计量。',
        8, 7, NULL, '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (46, '机构上级机构', 'orgHigherOrg(,)',
        'orgHigherOrg(【参数1】，【参数2】)；说明：返回【参数1】这个机构的上级机构。【参数2】为整数，表示跨越级别。', 5, 3, NULL,
        '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (47, '用户的上级机构', 'userHigherOrg(,)',
        'userHigherOrg(【参数1】，【参数2】)；说明：返回【参数1】这个机构的上级机构。【参数2】为整数，表示跨越级别。', 5, 4, NULL,
        '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (48, '跨表查找-多条件', 'vlookups(,,)',
        'vlookups(【它表目标列】, 【它表条件列1】, 【本表条件列1】, 【它表条件列2】, 【本表条件列2】…)；说明：多条件从其它表中查询数据。',
        5, 3, 'vlookups', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (49, '累乘', 'product()',
        'product (【参数1】)；说明：返回“当前行以及首列索引id比自己小的所有行【参数1】列值相乘”的结果。', 3, 11, 'product',
        '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (50, '日期相减（月）', 'dateSubDateM(,)',
        'dateSubDateM(【参数1】, 【参数2】)；说明：返回【参数1】和【参数2】之间相差的月数。', 4, 14, 'dateSubDateM', '1');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (51, '强转整数', 'toInt()', 'toInt(【参数1】)；说明：将【参数1】强制转为整数。', 5, 4, 'toInt', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (52, '多列最大值', 'mmax(,)', 'mmax(【参数1】,【参数2】,【参数3】...);说明：返回多个参数中的最大值', 3, 12, 'mmax',
        '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (53, '多列最小值', 'mmin(,)', 'mmin(【参数1】,【参数2】,【参数3】...);说明：返回多个参数中的最小值', 3, 13, 'mmin',
        '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (54, '多列平均值', 'mavg(,)', 'mavg(【参数1】,【参数2】,【参数3】...);说明：返回多个参数的平均值', 3, 14, 'mavg',
        '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (55, '多列靠左优先', 'mleftFirst(,)',
        'mleftFirst(【参数1】,【参数2】,【参数3】...);说明：多个参数从左至右排先后优先级，如果有多个非空值，返回优先级最高的非空值',
        3, 15, 'mleftFirst', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (56, '日月末', 'endMonth()', 'endMonth(【参数1】)；说明：返回【参数1】所在这月月末对应的日期。', 4, 15, 'endMonth',
        '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (57, '多列日期最大值', 'mmaxDate(,)', 'mmaxDate(【日期1】,【日期2】,【日期3】...);说明：返回多个日期中的最大值', 4, 15,
        'mmaxDate', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (58, '多列日期最小值', 'mminDate(,)', 'mminDate(【日期1】,【日期2】,【日期3】...);说明：返回多个日期中的最小值', 4, 15,
        'mminDate', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (59, '年月相减', 'monthSubMonth(,)',
        'monthSubMonth(【参数1】, 【参数2】)；说明：返回【参数1】和【参数2】两个月份之间相差的月数。', 4, 16, 'monthSubMonth',
        '1,2');

INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (60, '日期相减（精确）', 'dateSubDateEX(,)',
        'dateSubDateEX(【参数1】,【参数2】)；说明：返回【参数1】和【参数2】之间相差的天数，精确到秒。', 4, 17, 'dateSubDateEX',
        '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (61, '判断日期是否季度末', 'isQuarterEnd()', 'isQuarterEnd(【参数1】)；说明：判断【参数1】是否季度末。', 4, 18,
        'isQuarterEnd', '1,2');
INSERT INTO "cube_rule_func" ("id", "func_name", "func_val", "func_desc", "func_group", "func_seq", "alias",
                              "func_scope")
VALUES (62, '获取某天的开始时间', 'beginOfDay()', 'beginOfDay(【参数1】)；说明：获取【参数1】的开始时间00:00:00。', 4, 19,
        'beginOfDay', '1,2');

select setval(pg_get_serial_sequence('cube_rule_func', 'id'), 63, false);

-- ----------------------------
-- Records of cube_rule_func_group
-- ----------------------------

INSERT INTO "cube_rule_func_group" ("id", "group_name", "group_seq")
VALUES (1, '聚合', 1);
INSERT INTO "cube_rule_func_group" ("id", "group_name", "group_seq")
VALUES (2, '管会', 2);
INSERT INTO "cube_rule_func_group" ("id", "group_name", "group_seq")
VALUES (3, '数学', 3);
INSERT INTO "cube_rule_func_group" ("id", "group_name", "group_seq")
VALUES (4, '日期', 4);
INSERT INTO "cube_rule_func_group" ("id", "group_name", "group_seq")
VALUES (5, '其它', 5);
INSERT INTO "cube_rule_func_group" ("id", "group_name", "group_seq")
VALUES (6, '字符', 6);
INSERT INTO "cube_rule_func_group" ("id", "group_name", "group_seq")
VALUES (7, '统计', 7);
INSERT INTO "cube_rule_func_group" ("id", "group_name", "group_seq")
VALUES (8, '子表', 8);


-- ----------------------------
-- Records of sys_config
-- ----------------------------

INSERT INTO "sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "create_by",
                          "create_time", "update_by", "update_time", "remark")
VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2023-01-14 15:25:31', '',
        NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO "sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "create_by",
                          "create_time", "update_by", "update_time", "remark")
VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2023-01-14 15:25:31', '', NULL,
        '初始化密码 123456');
INSERT INTO "sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "create_by",
                          "create_time", "update_by", "update_time", "remark")
VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2023-01-14 15:25:31', '', NULL,
        '深色主题theme-dark，浅色主题theme-light');
INSERT INTO "sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "create_by",
                          "create_time", "update_by", "update_time", "remark")
VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'false', 'Y', 'admin', '2023-01-14 15:25:31', '', NULL,
        '是否开启验证码功能（true开启，false关闭）');
INSERT INTO "sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "create_by",
                          "create_time", "update_by", "update_time", "remark")
VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2023-01-14 15:25:31',
        '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO "sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "create_by",
                          "create_time", "update_by", "update_time", "remark")
VALUES (6, '规则引擎-执行规则线程数', 'sys.rule.execute.threadSize', '8', 'Y', 'admin', '2023-08-07 17:14:14', '', NULL,
        '规则引擎-执行规则线程数');
INSERT INTO "sys_config" ("config_id", "config_name", "config_key", "config_value", "config_type", "create_by",
                          "create_time", "update_by", "update_time", "remark")
VALUES (7, '规则引擎-快捷输入-if表达式', 'sys.rule.express.if', 'if()
{
return
}
else if()
{
return
}
else
{
return
}', 'Y', 'admin', '2023-08-22 14:32:39', '', NULL, 'sys.rule.express.if');


select setval(pg_get_serial_sequence('sys_config', 'config_id'), 8, false);

-- ----------------------------
-- Records of sys_dept
-- ----------------------------

INSERT INTO "sys_dept" ("dept_id", "parent_id", "ancestors", "dept_name", "order_num", "leader", "phone",
                        "email", "status", "del_flag", "create_by", "create_time", "update_by", "update_time",
                        "dept_type", "dept_level")
VALUES (100, 0, '0', '总行', 0, '总行', '15888888888', '<EMAIL>', '0', '0', 'admin', '2023-01-14 15:25:31',
        'admin', '2023-09-02 08:55:19', 'back', '0');


select setval(pg_get_serial_sequence('sys_dept', 'dept_id'), 101, false);

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------

INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '性别男');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '性别女');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '性别未知');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '显示菜单');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '隐藏菜单');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '正常状态');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '停用状态');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '正常状态');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '停用状态');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '默认分组');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '系统分组');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '系统默认是');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '系统默认否');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '通知');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '公告');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '正常状态');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '关闭状态');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '其他操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '新增操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '修改操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '删除操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '授权操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '导出操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '导入操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '强退操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '生成操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '清空操作');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '正常状态');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '停用状态');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (100, 0, '字符串', 'VARCHAR', 'biz_storage_type', NULL, 'info', 'N', '0', 'admin', '2023-01-14 15:25:31', '',
        NULL, NULL);
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (101, 1, '数值', 'DOUBLE', 'biz_storage_type', NULL, 'info', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        NULL);
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (102, 2, '整型', 'INTEGER', 'biz_storage_type', NULL, 'info', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        NULL);
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (103, 3, '日期', 'DATE', 'biz_storage_type', NULL, 'info', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        NULL);
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (104, 1, '自动', 'AUTO', 'biz_create_mode', NULL, 'info', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '自动');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (105, 2, '手动', 'MANUAL', 'biz_create_mode', NULL, 'info', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        '手动');
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (106, 1, '一般维度', 'COMMON', 'dim_type', NULL, 'info', 'Y', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        NULL);
INSERT INTO "sys_dict_data" ("dict_code", "dict_sort", "dict_label", "dict_value", "dict_type", "css_class",
                             "list_class", "is_default", "status", "create_by", "create_time", "update_by",
                             "update_time", "remark")
VALUES (107, 2, '主数据维度', 'MASTER', 'dim_type', NULL, 'info', 'N', '0', 'admin', '2023-01-14 15:25:31', '', NULL,
        NULL);


select setval(pg_get_serial_sequence('sys_dict_data', 'dict_code'), 108, false);

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------

INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '用户性别列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '菜单状态列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '系统开关列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '任务状态列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '任务分组列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '系统是否列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '通知类型列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '通知状态列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '操作类型列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '登录状态列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (11, '存储类型', 'biz_storage_type', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '存储类型列表');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (12, '建表方式', 'biz_create_mode', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '建表方式');
INSERT INTO "sys_dict_type" ("dict_id", "dict_name", "dict_type", "status", "create_by", "create_time",
                             "update_by", "update_time", "remark")
VALUES (13, '维度实例类型', 'dim_type', '0', 'admin', '2023-01-14 15:25:31', '', NULL, '维度实例类型');


select setval(pg_get_serial_sequence('sys_dict_type', 'dict_id'), 14, false);

-- ----------------------------
-- Records of sys_menu
-- ----------------------------

INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (1, '首页', 0, 1, 'dashboard', NULL, '', 1, 0, 'M', '0', '0', '', 'vxe-icon-home', 'admin',
        '2023-01-14 15:25:31', '', NULL, '首页');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2, '超算力表格', 0, 2, 'base-table', NULL, '', 1, 0, 'M', '0', '0', '', 'vxe-icon-table', 'admin',
        '2023-01-14 15:25:31', '', NULL, '二维数据表');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (3, '超算力数方', 0, 3, 'data-cube', NULL, '', 1, 0, 'M', '0', '0', '', 'vxe-icon-custom-column', 'admin',
        '2023-01-14 15:25:31', '', NULL, '多维数据集');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (4, '超算力图表', 0, 4, 'data-visual', NULL, '', 0, 0, 'M', '0', '0', '', 'vxe-icon-chart-bar-y', 'admin',
        '2023-01-14 15:25:31', '', NULL, '数据可视化');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (5, '审批流程', 0, 5, 'approve-process', NULL, '', 0, 0, 'M', '0', '0', '', 'vxe-icon-merge-cells', 'admin',
        '2023-01-14 15:25:31', '', NULL, '审批流程');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (6, '系统设置', 0, 6, 'system', NULL, '', 0, 0, 'M', '0', '0', '', 'vxe-icon-setting', 'admin',
        '2023-01-14 15:25:31', '', NULL, '系统设置');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (100, '机构设置', 6, 1, 'department', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'vxe-icon-flow-branch',
        'admin', '2023-01-14 15:25:31', '', NULL, '机构设置');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (101, '人员设置', 6, 2, 'user', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'vxe-icon-user-fill', 'admin',
        '2023-01-14 15:25:31', '', NULL, '人员设置');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (102, '功能角色', 6, 3, 'func-role', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'vxe-icon-envelope',
        'admin', '2023-01-14 15:25:31', '', NULL, '功能角色');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (103, '审批角色', 6, 4, 'approve-role', '', '', 1, 0, 'C', '0', '0', 'system:dept:list',
        'vxe-icon-envelope-open', 'admin', '2023-01-14 15:25:31', '', NULL, '审批角色');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (104, '数据格式', 6, 5, 'data-format', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'vxe-icon-maximize',
        'admin', '2023-01-14 15:25:31', '', NULL, '数据格式');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (105, '数据视图', 6, 6, 'data-view', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'vxe-icon-platform',
        'admin', '2023-01-14 15:25:31', '', NULL, '数据视图');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2002, '首页', 1, 1, 'dashboard', NULL, '', 1, 0, 'M', '0', '0', '', 'vxe-icon-home', 'admin',
        '2023-01-14 15:25:31', '', NULL, '首页');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2003, '超算力表格', 2, 1, '', NULL, '', 1, 0, 'M', '0', '0', '', 'vxe-icon-table', 'admin',
        '2023-01-14 15:25:31', '', NULL, '二维数据表');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2004, '超算力数方', 3, 1, '', NULL, '', 1, 0, 'M', '0', '0', '', 'vxe-icon-custom-column', 'admin',
        '2023-01-14 15:25:31', '', NULL, '多维数据集');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2005, '超算力图表', 4, 1, 'data-visual', NULL, '', 1, 0, 'M', '0', '0', '', 'vxe-icon-chart-bar-y', 'admin',
        '2023-01-14 15:25:31', '', NULL, '数据可视化');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2006, '审批流程', 5, 1, '', NULL, '', 1, 0, 'M', '0', '0', '', 'vxe-icon-merge-cells', 'admin',
        '2023-01-14 15:25:31', '', NULL, '审批流程');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2007, '维度设置', 6, 7, 'dimension-set', '', '', 1, 0, 'C', '0', '0', 'system:dict:list',
        'vxe-icon-chart-radar', 'admin', '2023-01-14 15:25:31', '', NULL, '维度设置');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2008, '事件进程', 6, 8, 'event-list', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'vxe-icon-chart-radar',
        'admin', '2023-01-14 15:25:31', '', NULL, '事件进程');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2009, '数据源', 6, 7, 'data-origin', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'vxe-icon-platform',
        'admin', '2023-01-14 15:25:31', '', NULL, '数据源');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2010, '内存管理', 6, 8, 'storage-manage', '', '', 1, 0, 'C', '0', '0', '', 'vxe-icon-chart-radar', 'admin',
        '2023-01-14 15:25:31', '', NULL, '内存管理');
INSERT INTO "sys_menu" ("menu_id", "menu_name", "parent_id", "order_num", "path", "component", "query",
                        "is_frame", "is_cache", "menu_type", "visible", "status", "perms", "icon", "create_by",
                        "create_time", "update_by", "update_time", "remark")
VALUES (2011, '大模型设置', 6, 9, 'model-setting', '', '', 1, 0, 'C', '0', '0', 'system:model:list',
        'vxe-icon-chart-radar', 'admin', '2023-01-14 15:25:31', '', NULL, '大模型设置');


select setval(pg_get_serial_sequence('sys_menu', 'menu_id'), 2012, false);

-- ----------------------------
-- Records of sys_post
-- ----------------------------

INSERT INTO "sys_post" ("post_id", "post_code", "post_name", "post_sort", "status", "create_by", "create_time",
                        "update_by", "update_time", "remark")
VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2023-01-14 15:25:31', '', NULL, '');
INSERT INTO "sys_post" ("post_id", "post_code", "post_name", "post_sort", "status", "create_by", "create_time",
                        "update_by", "update_time", "remark")
VALUES (2, 'user', '普通员工', 4, '0', 'admin', '2023-01-14 15:25:31', '', NULL, '');


select setval(pg_get_serial_sequence('sys_post', 'post_id'), 3, false);

-- ----------------------------
-- Records of sys_role
-- ----------------------------

INSERT INTO "sys_role" ("role_id", "role_name", "role_key", "role_sort", "data_scope", "menu_check_strictly",
                        "dept_check_strictly", "status", "del_flag", "create_by", "create_time", "update_by",
                        "update_time", "remark", "role_type")
VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2023-01-14 15:25:31', 'admin',
        '2023-11-30 13:06:22', '超级管理员', '0');
INSERT INTO "sys_role" ("role_id", "role_name", "role_key", "role_sort", "data_scope", "menu_check_strictly",
                        "dept_check_strictly", "status", "del_flag", "create_by", "create_time", "update_by",
                        "update_time", "remark", "role_type")
VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2023-01-14 15:25:31', 'admin', '2023-11-30 13:42:07',
        '普通角色', '0');


select setval(pg_get_serial_sequence('sys_role', 'role_id'), 3, false);

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

INSERT INTO "sys_role_dept" ("role_id", "dept_id")
VALUES (2, 100);


-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------

INSERT INTO "sys_role_menu" ("role_id", "menu_id")
VALUES (2, 1);
INSERT INTO "sys_role_menu" ("role_id", "menu_id")
VALUES (2, 2);
INSERT INTO "sys_role_menu" ("role_id", "menu_id")
VALUES (2, 3);
INSERT INTO "sys_role_menu" ("role_id", "menu_id")
VALUES (2, 4);
INSERT INTO "sys_role_menu" ("role_id", "menu_id")
VALUES (2, 6);
INSERT INTO "sys_role_menu" ("role_id", "menu_id")
VALUES (2, 104);
INSERT INTO "sys_role_menu" ("role_id", "menu_id")
VALUES (2, 105);
INSERT INTO "sys_role_menu" ("role_id", "menu_id")
VALUES (2, 2007);


-- ----------------------------
-- Records of sys_user
-- ----------------------------

INSERT INTO "sys_user" ("user_id", "dept_id", "user_name", "nick_name", "user_type", "email", "phonenumber",
                        "sex", "avatar", "password", "status", "del_flag", "login_ip", "login_date",
                        "create_by", "create_time", "update_by", "update_time", "remark")
VALUES (1, 1, 'admin', '超级管理员', '00', '<EMAIL>', '15888888888', '0',
        '/profile/avatar/2023/02/10/91d2965181a7654cf50f8a33e77e929_20230210153834A010.png',
        '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2023-11-30 13:41:44',
        'admin', '2023-01-14 15:25:31', 'admin', '2023-11-30 13:41:44', '管理员');
INSERT INTO "sys_user" ("user_id", "dept_id", "user_name", "nick_name", "user_type", "email", "phonenumber",
                        "sex", "avatar", "password", "status", "del_flag", "login_ip", "login_date",
                        "create_by", "create_time", "update_by", "update_time", "remark")
VALUES (2, 100, 'test', '测试', '00', '<EMAIL>', '15666666666', '1',
        '/profile/avatar/2023/02/10/91d2965181a7654cf50f8a33e77e929_20230210153834A010.png',
        '$2a$10$RDRdCQzsbvSODab81QnHku5wICoJ6nCdxf5tsfqwL/D9.QigLPfy2', '0', '0', '127.0.0.1', '2023-11-30 13:42:15',
        'admin', '2023-01-14 15:25:31', 'admin', '2023-11-30 13:42:14', '测试员');


select setval(pg_get_serial_sequence('sys_user', 'user_id'), 3, false);

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------

INSERT INTO "sys_user_post" ("user_id", "post_id")
VALUES (1, 1);
INSERT INTO "sys_user_post" ("user_id", "post_id")
VALUES (2, 2);


-- ----------------------------
-- Records of sys_user_role
-- ----------------------------

INSERT INTO "sys_user_role" ("user_id", "role_id")
VALUES (1, 1);
INSERT INTO "sys_user_role" ("user_id", "role_id")
VALUES (2, 2);
COMMIT;