INSERT INTO cube_model_scene_setting (id, scene_type, input_example, create_by,
                                      create_time, update_by, update_time, del_flag)
VALUES (1919674947842613249, '4',
        '可视化分析图表分析框架\n1、当用户提出要求将JSON数据可视化（柱状图/折线图/饼图）时，AI需生成标准echarts代码块，格式要求：\necharts\n   {\n      ...\n    }\n，整个JSON用反引号包裹，中间严禁插入任何非ECharts规范内容，所有的JSON键必须用双引号包裹，统一使用双引号包裹字符串值。\n2、示例一（柱状图）：\necharts\n{\n        \"title\": { \"text\": \"示例图表\"},\n        \"tooltip\": {},\n        \"xAxis\": { \"data\": [\"A\", \"B\", \"C\", \"D\", \"E\"] },\n        \"yAxis\": {},\n        \"series\": [{ \"name\": \"数据\", \"type\": \"bar\", \"data\": [\"5\", \"20\", \"36\", \"10\", \"15\"] }]\n}\n\n3、示例二（柱状图）：\necharts\n{\n  \"title\": {\n    \"text\": \"未来30天资金流动情况（单位：万元）\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    },\n    \"top\": \"1%\"\n  },\n  \"tooltip\": {\n    \"trigger\": \"axis\",\n    \"axisPointer\": {\n      \"type\": \"shadow\"\n    }\n  },\n  \"legend\": {\n    \"data\": [\"资金流入\", \"资金流出\", \"净流入\"],\n    \"bottom\": \"0%\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    }\n  },\n  \"grid\": {\n    \"left\": \"1%\",\n    \"right\": \"1%\",\n    \"bottom\": \"10%\",\n    \"top\": \"15%\",\n    \"containLabel\": true\n  },\n  \"toolbox\": {\n    \"feature\": {\n      \"saveAsImage\": {}\n    }\n  },\n  \"xAxis\": {\n    \"type\": \"category\",\n    \"data\": [\"2025-05-08\", \"2025-05-10\", \"2025-05-13\", \"2025-05-19\", \"2025-05-23\", \"2025-05-26\", \"2025-05-29\", \"2025-06-01\", \"2025-06-02\", \"2025-06-03\", \"2025-06-04\", \"2025-06-06\"],\n    \"axisLabel\": {\n      \"color\": \"#6C7A8A\",\n      \"rotate\": 45\n    }\n  },\n  \"yAxis\": {\n    \"type\": \"value\",\n    \"axisLabel\": {\n      \"color\": \"#6C7A8A\",\n      \"formatter\": \"{value}\"\n    }\n  },\n  \"series\": [\n    {\n      \"name\": \"资金流入\",\n      \"type\": \"bar\",\n      \"data\": [0, 2560, 0, 0, 0, 48, 0, 2000, 0, 0, 5000, 0],\n      \"itemStyle\": {\n        \"color\": \"#0F7CDE\"\n      }\n    },\n    {\n      \"name\": \"资金流出\",\n      \"type\": \"bar\",\n      \"data\": [9, 500, 1000, 2000, 2500, 500, 100, 200, 500, 500, 500, 168],\n      \"itemStyle\": {\n        \"color\": \"#FD4747\"\n      }\n    },\n    {\n      \"name\": \"净流入\",\n      \"type\": \"bar\",\n      \"data\": [-90, 2060, -1000, -2000, -2500, -452, -100, 1800, -500, -500, 4500, -168],\n      \"itemStyle\": {\n        \"color\": \"#73AD10\"\n      }\n    }\n  ]\n}\n\n4、示例三（柱状图）：\necharts\n{\n  \"title\": {\n    \"text\": \"各支行产品汇总金额（单位：亿元）\",\n    \"top\": \"1%\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    }\n  },\n  \"tooltip\": {\n    \"trigger\": \"axis\",\n    \"axisPointer\": {\n      \"type\": \"shadow\"\n    }\n  },\n  \"legend\": {\n    \"data\": [\"住房按揭贷款\", \"公司定期存款\", \"个人消费贷款\", \"固定资产抵押贷款\", \"流动资金贷款\", \"个人储蓄存款\"],\n    \"bottom\": \"5%\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    }\n  },\n  \"grid\": {\n    \"left\": \"1%\",\n    \"right\": \"1%\",\n    \"bottom\": \"15%\",\n    \"top\": \"15%\",\n    \"containLabel\": true\n  },\n  \"toolbox\": {\n    \"feature\": {\n      \"saveAsImage\": {}\n    }\n  },\n  \"xAxis\": {\n    \"type\": \"category\",\n    \"data\": [\"宝安支行\", \"海淀支行\", \"南山支行\", \"福田支行\", \"浦东支行\", \"朝阳支行\", \"虹桥支行\"],\n    \"axisLabel\": {\n      \"rotate\": 30,\n      \"color\": \"#6C7A8A\"\n    }\n  },\n  \"yAxis\": {\n    \"type\": \"value\",\n    \"axisLabel\": {\n      \"color\": \"#6C7A8A\",\n      \"formatter\": \"{value}\"\n    }\n  },\n  \"series\": [\n    {\n      \"name\": \"住房按揭贷款\",\n      \"type\": \"bar\",\n      \"data\": [0.50, 0.21, 0.35, 0.50, 0.21, 0.28, 0.50],\n      \"itemStyle\": {\n        \"color\": \"#0F7CDE\"\n      }\n    },\n    {\n      \"name\": \"公司定期存款\",\n      \"type\": \"bar\",\n      \"data\": [5.98, 51.56, 35.87, 60.67, 0.18, 0.05, 102.43],\n      \"itemStyle\": {\n        \"color\": \"#73AD10\"\n      }\n    },\n    {\n      \"name\": \"个人消费贷款\",\n      \"type\": \"bar\",\n      \"data\": [0.05, 0.22, 0.34, 0.05, 0.02, 0.27, 0.05],\n      \"itemStyle\": {\n        \"color\": \"#FDA012\"\n      }\n    },\n    {\n      \"name\": \"固定资产抵押贷款\",\n      \"type\": \"bar\",\n      \"data\": [14.20, 7.20, 114.00, 134.50, 5.70, 8.50, 140.00],\n      \"itemStyle\": {\n        \"color\": \"#FD4747\"\n      }\n    },\n    {\n      \"name\": \"流动资金贷款\",\n      \"type\": \"bar\",\n      \"data\": [1.65, 8.20, 1.42, 1.75, 0.60, 0.99, 1.75],\n      \"itemStyle\": {\n        \"color\": \"#17BBDA\"\n      }\n    },\n    {\n      \"name\": \"个人储蓄存款\",\n      \"type\": \"bar\",\n      \"data\": [104.12, 30.98, 96.22, 96.78, 63.51, 97.95, 60.52],\n      \"itemStyle\": {\n        \"color\": \"#D92C6E\"\n      }\n    }\n  ]\n}\n\n5、示例四（折线图）：\necharts\n{\n  \"title\": {\n    \"text\": \"各银行每周客户量\"\n  },\n  \"tooltip\": {\n    \"trigger\": \"axis\"\n  },\n  \"legend\": {\n    \"data\": [\"招商银行\", \"建设银行\", \"浦发银行\", \"光大银行\", \"平安银行\"]\n  },\n  \"grid\": {\n    \"left\": \"3%\",\n    \"right\": \"4%\",\n    \"bottom\": \"3%\",\n    \"containLabel\": true\n  },\n  \"toolbox\": {\n    \"feature\": {\n      \"saveAsImage\": {}\n    }\n  },\n  \"xAxis\": {\n    \"type\": \"category\",\n    \"boundaryGap\": false,\n    \"data\": [\"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\", \"周天\"]\n  },\n  \"yAxis\": {\n    \"type\": \"value\"\n  },\n  \"series\": [\n    {\n      \"name\": \"招商银行\",\n      \"type\": \"line\",\n      \"stack\": \"Total\",\n      \"data\": [120, 132, 101, 134, 90, 230, 210]\n    },\n    {\n      \"name\": \"建设银行\",\n      \"type\": \"line\",\n      \"stack\": \"Total\",\n      \"data\": [220, 182, 191, 234, 290, 330, 310]\n    },\n    {\n      \"name\": \"浦发银行\",\n      \"type\": \"line\",\n      \"stack\": \"Total\",\n      \"data\": [150, 232, 201, 154, 190, 330, 410]\n    },\n    {\n      \"name\": \"光大银行\",\n      \"type\": \"line\",\n      \"stack\": \"Total\",\n      \"data\": [320, 332, 301, 334, 390, 330, 320]\n    },\n    {\n      \"name\": \"平安银行\",\n      \"type\": \"line\",\n      \"stack\": \"Total\",\n      \"data\": [820, 932, 901, 934, 1290, 1330, 1320]\n    }\n  ]\n}\n\n6、示例五（折线图）：\necharts\n{\n  \"title\": {\n    \"text\": \"南山支行各产品汇总金额（单位：亿元）\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    },\n    \"top\": \"1%\"\n  },\n  \"tooltip\": {\n    \"trigger\": \"axis\"\n  },\n  \"legend\": {\n    \"data\": [\"个人消费贷款\", \"固定资产抵押贷款\", \"公司定期存款\", \"住房按揭贷款\", \"流动资金贷款\", \"个人储蓄存款\"],\n    \"bottom\": \"0%\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    }\n  },\n  \"grid\": {\n    \"left\": \"1%\",\n    \"right\": \"1%\",\n    \"bottom\": \"10%\",\n    \"top\": \"15%\",\n    \"containLabel\": true\n  },\n  \"toolbox\": {\n    \"feature\": {\n      \"saveAsImage\": {}\n    }\n  },\n  \"xAxis\": {\n    \"type\": \"category\",\n    \"data\": [\"个人消费贷款\", \"固定资产抵押贷款\", \"公司定期存款\", \"住房按揭贷款\", \"流动资金贷款\", \"个人储蓄存款\"],\n    \"axisLabel\": {\n      \"color\": \"#6C7A8A\",\n      \"rotate\": 45\n    }\n  },\n  \"yAxis\": {\n    \"type\": \"value\",\n    \"axisLabel\": {\n      \"color\": \"#6C7A8A\",\n      \"formatter\": \"{value}\"\n    }\n  },\n  \"series\": [\n    {\n      \"name\": \"南山支行\",\n      \"type\": \"line\",\n      \"data\": [0.03, 11.40, 3.59, 0.35, 1.42, 9.62],\n      \"itemStyle\": {\n        \"color\": \"#0F7CDE\"\n      }\n    }\n  ]\n}\n\n7、示例六（折线图）：\necharts\n{\n  \"title\": {\n    \"text\": \"未来30天资金流动情况（单位：万元）\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    },\n    \"top\": \"1%\"\n  },\n  \"tooltip\": {\n    \"trigger\": \"axis\"\n  },\n  \"legend\": {\n    \"data\": [\"资金流入\", \"资金流出\", \"净流入\"],\n    \"bottom\": \"0%\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    }\n  },\n  \"grid\": {\n    \"left\": \"1%\",\n    \"right\": \"1%\",\n    \"bottom\": \"10%\",\n    \"top\": \"15%\",\n    \"containLabel\": true\n  },\n  \"toolbox\": {\n    \"feature\": {\n      \"saveAsImage\": {}\n    }\n  },\n  \"xAxis\": {\n    \"type\": \"category\",\n    \"data\": [\"2025-05-08\", \"2025-05-10\", \"2025-05-13\", \"2025-05-19\", \"2025-05-23\", \"2025-05-26\", \"2025-05-29\", \"2025-06-01\", \"2025-06-02\", \"2025-06-03\", \"2025-06-04\", \"2025-06-06\"],\n    \"axisLabel\": {\n      \"color\": \"#6C7A8A\",\n      \"rotate\": 45\n    }\n  },\n  \"yAxis\": {\n    \"type\": \"value\",\n    \"axisLabel\": {\n      \"color\": \"#6C7A8A\",\n      \"formatter\": \"{value}\"\n    }\n  },\n  \"series\": [\n    {\n      \"name\": \"资金流入\",\n      \"type\": \"line\",\n      \"data\": [0, 2560, 0, 0, 0, 48, 0, 2000, 0, 0, 5000, 0],\n      \"lineStyle\": {\n        \"type\": \"solid\"\n      },\n      \"itemStyle\": {\n        \"color\": \"#0F7CDE\"\n      }\n    },\n    {\n      \"name\": \"资金流出\",\n      \"type\": \"line\",\n      \"data\": [9, 500, 1000, 2000, 2500, 500, 100, 200, 500, 500, 500, 168],\n      \"lineStyle\": {\n        \"type\": \"solid\"\n      },\n      \"itemStyle\": {\n        \"color\": \"#FD4747\"\n      }\n    },\n    {\n      \"name\": \"净流入\",\n      \"type\": \"line\",\n      \"data\": [-90, 2060, -1000, -2000, -2500, -452, -100, 1800, -500, -500, 4500, -168],\n      \"lineStyle\": {\n        \"type\": \"solid\"\n      },\n      \"itemStyle\": {\n        \"color\": \"#73AD10\"\n      }\n    }\n  ]\n}\n\n8、示例7（饼图）:\necharts\n{\n  \"title\": {\n    \"text\": \"南山支行各产品汇总金额（单位：亿元）\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    },\n    \"top\": \"1%\"\n  },\n  \"tooltip\": {\n    \"trigger\": \"item\",\n    \"formatter\": \"{a} <br/>{b}: {c} ({d}%)\"\n  },\n  \"legend\": {\n    \"orient\": \"horizontal\",\n    \"bottom\": \"0%\",\n    \"data\": [\"个人消费贷款\", \"固定资产抵押贷款\", \"公司定期存款\", \"住房按揭贷款\", \"流动资金贷款\", \"个人储蓄存款\"],\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    }\n  },\n  \"series\": [\n    {\n      \"name\": \"产品金额\",\n      \"type\": \"pie\",\n      \"radius\": [\"40%\", \"70%\"],\n      \"avoidLabelOverlap\": false,\n      \"itemStyle\": {\n        \"borderRadius\": 10,\n        \"borderColor\": \"#fff\",\n        \"borderWidth\": 2\n      },\n      \"label\": {\n        \"show\": false,\n        \"position\": \"center\"\n      },\n      \"emphasis\": {\n        \"label\": {\n          \"show\": true,\n          \"fontSize\": \"18\",\n          \"fontWeight\": \"bold\"\n        }\n      },\n      \"labelLine\": {\n        \"show\": false\n      },\n      \"data\": [\n        {\n          \"value\": 0.34,\n          \"name\": \"个人消费贷款\",\n          \"itemStyle\": {\n            \"color\": \"#0F7CDE\"\n          }\n        },\n        {\n          \"value\": 11.40,\n          \"name\": \"固定资产抵押贷款\",\n          \"itemStyle\": {\n            \"color\": \"#73AD10\"\n          }\n        },\n        {\n          \"value\": 3.59,\n          \"name\": \"公司定期存款\",\n          \"itemStyle\": {\n            \"color\": \"#FDA012\"\n          }\n        },\n        {\n          \"value\": 0.35,\n          \"name\": \"住房按揭贷款\",\n          \"itemStyle\": {\n            \"color\": \"#FD4747\"\n          }\n        },\n        {\n          \"value\": 1.42,\n          \"name\": \"流动资金贷款\",\n          \"itemStyle\": {\n            \"color\": \"#17BBDA\"\n          }\n        },\n        {\n          \"value\": 9.62,\n          \"name\": \"个人储蓄存款\",\n          \"itemStyle\": {\n            \"color\": \"#D92C6E\"\n          }\n        }\n      ]\n    }\n  ]\n}\n\n9、示例八（饼图）：\necharts\n{\n  \"title\": {\n    \"text\": \"宝安支行各产品执行利率分布\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    },\n    \"top\": \"1%\"\n  },\n  \"tooltip\": {\n    \"trigger\": \"item\",\n    \"formatter\": \"{a} <br/>{b}: {c}% ({d}%)\"\n  },\n  \"legend\": {\n    \"orient\": \"horizontal\",\n    \"bottom\": \"0%\",\n    \"data\": [\"住房按揭贷款\", \"公司定期存款\", \"流动资金贷款\", \"固定资产抵押贷款\", \"个人储蓄存款\", \"个人消费贷款\"],\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    }\n  },\n  \"series\": [\n    {\n      \"name\": \"执行利率\",\n      \"type\": \"pie\",\n      \"radius\": [\"40%\", \"70%\"],\n      \"avoidLabelOverlap\": false,\n      \"itemStyle\": {\n        \"borderRadius\": 10,\n        \"borderColor\": \"#fff\",\n        \"borderWidth\": 2\n      },\n      \"label\": {\n        \"show\": false,\n        \"position\": \"center\"\n      },\n      \"emphasis\": {\n        \"label\": {\n          \"show\": true,\n          \"fontSize\": \"18\",\n          \"fontWeight\": \"bold\"\n        }\n      },\n      \"labelLine\": {\n        \"show\": false\n      },\n      \"data\": [\n        {\n          \"value\": 3.6375,\n          \"name\": \"住房按揭贷款\",\n          \"itemStyle\": {\n            \"color\": \"#0F7CDE\"\n          }\n        },\n        {\n          \"value\": 2.0000,\n          \"name\": \"公司定期存款\",\n          \"itemStyle\": {\n            \"color\": \"#73AD10\"\n          }\n        },\n        {\n          \"value\": 4.2889,\n          \"name\": \"流动资金贷款\",\n          \"itemStyle\": {\n            \"color\": \"#FDA012\"\n          }\n        },\n        {\n          \"value\": 5.0269,\n          \"name\": \"固定资产抵押贷款\",\n          \"itemStyle\": {\n            \"color\": \"#FD4747\"\n          }\n        },\n        {\n          \"value\": 2.0000,\n          \"name\": \"个人储蓄存款\",\n          \"itemStyle\": {\n            \"color\": \"#17BBDA\"\n          }\n        },\n        {\n          \"value\": 5.7000,\n          \"name\": \"个人消费贷款\",\n          \"itemStyle\": {\n            \"color\": \"#D92C6E\"\n          }\n        }\n      ]\n    }\n  ]\n}\n\n10、示例九（饼图）：\necharts\n{\n  \"title\": {\n    \"text\": \"南山支行各产品汇总金额（单位：亿元）\",\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    },\n    \"top\": \"1%\"\n  },\n  \"tooltip\": {\n    \"trigger\": \"item\",\n    \"formatter\": \"{a} <br/>{b}: {c} ({d}%)\"\n  },\n  \"legend\": {\n    \"orient\": \"horizontal\",\n    \"bottom\": \"0%\",\n    \"data\": [\"个人消费贷款\", \"固定资产抵押贷款\", \"公司定期存款\", \"住房按揭贷款\", \"流动资金贷款\", \"个人储蓄存款\"],\n    \"textStyle\": {\n      \"color\": \"#6C7A8A\"\n    }\n  },\n  \"series\": [\n    {\n      \"name\": \"产品金额\",\n      \"type\": \"pie\",\n      \"radius\": [\"40%\", \"70%\"],\n      \"avoidLabelOverlap\": false,\n      \"itemStyle\": {\n        \"borderRadius\": 10,\n        \"borderColor\": \"#fff\",\n        \"borderWidth\": 2\n      },\n      \"label\": {\n        \"show\": false,\n        \"position\": \"center\"\n      },\n      \"emphasis\": {\n        \"label\": {\n          \"show\": true,\n          \"fontSize\": \"18\",\n          \"fontWeight\": \"bold\"\n        }\n      },\n      \"labelLine\": {\n        \"show\": false\n      },\n      \"data\": [\n        {\n          \"value\": 0.34,\n          \"name\": \"个人消费贷款\",\n          \"itemStyle\": {\n            \"color\": \"#0F7CDE\"\n          }\n        },\n        {\n          \"value\": 11.40,\n          \"name\": \"固定资产抵押贷款\",\n          \"itemStyle\": {\n            \"color\": \"#73AD10\"\n          }\n        },\n        {\n          \"value\": 3.59,\n          \"name\": \"公司定期存款\",\n          \"itemStyle\": {\n            \"color\": \"#FDA012\"\n          }\n        },\n        {\n          \"value\": 0.35,\n          \"name\": \"住房按揭贷款\",\n          \"itemStyle\": {\n            \"color\": \"#FD4747\"\n          }\n        },\n        {\n          \"value\": 1.42,\n          \"name\": \"流动资金贷款\",\n          \"itemStyle\": {\n            \"color\": \"#17BBDA\"\n          }\n        },\n        {\n          \"value\": 9.62,\n          \"name\": \"个人储蓄存款\",\n          \"itemStyle\": {\n            \"color\": \"#D92C6E\"\n          }\n        }\n      ]\n    }\n  ]\n}\n\n11.当用户没有特别提出要求使用某个图表来展示数据，则不得生成任何ECharts内容，仅使用Markdown表格来展现。\n12. 必须使用标准的option配置格式\n13. 不要包含函数或变量\n14. 使用双引号包裹属性名\n15.柱状图，饼图，折线图必须赋上具体的颜色值\n16.图例色块统一显示在图表的正下方，严格保证图例色块和图表主体（包括x轴维度成员描述部分）不会有信息重叠\n17.当用户需求是“柱状图”时，需要判断x轴维度成员个数a同图例维度成员个数b的乘积值，当a*b大于12时，生成堆叠柱形图，series中数据标签都要默认隐藏掉；但a*b小于12时，生成簇状柱形图，series中数据标签都要默认显示出来。\n18.无论哪个维度，如果维度成员数量大于12时，只把指标值排名前11的显示出来，后面所有的都统一放入其它\n19.显示的所有指标，以值最小的维度成员作为基准判断，小于1万的数字以“元”为单位，大于等于1万小于1亿的数字以“万元”为单位，大于等于1亿的数字以“亿元”为单位，所有单位均保留两位小数，在图表标题中显示出单位\n20.y轴靠左对齐，要保证把y轴上的数字完整展示出来\n21.可视化图表，图例参考色调： #0F7CDE、#73AD10、#FDA012、#FD4747、#17BBDA、#D92C6E，每个图例颜色要明显区分出不同来，需包含柱状图、折线图、饼图、热力图和油量表。要求风格简洁，文字用 #6C7A8A，避免色彩不和谐\n22.要保证图表的题目比图表主体高，避免题目和图表中的信息重叠\n23.整体布局\n整个图表在容器内左对齐，可通过设置 grid 组件的 left 属性来实现，left 值可设置为相对容器宽度的一定比例（如 1%），以确保图表与容器左侧有适当的间距。\n合理设置 grid 的 right、bottom 和 top 属性，为图例和坐标轴文字留出足够的空间。一般来说，right 可设置为 1%，bottom 可根据 x 轴文字的长度和数量设置为 5%，top 可设置为 5%。同时，将 containLabel 属性设置为 true，确保 grid 区域包含坐标轴标签。\n24.图例配置\n图例应放置在合适的位置，避免与 x 轴说明文字重叠。可以将图例放置在图表底部，通过设置 legend.bottom 属性为相对容器高度的一定比例（如 0%）。\n确保图例文字样式清晰，可设置 legend.textStyle 的 color、fontSize 等属性，以提高可读性。\n25.x 轴配置\n当 x 轴说明文字较长或数量较多时，为避免与图例重叠，可设置 xAxis.axisLabel.rotate 属性，将 x 轴文字旋转一定角度（如 30 - 60 度）。\n合理设置 xAxis.axisLabel.interval 属性，控制 x 轴文字的显示间隔，避免文字过于密集。\n26.y 轴配置\n确保 y 轴说明文字完整显示，可根据文字长度调整 grid.left 的值，为 y 轴文字留出足够的空间。\n设置 yAxis.axisLabel 的样式，如 color、fontSize 等，以提高可读性\n27.series中数据标签都要默认隐藏掉，当特别说明显示数据标签时可显示，lineStyle.type 默认设置为 \'solid\' \n28.title的top设置为1%\n29.所有图表，请保证图例和坐标，以及图例之间的对比关系数字完全正确\n30.图表的toolbox有且仅有保存为图片的功能\n31.如果要做雷达图，坐标维度的极限值默认为这个维度在各个图例维度的最大值，radius设置为65%，中心垂直位置设置为45%',
        'admin', '2025-05-06 16:45:48', 'admin', '2025-05-08 12:21:33', '0');