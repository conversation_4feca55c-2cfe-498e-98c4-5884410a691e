package com.dcube.quartz.constants.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 执行状态
 */
@Getter
@AllArgsConstructor
public enum JobExecuteStatusEnums {
    /**
     * 执行中
     */
    EXECUTING("执行中"),
    /**
     * 待执行
     */
    TO_EXECUTE("待执行"),
    ;

    @JsonValue
    private final String name;

}
