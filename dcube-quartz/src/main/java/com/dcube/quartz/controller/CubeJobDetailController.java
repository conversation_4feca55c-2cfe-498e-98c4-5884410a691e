package com.dcube.quartz.controller;

import cn.hutool.core.bean.BeanUtil;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.dto.ReportDto;
import com.dcube.common.enums.BusinessType;
import com.dcube.quartz.domain.CubeJobDetail;
import com.dcube.quartz.dto.CubeJobDetailDto;
import com.dcube.quartz.service.ICubeJobDetailService;
import com.dcube.quartz.util.TaskUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 事件进程详情Controller
 *
 * @date 2023-10-16
 */
@RestController
@RequestMapping("/jobDetail")
@Tag(name = "DCUBE-事件进程详情", description = "DCUBE-事件进程详情")
@RequiredArgsConstructor
public class CubeJobDetailController extends BaseController {
    private final ICubeJobDetailService sysJobDetailService;

    /**
     * 查询事件进程详情列表
     */
    @GetMapping("/list")
//    @Operation(summary = "查询定时任务列表", description = "查询定时任务列表")
    public TableDataInfo list(CubeJobDetail cubeJobDetail) {
        startPage();
        List<CubeJobDetail> list = sysJobDetailService.selectCubeJobDetailList(cubeJobDetail);
        return getDataTable(list);
    }

    /**
     * 获取事件进程详情详细信息
     */
    @GetMapping(value = "/getByJobId")
    @Operation(summary = "根据事件id获取事件进程详情详细信息", description = "获取事件进程详情详细信息")
    public AjaxResult getByJobId(@Parameter(description = "事件id") @RequestParam("jobId") Integer jobId) {
        CubeJobDetail cubeJobDetail = new CubeJobDetail();
        cubeJobDetail.setJobId(jobId);
        List<CubeJobDetail> jobDetails = sysJobDetailService.selectCubeJobDetailList(cubeJobDetail);
        if (CollectionUtils.isNotEmpty(jobDetails)) {
            List<ReportDto> jobTableCache = TaskUtil.getJobTableTask(jobId);
            if (CollectionUtils.isNotEmpty(jobTableCache)) {
                for (int i = 0; i < jobDetails.size(); i++) {
                    if (i >= jobTableCache.size()) {
                        break;
                    }
                    ReportDto reportDto = jobTableCache.get(i);
                    if (reportDto == null) {
                        continue;
                    }
                    CubeJobDetail jobDetail = jobDetails.get(i);
                    jobDetail.setProgress(reportDto.getProgress());
                }
            }
        }
        return success(jobDetails);
    }

    /**
     * 获取事件进程详情详细信息
     */
    @GetMapping(value = "/{id}")
//    @Operation(summary = "获取事件进程详情详细信息", description = "获取事件进程详情详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(sysJobDetailService.selectCubeJobDetailById(id));
    }

    /**
     * 新增事件进程详情
     */
    @Log(title = "新增事件进程详情", businessType = BusinessType.INSERT)
    @PostMapping
    @Operation(summary = "新增事件进程详情", description = "新增事件进程详情")
    public AjaxResult add(@RequestBody CubeJobDetailDto cubeJobDetailDto) {
        CubeJobDetail jobDetail = new CubeJobDetail();
        BeanUtil.copyProperties(cubeJobDetailDto, jobDetail);
        return toAjax(sysJobDetailService.insertCubeJobDetail(jobDetail));
    }

    /**
     * 批量新增事件进程详情
     */
    @Log(title = "批量新增事件进程详情", businessType = BusinessType.INSERT)
    @PostMapping("/saveBatch/{jobId}")
//    @Operation(summary = "批量新增事件进程详情", description = "批量新增事件进程详情")
    public AjaxResult saveBatch(@PathVariable("jobId") Integer jobId, @RequestBody List<CubeJobDetail> cubeJobDetail) {
        sysJobDetailService.deleteCubeJobDetailByJobId(jobId);
        cubeJobDetail.forEach(v -> v.setJobId(jobId));
        return toAjax(sysJobDetailService.saveBatch(cubeJobDetail));
    }

    /**
     * 修改事件进程详情
     */
    @Log(title = "修改事件进程详情", businessType = BusinessType.UPDATE)
    @PutMapping
    @Operation(summary = "修改事件进程详情", description = "修改事件进程详情")
    public AjaxResult edit(@RequestBody CubeJobDetailDto cubeJobDetailDto) {
        CubeJobDetail jobDetail = new CubeJobDetail();
        BeanUtil.copyProperties(cubeJobDetailDto, jobDetail);
        return toAjax(sysJobDetailService.updateCubeJobDetail(jobDetail));
    }

    /**
     * 删除事件进程详情
     */
//    @Log(title = "删除事件进程详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Operation(summary = "删除事件进程详情", description = "删除事件进程详情")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysJobDetailService.deleteCubeJobDetailByIds(ids));
    }
}
