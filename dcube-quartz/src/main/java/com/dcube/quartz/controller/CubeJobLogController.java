package com.dcube.quartz.controller;

import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.enums.BusinessType;
import com.dcube.quartz.domain.CubeJobLog;
import com.dcube.quartz.dto.PageJobLogDto;
import com.dcube.quartz.service.ICubeJobLogService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调度日志操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/jobLog")
@Tag(name = "DCUBE-调度任务日志", description = "DCUBE-调度任务日志")
@RequiredArgsConstructor
public class CubeJobLogController extends BaseController {
    private final ICubeJobLogService jobLogService;

    /**
     * 查询定时任务调度日志列表
     */
//    @PreAuthorize("@ss.hasPermi('monitor:job:list')")
    @GetMapping("/list")
    @Operation(summary = "查询定时任务调度日志列表", description = "查询定时任务调度日志列表")
    public TableDataInfo list(CubeJobLog cubeJobLog) {
        startPage();
        List<CubeJobLog> list = jobLogService.selectJobLogList(cubeJobLog);
        return getDataTable(list);
    }

    /**
     * 导出定时任务调度日志列表
     */
//    @PreAuthorize("@ss.hasPermi('monitor:job:export')")
//    @Log(title = "任务调度日志", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, CubeJobLog sysJobLog)
//    {
//        List<CubeJobLog> list = jobLogService.selectJobLogList(sysJobLog);
//        ExcelUtil<CubeJobLog> util = new ExcelUtil<CubeJobLog>(CubeJobLog.class);
//        util.exportExcel(response, list, "调度日志");
//    }

    /**
     * 根据调度编号获取详细信息
     */
//    @PreAuthorize("@ss.hasPermi('monitor:job:query')")
    @GetMapping(value = "/{jobLogId}")
    @Operation(summary = "根据调度编号获取详细信息", description = "根据调度编号获取详细信息")
    public AjaxResult getInfo(@PathVariable Long jobLogId) {
        return success(jobLogService.selectJobLogById(jobLogId));
    }


    /**
     * 删除定时任务调度日志
     */
//    @PreAuthorize("@ss.hasPermi('monitor:job:remove')")
    @Log(title = "定时任务调度日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{jobLogIds}")
    @Operation(summary = "定时任务调度日志", description = "定时任务调度日志")
    public AjaxResult remove(@PathVariable Long[] jobLogIds) {
        return toAjax(jobLogService.deleteJobLogByIds(jobLogIds));
    }

    /**
     * 清空定时任务调度日志
     */
//    @PreAuthorize("@ss.hasPermi('monitor:job:remove')")
    @Log(title = "清空定时任务调度日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    @Operation(summary = "清空定时任务调度日志", description = "清空定时任务调度日志")
    public AjaxResult clean() {
        jobLogService.cleanJobLog();
        return success();
    }

    @GetMapping("/pageJobLog")
    @Operation(summary = "分页查询事件日志", description = "查询定时任务调度日志列表")
    public TableDataInfo pageJobLog(@Validated PageJobLogDto pageJobLogDto) {
        startPage();
        List<CubeJobLog> list = jobLogService.pageJobLog(pageJobLogDto);
        return getDataTable(list);
    }
}
