package com.dcube.quartz.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcube.quartz.domain.CubeJobDetail;

/**
 * 事件进程详情Service接口
 *
 * @date 2023-10-16
 */
public interface ICubeJobDetailService extends IService<CubeJobDetail> {
    /**
     * 查询事件进程详情
     *
     * @param id 事件进程详情主键
     * @return 事件进程详情
     */
    public CubeJobDetail selectCubeJobDetailById(Long id);

    /**
     * 查询事件进程详情列表
     *
     * @param cubeJobDetail 事件进程详情
     * @return 事件进程详情集合
     */
    public List<CubeJobDetail> selectCubeJobDetailList(CubeJobDetail cubeJobDetail);

    /**
     * 新增事件进程详情
     *
     * @param cubeJobDetail 事件进程详情
     * @return 结果
     */
    public int insertCubeJobDetail(CubeJobDetail cubeJobDetail);

    /**
     * 修改事件进程详情
     *
     * @param cubeJobDetail 事件进程详情
     * @return 结果
     */
    public int updateCubeJobDetail(CubeJobDetail cubeJobDetail);

    /**
     * 批量删除事件进程详情
     *
     * @param ids 需要删除的事件进程详情主键集合
     * @return 结果
     */
    public int deleteCubeJobDetailByIds(Long[] ids);

    /**
     * 删除事件进程详情信息
     *
     * @param id 事件进程详情主键
     * @return 结果
     */
    public int deleteCubeJobDetailById(Long id);

    void deleteCubeJobDetailByJobIds(List<Integer> jobIds);

    void deleteCubeJobDetailByJobId(Integer jobId);

    List<CubeJobDetail> getByJobId(Integer jobId);
}
