package com.dcube.quartz.util;

import com.dcube.common.constant.CacheConstants;
import com.dcube.common.constant.Constants;
import com.dcube.common.constant.ScheduleConstants;
import com.dcube.common.utils.ExceptionUtil;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.ThreadLocalUtils;
import com.dcube.common.utils.bean.BeanUtils;
import com.dcube.common.utils.spring.SpringUtils;
import com.dcube.quartz.constants.enums.JobExecuteStatusEnums;
import com.dcube.quartz.domain.CubeJob;
import com.dcube.quartz.domain.CubeJobLog;
import com.dcube.quartz.service.ICubeJobLogService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 抽象quartz调用
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractQuartzJob implements Job {

    private static final Map<Integer, JobExecuteStatusEnums> JOB_EXECUTE_STATUS = new ConcurrentHashMap<>();

    private static final ThreadLocal<Map<String, Object>> LOCAL_MAP = new ThreadLocal<>();


    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        CubeJob cubeJob = new CubeJob();
        BeanUtils.copyBeanProp(cubeJob, context.getMergedJobDataMap().get(ScheduleConstants.TASK_PROPERTIES));
        try {
            before(context, cubeJob);
            doExecute(context, cubeJob);
            after(context, cubeJob, null);
        } catch (Exception e) {
            log.error("任务执行异常  - ：", e);
            after(context, cubeJob, e);
        } finally {
            LOCAL_MAP.remove();
            ThreadLocalUtils.clear();
        }
    }

    /**
     * 执行前
     *
     * @param context 工作执行上下文对象
     * @param cubeJob 系统计划任务
     */
    protected void before(JobExecutionContext context, CubeJob cubeJob) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(3);
        map.put(CacheConstants.JOB_KEY + "START_TIME", new Date());
        map.put(CacheConstants.JOB_KEY + "CONTEXT", context);
        map.put(CacheConstants.JOB_KEY + "CUBE_JOB", cubeJob);
        LOCAL_MAP.set(map);
    }

    /**
     * 执行后
     *
     * @param context 工作执行上下文对象
     * @param cubeJob 系统计划任务
     */
    protected void after(JobExecutionContext context, CubeJob cubeJob, Exception e) {
        Map<String, Object> map = LOCAL_MAP.get();
        Date startTime = (Date) MapUtils.getObject(map, CacheConstants.JOB_KEY + "START_TIME");
        final CubeJobLog cubeJobLog = new CubeJobLog();
        cubeJobLog.setJobName(cubeJob.getJobName());
        cubeJobLog.setJobGroup(cubeJob.getJobGroup());
        cubeJobLog.setInvokeTarget(cubeJob.getInvokeTarget());
        cubeJobLog.setStartTime(startTime);
        cubeJobLog.setStopTime(new Date());
        cubeJobLog.setJobId(cubeJob.getJobId());
        long runMs = cubeJobLog.getStopTime().getTime() - cubeJobLog.getStartTime().getTime();
        cubeJobLog.setJobMessage(cubeJobLog.getJobName() + " 总共耗时：" + runMs + "毫秒");
        if (e != null) {
            log.error("QuartzJob execute error:", e);
            cubeJobLog.setStatus(Constants.FAIL);
            String errorMsg = StringUtils.substring(ExceptionUtil.getRootErrorMessage(e), 0, 2000);
            cubeJobLog.setExceptionInfo(errorMsg);
        } else {
            cubeJobLog.setStatus(Constants.SUCCESS);
        }

        // 写入数据库当中
        SpringUtils.getBean(ICubeJobLogService.class).addJobLog(cubeJobLog);
    }

    /**
     * 执行方法，由子类重载
     *
     * @param context 工作执行上下文对象
     * @param cubeJob 系统计划任务
     * @throws Exception 执行过程中的异常
     */
    protected abstract void doExecute(JobExecutionContext context, CubeJob cubeJob) throws Exception;


    public static CubeJob getCubeJob() {
        return (CubeJob) MapUtils.getObject(LOCAL_MAP.get(), CacheConstants.JOB_KEY + "CUBE_JOB");
    }

    public static void updateJobExecuteStatus(Integer jobId, JobExecuteStatusEnums jobExecuteStatus) {
        JOB_EXECUTE_STATUS.put(jobId, jobExecuteStatus);

    }

    public static JobExecuteStatusEnums getJobExecuteStatus(Integer jobId) {
        return MapUtils.getObject(JOB_EXECUTE_STATUS, jobId);
    }

    public static JobExecuteStatusEnums getJobExecuteStatus(Integer jobId, JobExecuteStatusEnums jobExecuteStatus) {
        return MapUtils.getObject(JOB_EXECUTE_STATUS, jobId, jobExecuteStatus);
    }

}
