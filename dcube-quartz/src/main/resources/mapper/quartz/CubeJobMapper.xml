<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcube.quartz.mapper.CubeJobMapper">

    <resultMap type="CubeJob" id="CubeJobResult">
        <id property="jobId" column="job_id"/>
        <result property="jobName" column="job_name"/>
        <result property="jobGroup" column="job_group"/>
        <result property="invokeTarget" column="invoke_target"/>
        <result property="cronExpression" column="cron_expression"/>
        <result property="misfirePolicy" column="misfire_policy"/>
        <result property="concurrent" column="concurrent"/>
        <result property="triggerFrequency" column="trigger_frequency"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectJobVo">
        select job_id,
               job_name,
               job_group,
               invoke_target,
               cron_expression,
               misfire_policy,
               concurrent,
               status,
               trigger_frequency,
               create_by,
               create_time
        from cube_job
    </sql>

    <select id="selectJobList" parameterType="CubeJob" resultType="CubeJob">
        SELECT DISTINCT
        j.job_id,
        j.job_name,
        j.job_group,
        j.invoke_target,
        j.cron_expression,
        j.misfire_policy,
        j.concurrent,
        j.status,
        j.trigger_frequency,
        j.create_by,
        j.create_time,
        l.start_time AS last_trigger_time
        FROM cube_job j LEFT JOIN cube_job_log l ON j.job_id = l.job_id
        <where>
            (l.start_time = ( SELECT MAX( start_time ) FROM cube_job_log WHERE job_id = j.job_id )
            OR l.start_time IS NULL)
            <if test="jobName != null and jobName != ''">
                AND job_name like concat('%', #{jobName}, '%')
            </if>
            <if test="jobGroup != null and jobGroup != ''">
                AND job_group = #{jobGroup}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="invokeTarget != null and invokeTarget != ''">
                AND invoke_target like concat('%', #{invokeTarget}, '%')
            </if>
            <if test="triggerFrequency != null and triggerFrequency != ''">
                AND trigger_frequency like concat('%', #{triggerFrequency}, '%')
            </if>
        </where>
        order by j.job_name ASC,j.create_time DESC
    </select>

    <select id="selectJobAll" resultMap="CubeJobResult">
        <include refid="selectJobVo"/>
    </select>

    <select id="selectJobById" parameterType="Integer" resultMap="CubeJobResult">
        <include refid="selectJobVo"/>
        where job_id = #{jobId}
    </select>

    <delete id="deleteJobById" parameterType="Integer">
        delete
        from cube_job
        where job_id = #{jobId}
    </delete>

    <delete id="deleteJobByIds" parameterType="Long">
        delete from cube_job where job_id in
        <foreach collection="array" item="jobId" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </delete>

    <update id="updateJob" parameterType="CubeJob">
        update cube_job
        <set>
            <if test="jobName != null and jobName != ''">job_name = #{jobName},</if>
            <if test="jobGroup != null and jobGroup != ''">job_group = #{jobGroup},</if>
            <if test="invokeTarget != null and invokeTarget != ''">invoke_target = #{invokeTarget},</if>
            <if test="cronExpression != null and cronExpression != ''">cron_expression = #{cronExpression},</if>
            <if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy = #{misfirePolicy},</if>
            <if test="concurrent != null and concurrent != ''">concurrent = #{concurrent},</if>
            <if test="status !=null">status = #{status},</if>
            <if test="triggerFrequency !=null">trigger_frequency = #{triggerFrequency},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where job_id = #{jobId}
    </update>

    <update id="updateJob" parameterType="CubeJob" databaseId="pg">
        update cube_job
        <set>
            <if test="jobName != null and jobName != ''">job_name = #{jobName},</if>
            <if test="jobGroup != null and jobGroup != ''">job_group = #{jobGroup},</if>
            <if test="invokeTarget != null and invokeTarget != ''">invoke_target = #{invokeTarget},</if>
            <if test="cronExpression != null and cronExpression != ''">cron_expression = #{cronExpression},</if>
            <if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy = #{misfirePolicy},</if>
            <if test="concurrent != null and concurrent != ''">concurrent = #{concurrent},</if>
            <if test="status !=null">status = #{status},</if>
            <if test="triggerFrequency !=null">trigger_frequency = #{triggerFrequency},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = now()
        </set>
        where job_id = #{jobId}
    </update>


    <insert id="insertJob" parameterType="CubeJob" useGeneratedKeys="true" keyProperty="jobId">
        insert into cube_job(
        <if test="jobId != null and jobId != 0">job_id,</if>
        <if test="jobName != null and jobName != ''">job_name,</if>
        <if test="jobGroup != null and jobGroup != ''">job_group,</if>
        <if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
        <if test="cronExpression != null and cronExpression != ''">cron_expression,</if>
        <if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy,</if>
        <if test="concurrent != null and concurrent != ''">concurrent,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="triggerFrequency != null and triggerFrequency != ''">trigger_frequency,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="jobId != null and jobId != 0">#{jobId},</if>
        <if test="jobName != null and jobName != ''">#{jobName},</if>
        <if test="jobGroup != null and jobGroup != ''">#{jobGroup},</if>
        <if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
        <if test="cronExpression != null and cronExpression != ''">#{cronExpression},</if>
        <if test="misfirePolicy != null and misfirePolicy != ''">#{misfirePolicy},</if>
        <if test="concurrent != null and concurrent != ''">#{concurrent},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="triggerFrequency != null and triggerFrequency != ''">#{trigger_frequency},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <insert id="insertJob" parameterType="CubeJob" useGeneratedKeys="true" keyProperty="jobId" databaseId="pg">
        insert into cube_job(
        <if test="jobId != null and jobId != 0">job_id,</if>
        <if test="jobName != null and jobName != ''">job_name,</if>
        <if test="jobGroup != null and jobGroup != ''">job_group,</if>
        <if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
        <if test="cronExpression != null and cronExpression != ''">cron_expression,</if>
        <if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy,</if>
        <if test="concurrent != null and concurrent != ''">concurrent,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="triggerFrequency != null and triggerFrequency != ''">trigger_frequency,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="jobId != null and jobId != 0">#{jobId},</if>
        <if test="jobName != null and jobName != ''">#{jobName},</if>
        <if test="jobGroup != null and jobGroup != ''">#{jobGroup},</if>
        <if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
        <if test="cronExpression != null and cronExpression != ''">#{cronExpression},</if>
        <if test="misfirePolicy != null and misfirePolicy != ''">#{misfirePolicy},</if>
        <if test="concurrent != null and concurrent != ''">#{concurrent},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="triggerFrequency != null and triggerFrequency != ''">#{trigger_frequency},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        now()
        )
    </insert>

</mapper> 