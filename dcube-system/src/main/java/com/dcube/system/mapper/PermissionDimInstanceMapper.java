package com.dcube.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcube.common.core.domain.entity.PermissionDimInstance;
import com.dcube.system.vo.RoleDimInstanceListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PermissionDimInstanceMapper extends BaseMapper<PermissionDimInstance> {

    List<RoleDimInstanceListVo> selectRoleDimInstanceList(@Param("roleId") String roleId, @Param("tableId") String tableId);
}
