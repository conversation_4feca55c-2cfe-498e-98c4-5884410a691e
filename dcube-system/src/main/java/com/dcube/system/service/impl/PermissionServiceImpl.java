package com.dcube.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcube.common.core.domain.entity.Permission;
import com.dcube.common.exception.ServiceException;
import com.dcube.system.mapper.PermissionMapper;
import com.dcube.system.service.IPermissionService;
import com.dcube.system.vo.RolePermissionVo;
import com.dcube.system.vo.RoleTableListVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission> implements IPermissionService {

    @Override
    @Transactional
    public void saveOrUpdate(RolePermissionVo rolePermissionVo) {
        if (Objects.isNull(rolePermissionVo.getRoleId())) {
            log.error("角色ID参数为空. ");
            throw new ServiceException("角色ID参数为空. ");
        }
        // delete
        this.baseMapper.delete(Wrappers.<Permission>query().lambda().eq(Permission::getRoleId, rolePermissionVo.getRoleId()));
        // reset
        if (CollectionUtil.isNotEmpty(rolePermissionVo.getPermissionList())) {
            for (Permission permission : rolePermissionVo.getPermissionList()) {
                permission.setId(null);
                permission.setRoleId(rolePermissionVo.getRoleId());
            }
            this.saveBatch(rolePermissionVo.getPermissionList());
        }
    }

    @Override
    public List<RoleTableListVo> selectRoleTableList(String roleId) {
        return this.baseMapper.selectRoleTableList(roleId);
    }

    @Override
    public List<Permission> selectUserTableList(Long userId, String userName) {
        return this.baseMapper.selectUserTableList(userId, userName);
    }
}
