{
    "[vue]": {
        "editor.defaultFormatter": "octref.vetur"
    },
    "javascript.format.insertSpaceBeforeFunctionParenthesis": true, //让函数(名)和后面的括号之间加个空格
    "vetur.format.defaultFormatter.html": "js-beautify-html", //格式化.vue中html
    "vetur.format.defaultFormatter.js": "prettier-eslint", //让vue中的js按编辑器自带的ts格式进行格式化
    "vetur.format.defaultFormatterOptions": {
        "js-beautify-html": {
            "wrap_attributes": "force-expand-multiline",
            "end_with_newline": false
        },
        "prettier": {
            "semi": true,
            "eslintIntegration": true,
            "singleQuote": true,  
            "jsxBracketSameLine": true
        }
    },
    "javascript.validate.enable": false,
    "javascript.format.enable": true,
    "editor.tabSize": 2,
    "eslint.autoFixOnSave": true,
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        {
            "language": "html",
            "autoFix": true
        },
        {
            "language": "vue",
            "autoFix": true
        },
        "html",
        "vue"
    ],
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "javascript.updateImportsOnFileMove.enabled": "never",
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": true
    },
    "[typescript]": {
        "editor.defaultFormatter": "vscode.typescript-language-features"
    },
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "workbench.editorAssociations": {
        "*.svg": "default"
    },
    "security.workspace.trust.untrustedFiles": "open",
    "[scss]": {
        "editor.defaultFormatter": "michelemelluso.code-beautifier"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "editor.fontSize": 18,
    "explorer.confirmDelete": false,
    "editor.largeFileOptimizations": false,
    "editor.suggestSelection": "first",
    "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue",
    "files.exclude": {
        "**/.classpath": true,
        "**/.project": true,
        "**/.settings": true,
        "**/.factorypath": true
    },
    "workbench.iconTheme": "material-icon-theme",
    "eslint.workingDirectories": [
        ".eslintrc.js",
        {
            "mode": "auto"
        }
    ]
}