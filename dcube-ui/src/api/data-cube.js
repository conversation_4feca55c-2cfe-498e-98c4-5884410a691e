import request from '@/utils/request'

// 获取列表
export function getList(params) {
  return request({
    url: `/dcube/dim_table/list`,
    method: 'get',
    params
  })
}
// 新增
export function addDimTable(data) {
  return request({
    url: `/dcube/dim_table/add`,
    method: 'post',
    data
  })
}
// 修改
export function editDimTable(data) {
  return request({
    url: '/dcube/dim_table/put',
    method: 'post',
    data
  })
}
// 重命名
export function renameCube(data) {
  return request({
    url: '/dcube/dim_table/rename',
    method: 'post',
    data
  })
}
// 删除
export function delNode(id) {
  return request({
    url: `/dcube/dim_table/remove/${id}`,
    method: 'post'
  })
}
// 属性列表
export function getIndList(params) {
  return request({
    url: `/dcube/ind/list`,
    method: 'get',
    params
  })
}
// 添加属性
export function addInd(data) {
  return request({
    url: `/dcube/ind/add`,
    method: 'post',
    data
  })
}
// 删除属性
export function delInd(id) {
  return request({
    url: `/dcube/ind/remove/${id}`,
    method: 'post'
  })
}

// 新增表格
export function addTable(data) {
  return request({
    url: '/dcube/2dTable/add',
    method: 'post',
    data
  })
}
// 修改表格
export function editTable(data) {
  return request({
    url: '/dcube/2dTable/put',
    method: 'post',
    data
  })
}
// 表格详情
export function get2dTable(data) {
  return request({
    url: `/dcube/2dTable/listMemData`,
    method: 'post',
    data
  })
}
// 子表详情
export function getSubTable(data) {
  return request({
    url: `/dcube/2dTable/sub/listMemData`,
    method: 'post',
    data
  })
}
// 载入源数据
export function loadOriginData(id) {
  return request({
    url: `/dcube/dim_table/loading?id=${id}`,
    method: 'post'
  })
}
// 加载多维数方
export function getTableDetail(data) {
  return request({
    url: `/dcube/dim_table/listTableData`,
    method: 'post',
    data
  })
}
// 查询多维数方维度
export function getDim(id) {
  return request({
    url: `/dcube/dim_table/${id}`,
    method: 'get'
  })
}
// 加载源数据
export function loadViewData(id) {
  return request({
    url: `/dcube/view/loadDataById/${id}`,
    method: 'get'
  })
}
// 父表入口列
export function getParentCol(params) {
  return request({
    url: `/dcube/2dTable/sub/pk_list`,
    method: 'get',
    params
  })
}
// 保存列
export function saveCol(data) {
  return request({
    url: `/dcube/2dTable/addColumn`,
    method: 'post',
    data
  })
}
// 保存行
export function addRowData(data) {
  return request({
    url: `/dcube/2dTable/addData`,
    method: 'post',
    data
  })
}
// 修改行
export function updateRowData(data) {
  return request({
    url: `/dcube/2dTable/updateData`,
    method: 'post',
    data
  })
}
// 计算求和平均
export function calcSumAvg(data) {
  return request({
    url: `/dcube/2dTable/avgAndSum`,
    method: 'post',
    data
  })
}
// 新增子表
export function addChildTable(data) {
  return request({
    url: '/dcube/2dTable/sub/add',
    method: 'post',
    data
  })
}
// 删除行
export function deleteRowApi(data) {
  return request({
    url: '/dcube/2dTable/deleteData',
    method: 'post',
    data
  })
}
// 获取字典集合
export function getDict(params) {
  return request({
    url: '/dcube/group_instance/list',
    method: 'get',
    params
  })
}
// 加载数据
export function loadData(params) {
  return request({
    url: '/dcube/2dTable/loading',
    method: 'post',
    params
  })
}
// 加载数据表状态
export function getTableProcess(data) {
  return request({
    url: '/dcube/2dTable/loading_state',
    method: 'post',
    data
  })
}
// 二维表释放数据
export function releaseData(params) {
  return request({
    url: '/dcube/2dTable/release',
    method: 'post',
    params
  })
}
// 数据格式分组实例树(排除字典分组)
export function getDataFormat(params) {
  return request({
    url: '/dcube/group_instance/tree_exclude_dic',
    method: 'get',
    params
  })
}
// 校验计算规则
export function checkRule(data) {
  return request({
    url: '/dcube/rule/validate',
    method: 'post',
    data
  })
}
// 保存计算规则
export function saveRule(data) {
  return request({
    url: '/dcube/rule/save',
    method: 'post',
    data
  })
}
// 获取进度
export function getTaskProcess(params) {
  return request({
    url: '/dcube/task/getTaskInfo',
    method: 'get',
    params
  })
}
// 自增id
export function createId(data) {
  return request({
    url: '/dcube/2dTable/createIncreId',
    method: 'post',
    data
  })
}
// 规则快捷输入
export function getQuickInput(params) {
  return request({
    url: '/dcube/rule/getQuickInput',
    method: 'get',
    params
  })
}
// 获取任务进度
export function getTaskInfoByTableId(params) {
  return request({
    url: '/dcube/task/getTaskInfoByTableId',
    method: 'get',
    params
  })
}
// 备份数据
export function backUpData(data) {
  return request({
    url: '/dcube/2dTable/backup',
    method: 'post',
    data
  })
}
// 获取二维表版本列表
export function getVersionList(tableId) {
  return request({
    url: `/dcube/tableVersion/select/${tableId}`,
    method: 'get'
  })
}
// 还原数据
export function revertData(tableId, versionId) {
  return request({
    url: `/dcube/2dTable/recoverByVersion/${tableId}/${versionId}`,
    method: 'post'
  })
}
// 删除版本
export function delVersionApi(versionId) {
  return request({
    url: `/dcube/tableVersion/remove/${versionId}`,
    method: 'post'
  })
}
// 导出
export function exportTable(data) {
  return request({
    url: '/dcube/2dTable/exportData',
    method: 'post',
    data,
    timeout: 120000
  })
}
// 执行表规则
export function executeRule(data) {
  return request({
    url: '/dcube/rule/execute',
    method: 'post',
    data
  })
}
// 获取聚合函数列表
export function getPolyFuncList() {
  return request({
    url: `/dcube/rule/aggregationFunList`,
    method: 'get'
  })
}
// 新增聚合二维表
export function addPolyTable(data) {
  return request({
    url: '/dcube/2dTable/add/aggregation',
    method: 'post',
    data
  })
}
// // 上传zip
// export function updateZip(data) {
//   return request({
//     url: `/dcube/2dTable/uploadZipFile`,
//     method: 'post',
//     data,
//     timeout: 300000
//   })
// }
// 导入Excel
export function importExcel(data) {
  return request({
    url: `/dcube/2dTable/impExcel`,
    method: 'post',
    data,
    timeout: 300000
  })
}
// 预览Excel
export function previewExcel(data) {
  return request({
    url: `/dcube/2dTable/previewExcel`,
    method: 'post',
    data
  })
}
// 删除Excel
export function delExcelApi(data) {
  return request({
    url: `/dcube/2dTable/removeExcels`,
    method: 'delete',
    data
  })
}
// 解压接口
export function unZip(data) {
  return request({
    url: `/dcube/2dTable/unzip`,
    method: 'post',
    data
  })
}
export function downloadExcel(params) {
  return request({
    url: '/dcube/common/download',
    method: 'get',
    responseType: 'arraybuffer',
    params
  })
}
// 下载二维表模版
export function downloadTemplate(params) {
  return request({
    url: '/dcube/2dTable/downloadTemplate',
    method: 'get',
    responseType: 'arraybuffer',
    params
  })
}

export function impData(data) {
  return request({
    url: `/dcube/2dTable/impData`,
    method: 'post',
    data
  })
}

// 函数列表
export function getFunctionList(params) {
  return request({
    url: '/dcube/rule/getFunctionList',
    method: 'get',
    params
  })
}
// 获取表格主键
export function getTableKey(params) {
  return request({
    url: '/dcube/2dTable/pk',
    method: 'get',
    params
  })
}
// 保存多维
export function saveTable(data) {
  return request({
    url: `/dcube/dim_table/addData`,
    method: 'post',
    data
  })
}
// 设置维度关联
export function setDimRel(data) {
  return request({
    url: `/dcube/dim_dim/add`,
    method: 'post',
    data
  })
}
// 获取维度关联列表
export function getDimRel(params) {
  return request({
    url: `/dcube/dim_dim/list`,
    method: 'get',
    params
  })
}
// 执行表规则
export function executeDimRule(data) {
  return request({
    url: '/dcube/cube/dimRule/execute',
    method: 'post',
    data
  })
}
// 保存查看样式
export function saveLayout(data) {
  return request({
    url: '/dcube/dim_table/layout',
    method: 'post',
    data
  })
}
/** 查询维度计算的维度
 * @param {*} params:tableId,dimVariableId
 * @returns
 */
export function getDimRuleDims(params) {
  return request({
    url: `/dcube/cube/dimRule/getDimRuleDims`,
    method: 'get',
    params
  })
}
/** 查询规则的多维变量
 * @param {*} params:dimRuleId
 * @returns
 */
export function getDimVariables(params) {
  return request({
    url: `/dcube/cube/dimRule/listDimVariable`,
    method: 'get',
    params
  })
}
// 删除多维变量 data:id
export function delVariable(data) {
  return request({
    url: '/dcube/cube/dimRule/removeDimVariable',
    method: 'delete',
    data
  })
}
// 保存多维变量 dimRuleId,dimTableId,id,variableDetails:[dimDirectoryId,dimRuleVariableTableId,dimType,effectScope],variableName
export function saveVariable(data) {
  return request({
    url: '/dcube/cube/dimRule/saveDimVariable',
    method: 'post',
    data
  })
}
