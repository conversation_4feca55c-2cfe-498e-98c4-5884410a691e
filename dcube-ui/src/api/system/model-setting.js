import request from '@/utils/request'
// 查询模型场景
export function getModelScene() {
  return request({
    url: '/dcube/ai/modelConfig/queryUseScene',
    method: 'get'
  })
}
// 查询模型配置类型
export function getModelType() {
  return request({
    url: '/dcube/ai/modelConfig/queryModelType',
    method: 'get'
  })
}
// 查询模型配置列表
export function getModelList() {
  return request({
    url: '/dcube/ai/modelConfig/list',
    method: 'get'
  })
}
// 新增模型配置
export function addModel(data) {
  return request({
    url: '/dcube/ai/modelConfig/add',
    method: 'post',
    data
  })
}
// 修改模型配置
export function editModel(data) {
  return request({
    url: '/dcube/ai/modelConfig/put',
    method: 'post',
    data
  })
}
// 删除模型配置
export function delModel(id) {
  return request({
    url: `/dcube/ai/modelConfig/remove/${id}`,
    method: 'post'
  })
}
// 设置场景
export function setModelScene(data) {
  return request({
    url: '/dcube/ai/modelConfig/setUseScene',
    method: 'post',
    data
  })
}
// 测试模型
export function testModel(data) {
  return request({
    url: '/dcube/ai/modelConfig/test',
    method: 'post',
    data,
    timeout: 300000
  })
}
// 查询模型场景设置
export function getSceneSetting(params) {
  return request({
    url: `/dcube/ai/modelConfig/queryModelUseSceneSetting`,
    method: 'get',
    params
  })
}
// 保存使用场景示例
export function saveExample(data) {
  return request({
    url: `/dcube/ai/modelConfig/saveUseSceneInputExample`,
    method: 'post',
    data
  })
}
