import request from '@/utils/request'
// import { baseUrl } from '@/utils/config'
export function login(data) {
  return request({
    url: `/dcube/login`,
    method: 'post',
    data
  })
}

export function getInfo(token) {
  return request({
    url: `/dcube/getInfo`,
    method: 'get'
    // params: { token }
  })
}

export function logout() {
  return request({
    url: `/dcube/logout`,
    method: 'post'
  })
}
// 获取路由信息
export function getRouters() {
  return request({
    url: `/dcube/getRouters`,
    method: 'get'
  })
}
// 获取人员列表

export function getUserList(params) {
  return request({
    url: `/dcube/system/user/list`,
    method: 'get',
    params
  })
}
// 新的获取人员列表
export function newGetUserList(params) {
  return request({
    url: `/dcube/system/user/listUser`,
    method: 'get',
    params
  })
}
// 查询用户详细
export function getUser(userId) {
  return request({
    url: `/dcube/system/user/` + userId,
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: `/dcube/system/user`,
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: `/dcube/system/user`,
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: `/dcube/system/user/` + userId,
    method: 'delete'
  })
}
// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/dcube/system/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/dcube/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/dcube/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/dcube/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/dcube/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/dcube/system/user/authRole/' + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/dcube/system/user/authRole',
    method: 'put',
    params: data
  })
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: '/dcube/system/user/deptTree',
    method: 'get'
  })
}
// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/dcube/system/user/resetPwd',
    method: 'put',
    data: data
  })
}
// 下载用户模板信息
export function downloadTemplate() {
  return request({
    url: '/dcube/system/user/downloadTemplate',
    method: 'get',
    responseType: 'arraybuffer'
  })
}
// 导入
export function importFromExcel(data) {
  return request({
    url: '/dcube/system/user/importFromExcel',
    method: 'post',
    data
  })
}
// 获取公钥
export function getPublicKey() {
  return request({
    url: '/dcube/crypto/getPublicKey',
    method: 'get'
  })
}
export function getVersion() {
  return request({
    url: '/dcube/system/info/version',
    method: 'get'
  })
}
