<template>
  <div class="copy-code-container">
    <div class="code-palce-container" :class="{ 'show-code': showCode }">
      <div v-highlight class="code-box">
        <!-- <div class="code-btn flex-row">
          <span class="el-icon-document-copy" @click="handleCopy(code, $event)" />
          <i class="vxe-icon-rich-text" @click="handeShowCode" />
        </div> -->
        <pre>
            <code class="language-sql">{{ code }}</code>
        </pre>
      </div>
    </div>
  </div>
</template>

<script>
import clip from '@/utils/clipboard.js' // use clipboard directly

export default {
  props: {
    code: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showCode: true
    }
  },
  methods: {
    handeShowCode() {
      this.showCode = !this.showCode
    },
    handleCopy(text, event) {
      this.$message({
        message: '复制成功',
        type: 'success'
      })
      clip(text, event)
    }
  }
}
</script>

<style lang="scss" scoped>
.copy-code-container {
  width: 100%;
  .code-palce-container {
    width: 100%;
    height: 0;
    overflow: hidden;
    transition: all linear 0.1s;

    &.show-code {
      height: 100%;
    }
    .code-box {
      position: relative !important;
      ::v-deep pre {
        margin: 0 !important;
        display: flex;
      }
      width: 100%;
      ::v-deep .hljs {
        width: 100%;
        font-size: 12px;
        padding: 20px;
        line-height: 25px;
      }
      .code-btn {
        position: absolute;
        right: 20px;
        top: 30px;
        z-index: 10;
        color: #fff;
        cursor: pointer;
      }
    }
  }
}
</style>

