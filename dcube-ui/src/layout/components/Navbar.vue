<template>
  <div class="navbar">
    <!-- <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->
    <div class="flex-vertical-center">
      <div class="left-name" />
    </div>
    <!-- <div class="middle-nav">
      <div v-for="(item,index) in asyncRoutes" :key="index" class="item">
        <router-link :to="resolvePath(item)">
          {{ item.meta?item.meta.title:item.children[0].meta.title }}
        </router-link>
      </div>
    </div> -->
    <el-menu
      :default-active="activeIndex"
      class="el-menu-demo"
      mode="horizontal"
      background-color="#004DE7"
      text-color="#fffc"
      active-text-color="#fff"
      @select="handleSelect"
    >
      <template v-for="(item,index) in asyncRoutes">
        <el-menu-item v-if="item.children.length===1" :key="String(index)" :index="String(index)" @click.native="linkToPage(item.path,item.children[0].path)">
          <span :class="item.meta?item.meta.icon:item.children[0].meta.icon">
            {{ item.meta?item.meta.title:item.children[0].meta.title }}
          </span>
        </el-menu-item>
        <el-submenu v-if="item.children.length>1" :key="String(index)" class="multi-submenu" :index="String(index)">
          <template slot="title">
            <span :class="item.meta?item.meta.icon:item.children[0].meta.icon">
              {{ item.meta?item.meta.title:item.children[0].meta.title }}
            </span>
          </template>
          <el-menu-item v-for="(child,idx) in item.children" :key="index+'-'+idx" :index="index+'-'+idx" @click.native="linkToPage(item.path,child.path)">
            <span :class="child.meta.icon">
              {{ child.meta.title }}
            </span>
          </el-menu-item>
        </el-submenu>
      </template>
    </el-menu>
    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <div class="nick-box">
          <i class="iconfont icon-circle-user" />
          <div class="nick-name">{{ nickName }}</div>
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item divided @click.native="openUserInfo">
            <span style="display:block;">个人中心</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="checkVersion">
            <span style="display:block;">查看版本</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display:block;">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <user-center v-if="userCneterShow" @closeUserCenter="userCneterShow = false" />
    </div>
    <el-dialog
      title="查看版本号"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <div class="version-box">当前系统后端版本号为:<span>{{ curVersion }}</span></div>
      <div class="version-box">当前系统前端版本号为:<span>{{ $version }}</span></div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import path from 'path'
import defaultAvatar from '@/assets/default-avatar.png'
import UserCenter from './profile/index.vue'
import { getVersion } from '@/api/system/user'
export default {
  components: {
    // Hamburger
    UserCenter
  },
  data() {
    return {
      asyncRoutes: this.$store.getters.asyncRoutes,
      activeIndex: sessionStorage.getItem('navActiveIdx') || '0',
      userCneterShow: false,
      nickName: this.$store.getters.user.nickName,
      dialogVisible: false,
      curVersion: ''
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar'
    ]),
    avatarSrc() {
      return this.avatar ? this.avatar + '?imageView2/1/w/80/h/80' : defaultAvatar
    }
  },
  created() {
    console.log('动态路由', this.asyncRoutes)
  },
  methods: {
    // 点击导航
    handleSelect(key, keyPath) {
      console.log(key, keyPath)
      this.activeIndex = String(key)
      sessionStorage.setItem('navActiveIdx', String(key))
    },
    resolvePath(path1, path2) {
      return path.resolve(path1, path2)
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      // 去掉sessionStorage
      sessionStorage.removeItem('navActiveIdx')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    linkToPage(path1, path2) {
      const linkToPath = this.resolvePath(path1, path2)
      this.$router.push(linkToPath)
    },
    // 打开个人信息框
    openUserInfo() {
      this.userCneterShow = true
    },
    checkVersion() {
      this.dialogVisible = true
      getVersion().then(res => {
        if (res.code === 200) {
          this.curVersion = res.msg
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import  '@/styles/variables';
.navbar {
  height: 56px;
  overflow: hidden;
  position: relative;
  background: $primary;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 16px;
  .left-name {
    background-image: url("../../assets/navbar-slogan.png");
    width: 130px;
    height: 30px;
    background-size: contain;
    background-repeat: no-repeat;
  }
.middle-nav {
  height: 56px;
  display: flex;
  align-items: flex-end;
  .item {
    font-size: 14px;
    margin: 0 10px;
    background: $primary;
    color: #fff;
    cursor: pointer;
    margin-bottom: -1px;
    .router-link-active {
      padding: 10px 12px 0px 12px;
      background: #fff;
      color:$primary;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
    }
  }
}
  .right-menu {
    height: 100%;
    line-height: 56px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;
    }
  }
}
.multi-submenu {
  ::v-deep .el-icon-arrow-down {
    color: #fff;
  }
}
.user-dropdown {
  padding: 0;
  margin-top: 0;
  .el-dropdown-menu__item {
    margin-top: 0;
    &::before {
      height: 0;
    }
    background: $defaultBg;
    color: $secondText;
    &:focus {
      background: $focusBg;
      color: $primary;
    }
    &:hover {
      background: $focusBg;
      color: $primary;
    }
  }
}
.nick-name {
  color: #fff;
  cursor: pointer;
}
.el-menu-demo {
  .el-menu-item {
    border-bottom: none !important;
    &:hover {
      background: rgba(255,255,255,0.1) !important;
    }
  }
}
.nick-box {
  display: flex;
  align-items: center;
  color: #fff;
  i {
    margin-right: 5px;
  }
}
.version-box {
  margin: 10px 0;
  span {
    color: $primary;
    font-weight: 500;
  }
}
</style>
