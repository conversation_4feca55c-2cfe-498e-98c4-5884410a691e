import { asyncRoutes, constantRoutes } from '@/router'
import { getRouters } from '@/api/permission'
import { cloneDeep } from 'lodash'

export function filterAsyncRoutes(apiRoutes) {
  // 暂时写死
  const newRoutes = cloneDeep(asyncRoutes)
  const allRouteNames = []
  // 获取后端返回的路由并将name取出放到集合数组
  function getNameArr(arr) {
    arr && arr.forEach((item) => {
      if (item.name && !allRouteNames.includes(item.name)) {
        allRouteNames.push(item.name)
      }
      if (item.children && item.children.length) {
        getNameArr(item.children)
      }
    })
  }
  getNameArr(apiRoutes)
  const validRoutes = newRoutes.filter(item => {
    if (item.children && item.children.length) {
      item.children = item.children.filter((child) => allRouteNames.includes(child.name))
    }
    return allRouteNames.includes(item.name)
  })
  return validRoutes
}
const state = {
  routes: [],
  asyncRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.asyncRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }) {
    return new Promise((resolve, reject) => {
      getRouters().then(response => {
        const { code, data } = response
        if (code !== 200) {
          return reject('获取路由信息失败')
        }
        if (code === 200 && (!data || !data.length)) {
          return reject('该用户未分配菜单')
        }
        const accessedRoutes = filterAsyncRoutes(data)
        commit('SET_ROUTES', accessedRoutes)
        resolve(accessedRoutes)
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
