import { getDataFormat } from '@/api/base-table'
import { listToTree } from '@/utils/index'
const state = {
  formatList: [],
  formatListTree: []
}
const mutations = {
  SET_LIST: (state, formatList) => {
    state.formatList = formatList
  },
  SET_TREE: (state, formatListTree) => {
    state.formatListTree = formatListTree
  }
}

const actions = {
  getDataFormat({ commit }) {
    return new Promise((resolve, reject) => {
      getDataFormat()
        .then((response) => {
          const { code, data } = response
          if (code !== 200) {
            return reject('获取数据格式失败')
          }
          const treeData = listToTree(data)
          commit('SET_LIST', data)
          commit('SET_TREE', treeData)
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
