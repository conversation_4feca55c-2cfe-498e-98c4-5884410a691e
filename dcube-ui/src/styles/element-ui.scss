// cover some element-ui styles
@import './variables.scss';
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  .el-dialog__body {
    .el-form {
      .el-form-item {
        margin-top: 10px;
      }
    }
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}
.el-pagination {
  text-align: right;
  margin: 10px 0;
  .el-pager li.active {
    color: $primary;
  }
}
.el-button  {
  padding: 9px 13px;
}

// 表单样式
.el-form {
  .el-form-item {
    margin-bottom: 15px;
    .el-form-item__label {
      color: $mainText;
    }
    .el-input-number__decrease, .el-input-number__increase {
      &:hover {
        color: $primary;
        &~.el-input {
          .el-input__inner {
            border-color: $primary;
          }
        }
      }
    }
  }
}
.el-menu-item {
  font-size: 14px;
  span {
    font-size: 14px !important;
  }
}
.el-drawer__wrapper {
  .el-drawer__header {
    height: 70px !important;
    margin-bottom: 0px;
  }
  .el-drawer__body {
    padding: 15px;
  }
}
.el-popover {
  min-width: 30px !important;
  padding: 6px 8px !important;
  font-size: 13px;
}
// 主按钮
.el-button--primary {
  background-color: $primary;
  border-color: $primary;
  color: #fff;
  &:hover {
    background-color: $primary-hover;
    border-color: $primary-hover;
  }
  &:focus {
    background-color: $primary-focus;
    border-color: $primary-focus;
  }
}
.el-button--primary.is-plain {
  background-color: $btn-default;
  border-color: $primary;
  color: $primary;
  &:hover {
    background-color: $primary-hover;
    border-color: $primary-hover;
    color: $white;
  }
  &:focus {
    background-color: $primary-focus;
    border-color: $primary-focus;
    color: $white;
  }
}
// 默认按钮
.el-button--default {
  background-color: $white;
  border-color: $grayBorder;
  color: $mainText;
  &:hover {
    background-color: $btn-default;
    border-color: $primary-hover;
    color:$primary-hover;
  }
  &:focus {
    background-color: $btn-default;
    border-color: $primary-focus;
    color: $primary-focus;
  }
}
// 默认按钮-朴素按钮
.el-button--default.is-plain {
  background-color: $btn-default;
  border-color: $primary;
  color: $primary;
  &:hover {
    background-color: $primary-hover;
    border-color: $primary-hover;
    color: $white;
  }
  &:focus {
    background-color: $primary-focus;
    border-color: $primary-focus;
    color: $white;
  }
}
// 文字按钮
.el-button--text {
  color: $primary;
  &:hover {
    color:  $primary-hover;
  }
  &:focus {
    color: $primary-focus;
  }
}
// radio 
.el-radio-group {
  .is-checked {
    .el-radio__inner {
      color: $primary;
      background: $primary;
      border-color: $primary;
    }
    .el-radio__label {
      color: $primary;
    }
  }
}
// select
.el-select {
  .is-focus {
    .el-input__inner {
      border-color: $primary !important;
    }
  }
  .el-input__inner:focus {
    border-color: $primary !important;
  }
  .el-input__inner {
    color: $mainText;
  }
  &.el-select--mini {
    .el-input__inner {
      padding: 0 5px;
    }
  }
}
.el-input {
  .el-input__inner:focus {
    border-color: $primary !important;
  }
}
.el-select-dropdown  {
  .el-select-dropdown__item.selected {
    color: $primary !important;
  }
  .el-select-dropdown__item {
    font-size: 12px;
  }
}
.el-progress-bar {
  .el-progress-bar__inner {
    background-color: $primary;
  }
}
.el-switch.is-checked {
  .el-switch__core {
    background: $primary;
    border-color: $primary;
  }
}
.el-tree {
  .el-tree-node.is-current {
    .el-tree-node__content {
      background-color: $defaultBg;
    }
  }
}
.el-textarea {
  .el-textarea__inner {
    color: $mainText;
    &:focus {
      border-color: $primary;
    }
  }
}
.el-dialog__header {
  padding: 20px;
}
.el-dialog__header .el-dialog__close:hover {
  color: $primary;
}
.el-radio__inner:hover {
  border-color: $primary;
}
.el-tabs--border-card>.el-tabs__header .el-tabs__item {
  &.is-active,&:hover {
    color: $primary !important;
  }
}
.el-tabs {
  .el-tabs__active-bar {
    background-color: $primary;
  }
  .el-tabs__item.is-active {
    color: $primary;
  }
  .el-tabs__item:hover {
    color: $primary;
  }
}
.el-radio__input.is-checked {
  & .el-radio__inner {
    background-color: $primary !important;
    border-color: $primary !important;
  }
  &+.el-radio__label {
    color: $primary;
  }
}
.el-cascader-node {
  &.in-active-path,&.is-active {
    color: $primary;
  }
}
.tag-box {
  margin-bottom:6px;
  .el-tag {
    cursor: pointer;
    height: 32px;
    padding: 5px 8px;
    z-index: 1;
    border-radius: 4px;
    /* 选中 */
    background: $focusBg;
    border:1px solid $primary;
    color:$primary;
    margin-right: 6px;
    .el-tag__close {
      color: $primary;
      &:hover {
        background: $primary !important;
        border-color: $primary !important;
        color: #fff;
      }
    }
    &:hover {
      color: $primary;;
    }
    &.el-tag--info {
      background: $defaultBg;
      border:1px solid $focusBg;
      color: $secondText;
      &:hover {
        color: $primary;
      }
      .el-tag__close {
        color: $secondText;
        &:hover {
          background: $primary !important;
          border-color: $primary !important;
          color: #fff;
        }
      }
    }
  }
}
.el-checkbox.is-checked {
  .el-checkbox__inner {
    background-color: $primary;
    border-color: $primary;
  }
  .el-checkbox__label {
    color: $primary;
  }
}
.el-tree-node {
  .el-checkbox__input:hover,.el-checkbox__input.is-focus {
    .el-checkbox__inner {
      border-color: $primary;
    }
  }
  .el-checkbox__input.is-indeterminate {
    .el-checkbox__inner {
      background-color: $primary;
      border-color: $primary;
    }
  }
}
.el-tag {
  color: $primary;
}
.el-loading-spinner {
  .el-icon-loading,.el-loading-text {
    color: $primary;
  }
}
.el-date-picker {
  .today {
    span {
      color: $primary !important;
    }
  }
  .available:hover {
    span {
      color: $primary-hover !important;
    }
  }
  .el-date-table td.current:not(.disabled) span {
    color: #fff !important;
  }
}
.el-date-table td.current:not(.disabled) span {
  background: $primary;
}
.el-cascader-menu {
  .el-cascader-node {
    &.in-checked-path {
      .el-cascader-node__label {
        color: $primary;
      }
    }
  }
  .el-cascader-node__postfix {
    color: $primary;
  }
}