// sidebar
$menuText:#bfcbd9;
$menuActiveText:#004DE7;
$subMenuActiveText:#f4f4f5; //https://github.com/ElemeFE/element/issues/12951
$primary:#004DE7;
$primary-hover:#3377ff;
$primary-focus:#0045cf;
$btn-default:#f2f6ff;
$mainText: #1a253b;
$grayBorder:#e1e5eb;
$menuBg:#304156;
$menuHover:#263445;
$infoText: #909399;
$remindText:#C0C4CC;
$subMenuBg:#1f2d3d;
$subMenuHover:#001528;
$formBorder:#DCDFE6;
$formLabel:#606266;
$mainRed:#f56c6c;
$white:#fff;
$defaultBg:#f5f7fa; // 默认背景
$focusBg:#E6EBF6; // 选中背景
$secondText:#4e5d78; // 次要文字
$tipsText:#A6AFC2; // 说明文字
$importantText:#1A253B;
$sideBarWidth: 210px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
