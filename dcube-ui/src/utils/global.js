export default {
  // websocket
  webSocket: {},
  setWs: function(ws) {
    this.webSocket = ws
  },
  wsUrl: process.env.NODE_ENV === 'production' ? `${location.protocol === 'https:' ? 'wss' : 'ws'}://${location.hostname}:${location.port}/dcube/ws` : `${location.protocol === 'https:' ? 'wss' : 'ws'}://127.0.0.1:8999/dcube/ws`
  // wsUrl: `${location.protocol === 'https:' ? 'wss' : 'ws'}://**************:8999/ws`
}
