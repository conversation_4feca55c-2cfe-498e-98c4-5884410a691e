<template>
  <div class="cube-flow-wrap">
    <div class="top-search-area">
      <el-form ref="queryForm" size="small" :model="queryParams" :inline="true">
        <el-form-item label="流程节点" prop="dimName">
          <el-input
            v-model="queryParams.dimName"
            placeholder="请输入节点名称"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >查询</el-button>
          <el-button
            icon="el-icon-refresh"
            size="mini"
            @click="resetQuery"
          >重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <vxe-table
        ref="tableRef"
        border="inner"
        :column-config="{resizable: true}"
        :tree-config="{transform: true, rowField: 'dimId', parentField: 'parentId',expandAll: true}"
        :data="tableData"
      >
        <vxe-column field="dimName" title="节点名称" tree-node />
        <vxe-column title="审批人">
          <template #default="{ row }">
            <a :class="{'approver-select-wrap':true,'approver-select-active':changeApproverRow.dimId===row.dimId}" @click="changeApproveUser(row)">{{ row.userName&&row.userName!=='' ? row.userName : '无' }}</a>
          </template>
        </vxe-column>
        <vxe-column title="归口维度">
          <template #default="{ row }">
            <el-select
              v-if="changeCentralizedRow.dimId===row.dimId&&row.isLeaf==='N'"
              ref="centralizedDimSelect"
              :value="changeCentralizedRow.centralizedDimId===''?'':changeCentralizedRow.centralizedDimId"
              size="small"
              @change="centralizedChange"
              @blur="blurCentralizedSelect"
            >
              <el-option
                v-for="item in centralizedList"
                :key="item.id"
                :label="item.dimDirectoryName"
                :value="item.id"
              />
              <el-option value="" label="无" />
            </el-select>
            <span v-else-if="changeCentralizedRow.dimId!==row.dimId&&row.isLeaf==='N'">
              <a
                class="centralized-select-wrap"
                @click="changeCentralized(row)"
              >{{ row.centralizedDimName||'无' }}</a>
              <el-button v-if="row.centralizedDimName" :class="{'centralized-node-btn-active':updateCentralizedNodesRow.dimId===row.dimId}" style="paddingLeft:revert;marginLeft:8px" size="mini" @click="onCentralizedUpdate(row)">归口节点维护</el-button>
            </span>
            <span v-else-if="row.isLeaf==='Y'">{{ row.centralizedDimName||'无' }}</span>
          </template>
        </vxe-column>
      </vxe-table>
      <div class="auxiliarywrap">
        <CubeApproveUser
          v-if="showApproveSelect"
          :approver-selected="changeApproverRow.userIds||[]"
          @saveAprrover="saveAprrover"
        />
        <CubeCentralizedNode
          v-if="showCentralizedNodes"
          :centralized-row="updateCentralizedNodesRow"
        />
      </div>
    </div>
  </div>
</template>

<script>
import CubeApproveUser from './cubeApproveUser.vue'
import CubeCentralizedNode from './cubeCentralizedNode.vue'
import { getDimsByCubeId, getDimTreeById, saveApproversCentralized } from '@/api/approve-progress.js'

export default {
  components: { CubeApproveUser, CubeCentralizedNode },
  props: {
    flowId: String,
    tableId: String
  },
  data() {
    return {
      queryParams: {
        dimName: undefined
      },
      tableData: [],
      showApproveSelect: false,
      changeApproverRow: {}, // 变更审批人行(选中高亮、传入组件)
      centralizedList: [],
      changeCentralizedRow: {}, // 变更归口维度行
      updateCentralizedNodesRow: {}, // 归口节点维护行(选中高亮、传入组件)
      showCentralizedNodes: false,
      expandAllTimer: null,
      changeCenTimer: null
    }
  },
  watch: {
    tableId: {
      handler(val) {
        this.getList()
        getDimsByCubeId(this.tableId).then(res => {
          this.centralizedList = res.data
        })
      },
      immediate: true
    }
  },
  created() {

  },
  methods: {
    blurCentralizedSelect() {
      this.clearTimer(this.changeCenTimer)
      this.changeCenTimer = setTimeout(() => {
        this.changeCentralizedRow = {}
      }, 100)
    },
    // 获取表格数据
    getList(targetId) {
      this.listLoading = true
      getDimTreeById({ ...this.queryParams, defineId: this.flowId })
        .then((res) => {
          if (res.code === 200) {
            console.log('维度树列表', res)
            this.tableData = res.data
            this.total = res.total
            this.setAllTreeExpand(targetId)
          }
        })
        .finally(() => {
          this.listLoading = false
        })
    },
    // 展开树并定位到当前节点
    setAllTreeExpand(id) {
      this.clearTimer(this.expandAllTimer)
      this.expandAllTimer = setTimeout(() => {
        this.$refs.tableRef.setAllTreeExpand(true)
        if (id) {
          const target = this.tableData.find(item => item.id === id)
          this.$refs.tableRef.setCurrentRow(target)
        }
      }, 10)
    },
    /** 查询按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    changeApproveUser(row) {
      this.changeApproverRow = row
      this.showCentralizedNodes = false
      this.updateCentralizedNodesRow = {}
      this.showApproveSelect = true
    },
    // 保存左侧维度审批人
    saveAprrover(selectedUserIds) {
      const params = {
        'centralizedDimId': this.changeApproverRow.centralizedDimId,
        'defineId': this.flowId,
        'dimId': this.changeApproverRow.dimId,
        'userId': selectedUserIds.join(','),
        id: this.changeApproverRow.id
      }
      saveApproversCentralized(params).then(res => {
        if (res.code && res.code !== 200) return this.$message.error(res.msg)
        this.showApproveSelect = false
        this.changeApproverRow = {}
        this.getList()
        this.$message.success('保存成功')
      }).catch(err => {
        this.$message.error('保存失败', err)
      })
    },
    changeCentralized(row) {
      this.changeCentralizedRow = row
      this.$nextTick(() => {
        this.$refs.centralizedDimSelect.focus()
      })
    },
    centralizedChange(value) {
      const params = {
        'centralizedDimId': value,
        'defineId': this.flowId,
        'dimId': this.changeCentralizedRow.dimId,
        'userId': this.changeCentralizedRow.userId,
        id: this.changeCentralizedRow.id
      }
      saveApproversCentralized(params).then(res => {
        if (res.code && res.code !== 200) return this.$message.error(res.msg)
        this.changeCentralizedRow = {}
        this.getList()
        this.$message.success('保存成功')
      }).catch(err => {
        this.$message.error('保存失败', err)
      })
    },
    onCentralizedUpdate(row) {
      this.updateCentralizedNodesRow = row
      this.showApproveSelect = false
      this.changeApproverRow = {}
      this.showCentralizedNodes = true
    }
  }
}
</script>

<style lang="scss">
.cube-flow-wrap .content{
  display: flex;
  >*{
    flex: 1;
  }
}
a.approver-select-wrap,a.centralized-select-wrap{
  text-decoration:underline;
  color:#004DE7;
  &.approver-select-active{
    color: #E6A23C;
  }
}
.centralized-node-btn-active{
  color:#E6A23C;
  border-color: #E6A23C;
}
</style>
