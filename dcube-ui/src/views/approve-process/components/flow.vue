<template>
  <div class="logic-flow-view">
    <!-- 辅助工具栏 -->
    <Control
      class="demo-control"
      v-if="lf"
      :lf="lf"
      @saveData="saveData"
    ></Control>
    <!-- 节点面板 -->
    <NodePanel v-if="lf" :lf="lf" :nodeList="nodeList"></NodePanel>
    <!-- 画布 -->
    <div id="LF-view" ref="container"></div>
    <!-- 属性面板 -->
    <el-dialog
      :title="`${clickNode&&clickNode.type==='polyline'?'配置流程路线':'配置流程节点'}`"
      :visible.sync="dialogVisible"
      append-to-body
      width="500px"
      @close="closeDialog">
      <PropertyDialog
        v-if="dialogVisible"
        :nodeData="clickNode"
        :lf="lf"
        :flowId="flowId"
        @setPropertiesFinish="closeDialog"
      ></PropertyDialog>
    </el-dialog>
  </div>
</template>
<script>
import LogicFlow from '@logicflow/core'
// const LogicFlow = window.LogicFlow
import { Menu, Snapshot, MiniMap } from '@logicflow/extension'
import '@logicflow/core/dist/style/index.css'
import '@logicflow/extension/lib/style/index.css'
import NodePanel from './LFComponents/NodePanel'
import Control from './LFComponents/Control'
import PropertyDialog from './PropertySetting/PropertyDialog'
import { nodeList } from './flowConfig'

import {
  registerStart,
  registerEnd,
  registerPolyline,
  registerTask,
} from './registerNode'
import {updateWorkflow} from '@/api/approve-progress.js'

export default {
  name: 'LF',
  components: { NodePanel, Control, PropertyDialog },
  props:{flowChartData:Object,flowId:String},
  data () {
    return {
      lf: null,
      nodeData: null,
      addClickNode: null,
      clickNode: null,
      dialogVisible: false,
      graphData: null,
      config: {
        background: {
          backgroundColor: '#f7f9ff',
        },
        grid: {
          size: 10,
          visible: false
        },
        keyboard: {
          enabled: true
        },
        edgeTextDraggable: true,
        hoverOutline: false,
      },
      moveData: {},
      nodeList,
    }
  },
  mounted () {
    this.$_initLf()
  },
  methods: {
    $_initLf () {
      // 画布配置
      const lf = new LogicFlow({
        ...this.config,
        plugins: [
          Menu,
          MiniMap,
          Snapshot
        ],
        container: this.$refs.container,
        isSilentMode: false,
      })
      this.lf = lf
      // 设置主题
      lf.setTheme({
        circle: {
          stroke: '#0070c0',
          strokeWidth: 1,
          outlineColor: '#88f',
        },
        rect: {
          stroke: '#0070c0',
          outlineColor: '#88f',
          strokeWidth: 1
        },
        polyline: {
          stroke: '#0070c0',
          hoverStroke: '#000000',
          selectedStroke: '#000000',
          outlineColor: '#88f',
          strokeWidth: 1
        },
        nodeText: {
          fontSize: 16,
          color: '#000000'
        },
        edgeText: {
          fontSize: 14,
          color: '#0A1F44',
          background: {
            fill: '#f7f9ff'
          }
        },
      })
      this.$_registerNode()
    },
    // 自定义
    $_registerNode () {
      registerStart(this.lf)
      registerEnd(this.lf)
      registerPolyline(this.lf)
      registerTask(this.lf)
      this.$_render()
    },
    $_render () {
      this.lf.render(this.flowChartData)
      this.$_LfEvent()
    },
    saveData () {
      const data = this.lf.getGraphData()
      console.log(JSON.stringify(data))
      updateWorkflow({id:this.flowId,flowchartJson:JSON.stringify(data)}).then(res=>{
        if (res.code === 200) {
            this.$message.success('保存成功');
        }
      })
    },
    $_LfEvent () {
      this.lf.on('node:dbclick', ({data}) => {
        console.log('node:dbclick', data)
        this.$data.clickNode = data
        this.$data.dialogVisible = true
      })
      this.lf.on('edge:dbclick', ({data}) => {
        console.log('edge:dbclick', data)
         this.$data.clickNode = data
          this.$data.dialogVisible = true
      })
      this.lf.on('node:mousemove', ({data}) => {
        console.log('node:mousemove')
        this.moveData = data
      })
      this.lf.on('connection:not-allowed', (data) => {
        this.$message({
          type: 'error',
          message: data.msg
        })
      })
    },
    mouseDownPlus (e, attributes) {
      e.stopPropagation()
      console.log('mouseDownPlus', e, attributes)
    },
    closeDialog () {
      this.$data.dialogVisible = false
    },
  }
}
</script>
<style>
.logic-flow-view {
  height: calc(100vh - 24px - 50px - 34px);
  position: relative;
}
.demo-title{
  text-align: center;
  margin: 20px;
}
.demo-control{
  position: absolute;
  top: 24px;
  right: 24px;
  z-index: 2;
}
#LF-view{
  width: 100%;
  height: 100%;
  outline: none;
}
.time-plus{
  cursor: pointer;
}
.add-panel {
  position: absolute;
  z-index: 11;
  background-color: white;
  padding: 10px 5px;
}
.el-drawer__body {
  height: 80%;
  overflow: auto;
  margin-top: -30px;
  z-index: 3;
}

@keyframes lf_animate_dash {
  to {
    stroke-dashoffset: 0;
  }
}
</style>

