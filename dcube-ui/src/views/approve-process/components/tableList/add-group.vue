<template>
  <div>
    <el-dialog :title="addGroupTitle" :visible="true" width="400px" :close-on-click-modal="false" @close="closeAddGroup">
      <el-form ref="groupForm" size="small" label-position="right" label-width="80px" :model="addGroupForm" :rules="rules">
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="addGroupForm.name" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeAddGroup">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleAddGroup">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// import { getList } from '@/api/base-table'

export default {
  props: {
    currentNodeId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    var checkName = (rule, value, callback) => {
      const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/
      if (!value) {
        return callback(new Error('请输入名称'))
      }
      if (value && !reg.test(value)) {
        return callback(new Error('名称只能为汉字、英文、数字、下划线的组合'))
      }
      callback()
    }
    return {
      addGroupForm: {
        name: ''
      },
      rules: {
        name: [
          { required: true, validator: checkName, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    addGroupTitle() {
      return this.currentNodeId ? '新增子组' : '新增分组'
    }
  },
  created() {
    // this.fetchData()
    this.resetForm()
  },
  methods: {
    // 重置分组弹框
    resetForm() {
      if (this.$refs.groupForm) {
        this.$refs.groupForm.resetFields()
      }
    },
    // 关闭分组弹框
    closeAddGroup() {
      this.$emit('close')
    },
    handleAddGroup() {
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          this.$emit('handleAddGroup', this.addGroupForm)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }

  }
}
</script>
<style lang="scss" scoped>

</style>
