<template>
  <el-dialog :title="dialogTitle" :visible="true" fullscreen class="full-dialog" @close="closeSubTable">
    <div>
      <div v-if="enableEditRow" class="btn-group">
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="addRow">
          <div class="btn-icon el-icon-plus" />
          <div class="btn-text">添加行</div>
        </div>
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="saveRowData">
          <div class="btn-icon el-icon-check" />
          <div class="btn-text">保存数据</div>
        </div>

        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="delRow">
          <div class="btn-icon el-icon-delete" />
          <div class="btn-text">删除行</div>
        </div>
        <div class="btn-box" @click="refreshTable">
          <div class="btn-icon el-icon-refresh" />
          <div class="btn-text">刷 新</div>
        </div>
        <div class="gap-line" />
        <div class="btn-box" @click="downloadTemplate">
          <div class="btn-icon el-icon-paperclip" />
          <div class="btn-text">下载导入模版</div>
        </div>
        <div class="btn-box" @click="impData">
          <div class="btn-icon el-icon-upload" />
          <div class="btn-text">导 入</div>
        </div>
        <div class="btn-box" @click="exportData">
          <div class="btn-icon el-icon-download" />
          <div class="btn-text">导 出</div>
        </div>
        <div class="gap-line" />
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="openBackUp">
          <div class="btn-icon el-icon-film" />
          <div class="btn-text">备份至数据库</div>
        </div>
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="openRevertData">
          <div class="btn-icon el-icon-coin" />
          <div class="btn-text">载入备份数据</div>
        </div>
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="openOriginData">
          <div class="btn-icon el-icon-receiving" />
          <div class="btn-text">载入源数据</div>
        </div>
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="showCreateData">
          <div class="btn-icon create-data" />
          <div class="btn-text">生成数据</div>
        </div>
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="releaseData">
          <div class="btn-icon el-icon-refresh-right" />
          <div class="btn-text">释放内存</div>
        </div>
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="executeRule">
          <div class="btn-icon el-icon-caret-right" />
          <div class="btn-text">执行规则</div>
        </div>
      </div>
      <div class="table-list">
        <div v-if="dynamicSearchArr.length" class="search-container">
          <!-- 动态查询条件 -->
          <div class="left">
            <el-form :inline="true" class="search-form" size="mini">
              <div class="left-area">
                <el-form-item v-for="item in dynamicSearchArr" :key="item.columnCode" :label="item.name">
                  <div v-if="(item.storageType === 'VARCHAR'||item.storageType === null)&&item.dicFlag==='N'" class="custom-control">
                    <vxe-select v-model="item.prefixVal" size="mini">
                      <vxe-option v-for="opt in searchOpts2" :key="opt.value" :value="opt.value" :label="opt.label" />
                    </vxe-select>
                    <vxe-input v-model="item.value" size="mini" type="text" />
                  </div>
                  <div v-if="numType.includes(item.storageType)" class="custom-control">
                    <vxe-select v-model="item.prefixVal" size="mini">
                      <vxe-option v-for="opt in searchOpts1" :key="opt.value" :value="opt.value" :label="opt.label" />
                    </vxe-select>
                    <vxe-input v-model="item.value" :type="item.storageType==='DOUBLE'?'number':'integer'" size="mini" />
                  </div>
                  <div v-if="item.storageType === 'DATE'" class="custom-control">
                    <vxe-select v-model="item.prefixVal" size="mini">
                      <vxe-option v-for="opt in searchOpts1" :key="opt.value" :value="opt.value" :label="opt.label" />
                    </vxe-select>
                    <vxe-input
                      v-model="item.value"
                      size="mini"
                      type="date"
                      placeholder="选择日期"
                    />
                  </div>
                  <vxe-select v-if="item.dicFlag==='Y'" v-model="item.value" size="mini">
                    <vxe-option v-for="opt in item.dicItems" :key="opt.dicCode" :value="opt.dicCode" :label="opt.dicLabel" />
                  </vxe-select>
                </el-form-item>
              </div>
              <div class="right-area">
                <el-form-item>
                  <el-button size="mini" type="primary" icon="el-icon-search" @click="searchTable">查询</el-button>
                  <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
              </div>

            </el-form>
          </div>

        </div>
        <div class="table-box" :class="{'no-search':dynamicSearchArr.length===0}">
          <vxe-table
            :key="randomKey"
            ref="xTable"
            size="mini"
            height="auto"
            class="dynamic-table"
            border
            keep-source
            :cell-class-name="cellClassName"
            :data="viewTable"
            :loading="loading"
            :column-config="{isCurrent: true, isHover: true,useKey:true,resizable: true}"
            :row-config="{isCurrent: true, isHover: true}"
            :edit-config="{trigger: 'click', mode: 'cell',showStatus: true,showUpdateStatus:true,enabled:enableEditRow}"
            :scroll-y="{scrollToTopOnChange:true}"
            :mouse-config="{selected: true}"
            :sort-config="{multiple: true}"
            :menu-config="{}"
            :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true, isChecked: true}"
            @current-change="currentChangeEvent"
            @menu-click="contextMenuClickEvent"
            @resizable-change="colResizeChange"
            @sort-change="sortChangeEvent"
            @edit-actived="editActived"
            @cell-click="cellClickEvent"
          >
            <vxe-column
              v-for="(config,index) in tableMetaJson"
              v-show="config.operationType!=='DELETE'"
              :key="config.code"
              :params="config"
              :field="config.code||String(config.no)"
              :title="config.name"
              :align="formatAlign(config.dataFormat)"
              :edit-render="{autofocus: '.vxe-input--inner', autoselect: true,enabled:index>0&&(!config.parentTableEntry)&&!config.computationRule&&!disableEditRow&&(config.systemKey!=='USER'&&config.systemKey!=='ORG')}"
              show-header-overflow
              show-overflow="title"
              :width="config.columnWidth||150"
              sortable
            >
              <template #header="{ column }">
                <div slot="reference" class="flex-vertical-center custom-col" @click="clickCol(column)">
                  <div class="flex-vertical-center col-left">
                    <i v-if="index===0" class="iconfont icon-yuechi1" />
                    <i v-if="config.systemKey==='ORG' || config.systemKey==='USER'" class="vxe-icon-user" />
                    <i v-if="config.condition===true" class="iconfont icon-shaixuan" />
                    <div class="flex-vertical-center">
                      <span class="custom-ellipsis" :style="{width:`${allColWArr[index]-(config.condition===null?100:110)}px`}">{{ config.name }}</span>
                    </div>
                  </div>
                  <div v-if="enableEditCol" class="set-pannel flex-vertical-center">
                    <i v-if="index>(childFlag==='Y'?1:0)" class="iconfont icon-appjisuanqi" @click="openCalc(config)" />
                    <el-popover
                      placement="bottom-start"
                      width="200"
                      trigger="click"
                    >
                      <div class="popver-box">
                        <!-- 列名 -->
                        <div class="row-cell">
                          <vxe-input v-model="config.name" size="small" />
                        </div>
                        <!-- 数据格式 -->
                        <div class="row-cell">
                          <el-cascader
                            v-model="config.dataFormatId"
                            size="small"
                            placeholder="选择数据格式"
                            :options="formatTree"
                            :props="formatProps"
                            :show-all-levels="false"
                            @change="changeFormatTree"
                          />
                        </div>
                        <!-- 宽度设置 -->
                        <!-- <div class="row-cell">
                        <vxe-input v-model="config.width" type="number" size="small" :min="minCount" placeholder="列宽(字符数:最小10位)" />
                      </div> -->
                        <!-- 筛选条件 -->
                        <div class="row-cell">
                          <el-select v-model="config.condition" size="small" placeholder="选择筛选条件">
                            <el-option
                              v-for="item in filterOption"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </div>
                        <!-- 关联系统参数 -->
                        <div class="row-cell">
                          <el-select v-model="config.systemKey" size="small" placeholder="选择关联参数" @change="changeSystemKey(config)">
                            <el-option
                              v-for="item in relateOption"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </div>
                        <div v-if="config.systemKey==='DICT'" class="row-cell">
                          <el-select v-model="config.systemValue" size="small" placeholder="选择字典">
                            <el-option
                              v-for="item in dictType"
                              :key="item.id"
                              :label="item.groupInstanceName"
                              :value="item.id"
                            />
                          </el-select>
                        </div>
                        <div class="row-cell setcol-btn">
                          <el-button class="del-btn" type="text" @click="updateCol(config)">确  认</el-button>
                        </div>

                      </div>
                      <i :slot="disableEditCol?'':'reference'" style="margin-top:6px" class="el-icon-setting font-15" />
                    </el-popover>
                  </div>

                </div>
              </template>
              <template #edit="{ row }">
                <vxe-input v-if="(config.dataFormat.storageType === 'VARCHAR'||config.dataFormat.storageType === null)&&config.dataFormat.dicFlag==='N'" v-model="row[config.code]" type="text" />
                <vxe-input v-if="numType.includes(config.dataFormat.storageType)" v-model="row[config.code]" :type="config.dataFormat.storageType==='DOUBLE'?'number':'integer'" />
                <vxe-input
                  v-if="config.dataFormat.storageType === 'DATE'"
                  v-model="row[config.code]"
                  size="mini"
                  type="date"
                  placeholder="选择日期"
                />
                <vxe-select v-if="config.dataFormat.dicFlag==='Y'" v-model="row[config.code]">
                  <vxe-option v-for="item in config.dicItems" :key="item.dicCode" :value="item.dicCode" :label="item.dicLabel" />
                </vxe-select>
              </template>
            <!-- <template #default="{ row }">
              <div class="default-cell" @click="cellClick(row)">{{ row[config.code] }}</div>
            </template> -->
            </vxe-column>
            <template #empty>
              <table-empty />
            </template>
          </vxe-table>
          <div v-if="enableEditCol" class="add-col">
            <el-button :disabled="disableEditCol" type="primary" circle icon="el-icon-plus" size="mini" @click="addColumn" />
            <el-button :disabled="disableEditCol" type="danger" style="margin:10px 0 0 0" circle icon="el-icon-minus" size="mini" @click="delColumn" />
          <!-- <el-tooltip class="item" effect="dark" content="保存列" placement="bottom-end">
            <el-button type="primary" circle icon="el-icon-check" size="mini" @click="saveColumn" />
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="删除列" placement="bottom-end">
            <el-button type="danger" circle icon="el-icon-minus" size="mini" @click="delColumn" />
          </el-tooltip> -->
          </div>

        </div>
        <div class="table-footer" :style="{'justify-content':processInfo||exportInfo.msg?'space-between':'flex-end'}">
          <div v-if="processInfo&&processInfo.status!=='ERROR'" class="process-info">
            <span>{{ processInfo.taskName }}：</span>
            <el-progress :percentage="formatProgress(processInfo.progress)" size="mini" />
            <span v-if="processInfo.status==='COMPLETE'" style="margin-left: 10px;">{{ processInfo.cost+'ms' }}</span>
          </div>
          <div v-if="exportInfo.msg" class="process-info">
            <span>{{ exportInfo.msg }}</span>
            <el-progress :percentage="exportInfo.process" size="mini" />
            <el-button v-if="exportInfo.extra" type="text" size="mini" class="download-link" @click="commonDownLoad">点击下载文件</el-button>
            <el-progress v-if="showDownloadProcess" :percentage="downloadPercentage" size="mini" />
          </div>
          <div class="right">
            <div v-if="sumavgObj.a && sumavgObj.s" class="sum-avg flex-vertical-center">
              <div class="flex-vertical-center" style="margin-right:20px">
                <div class="label">求和：</div>
                <div class="val">{{ sumavgObj.s }}</div>
              </div>
              <div class="flex-vertical-center">
                <div class="label">平均：</div>
                <div class="val">{{ sumavgObj.a }}</div>
              </div>
            </div>
            <el-pagination
              :current-page="pageInfo.currentPage"
              :page-sizes="[20,50,100]"
              :page-size="pageInfo.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalRecords"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
      <!-- 备份至数据库 -->
      <el-dialog append-to-body title="备份至数据库" :visible.sync="showBackUp" width="400px" :close-on-click-modal="false" @close="closeBackUp">
        <el-form ref="backUpForm" label-position="right" label-width="80px" size="small" :model="backUpForm" :rules="backUpRules">
          <el-form-item label="版本名称" prop="name">
            <el-input ref="backUpInput" v-model="backUpForm.name" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="closeBackUp">取 消</el-button>
          <el-button size="mini" type="primary" @click="handleBackUp">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 载入备份数据 -->
      <el-dialog append-to-body title="载入备份数据" :visible.sync="showRevert" width="400px" :close-on-click-modal="false" @close="closeRevert">
        <el-form ref="revertForm" label-position="right" label-width="80px" size="small" :model="revertForm">
          <el-form-item label="版本名称" prop="name">
            <el-select v-model="revertForm.name" class="del-select" placeholder="请选择">
              <el-option
                v-for="item in versionList"
                :key="item.id"
                class="delversion-option"
                :label="item.version"
                :value="item.id"
              >
                <span style="float: left">{{ item.version }}</span>
                <span class="el-icon-close del-icon" @click.prevent="(e)=>delVersion(e,item)" />
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="closeRevert">取 消</el-button>
          <el-button size="mini" type="primary" @click="handleRevert">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 载入源数据 -->
      <el-dialog append-to-body title="载入源数据" :visible.sync="showOrigin" width="400px" :close-on-click-modal="false" @close="closeOrigin">
        <el-form ref="originForm" label-position="right" label-width="90px" size="small" :model="originForm">
          <el-form-item label="数据源名称" prop="name">
            <el-select v-model="originForm.name" :disabled="true" class="w-full" placeholder="请选择">
              <el-option
                v-for="item in originList"
                :key="item.id"
                :label="item.viewName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="closeOrigin">取 消</el-button>
          <el-button size="mini" type="primary" @click="handleOrigin">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 计算规则 -->
      <el-dialog append-to-body title="计算规则" :visible.sync="calcVisible" width="1100px" :close-on-click-modal="false" @close="closeCalc">
        <!-- 快捷输入选项 -->
        <div class="quick-input">
          <!-- 条件分支 -->
          <div class="control-box">
            <div class="control-btn if" @click="quickSelectIf">条件分支(if)</div>
          <!-- <div class="control-select">
            <el-select v-model="quickForm['expressIf']" size="mini" placeholder="请选择" @change="(val)=>quickSelect(val,'expressIf')">
              <el-option v-for="item in quickInputOpts.expressIf" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </div> -->
          </div>
          <!-- 函数 -->
          <div class="control-box">
            <div class="control-btn">函数(f)</div>
            <div class="control-select">
              <el-cascader
                v-model="quickForm['func']"
                size="mini"
                :options="quickInputOpts.func"
                :props="{ expandTrigger: 'hover',value:'funcVal',label:'funcName',children:'ruleFuncList' }"
                @change="(val)=>quickSelect(val,'func')"
              />
            </div>
          </div>
          <!-- 本表 -->
          <div class="control-box">
            <div class="control-btn">本表(o)</div>
            <div class="control-select">
              <el-select v-model="quickForm['o']" size="mini" placeholder="请选择" @change="(val)=>quickSelect(val,'o')">
                <el-option v-for="item in quickInputOpts.o" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
            </div>
          </div>
          <!-- 其他表 -->
          <div class="control-box">
            <div class="control-btn">其他表(t)</div>
            <div class="control-select">
              <el-cascader
                v-model="quickForm['t']"
                size="mini"
                :options="quickInputOpts.t"
                :props="{ expandTrigger: 'hover' ,value:'value',label:'name',children:'children' }"
                @change="(val)=>quickSelect(val,'t')"
              />
            </div>
          </div>
          <!-- 父表 -->
          <div class="control-box">
            <div class="control-btn">父表(p)</div>
            <div class="control-select">
              <el-select v-model="quickForm['p']" size="mini" placeholder="请选择" @change="(val)=>quickSelect(val,'p')">
                <el-option v-for="item in quickInputOpts.p" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
            </div>
          </div>
          <!-- 子表 -->
          <div class="control-box">
            <div class="control-btn">子表(c)</div>
            <div class="control-select">
              <el-cascader
                v-model="quickForm['c']"
                size="mini"
                :options="quickInputOpts.c"
                :props="{ expandTrigger: 'hover',value:'value',label:'name',children:'children' }"
                @change="(val)=>quickSelect(val,'c')"
              />
            </div>
          </div>
        </div>
        <el-form ref="calcForm" label-position="right" label-width="0px" size="small" :model="calcForm" :rules="calcRules">
          <el-form-item label="" prop="rule">
            <el-input ref="ruleInput" v-model="calcForm.rule" type="textarea" rows="14" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="closeCalc">取 消</el-button>
          <el-button size="mini" type="primary" @click="handleCalc">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 选择人员 -->
      <select-user ref="selectUser" @selectUser="handleSelectUser" />
      <!-- 修改痕迹 -->
      <el-dialog append-to-body title="修改痕迹" :visible.sync="showModifyHis" width="600px" :close-on-click-modal="false" @close="hideModifyHis">
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="hideModifyHis">取 消</el-button>
          <el-button size="mini" type="primary" @click="hideModifyHis">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <create-data v-if="showCreateDataDia" :cur-table-detail="curTableDetail" :table-meta-json="tableMetaJson" @closeCreateData="closeCreateData" @createDataSuccess="createDataSuccess" />
    <grand-table v-if="grandTableShow" :table-permission="tablePermission" :dict-type="dictType" :rel-sub-table-id="relGrandTableId" :parent-table-id="curTableId" :parent-table-key-val="subTableKeyVal" :two-col="childTwoCol" @closeGrandTable="closeGrandTable" />
  </el-dialog>

</template>

<script>
import moment from 'moment'
import Sortable from 'sortablejs'
// import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { cloneDeep } from 'lodash'
import calcRules from '../rule'
import { getViewList } from '@/api/system/data-view'
import { createExcel } from '@/utils/index'
import {
  // downloadExcel,
  executeRule,
  exportTable,
  loadData,
  delVersionApi,
  getVersionList,
  backUpData,
  createId,
  // get2dTable,
  getSubTable,
  getTableDetail,
  saveCol,
  addRowData,
  updateRowData,
  calcSumAvg,
  deleteRowApi,
  revertData,
  releaseData,
  saveRule,
  getTaskProcess,
  getQuickInput,
  getTaskInfoByTableId,
  downloadTemplate,
  impData,
  getTableKey
} from '@/api/base-table'
import selectUser from '@/components/SelectUser'
import grandTable from './grand-table.vue'
import createData from './create-data.vue'
import { spiltDownload } from '@/api/system/approve-role'
import VXETable from 'vxe-table'
export default {
  components: {
    selectUser,
    grandTable,
    createData
  },
  props: {
    relSubTableId: {
      type: Number,
      default: -1
    },
    parentTableId: {
      type: Number,
      default: 0
    },
    parentTableLevel: {
      type: Number,
      default: 0
    },
    parentTableKeyVal: {
      type: String,
      default: ''
    },
    dictType: {
      type: Array,
      default: () => []
    },
    twoCol: {
      type: Array,
      default: () => []
    },
    tablePermission: {
      type: [Object, null],
      default: null
    }
  },
  data() {
    return {
      searchOpts1: Object.freeze([{
        label: '等于',
        value: '0'
      }, {
        label: '小于',
        value: '-1'
      }, {
        label: '小于等于',
        value: '-2'
      }, {
        label: '大于',
        value: '1'
      }, {
        label: '大于等于',
        value: '2'
      }]),
      searchOpts2: Object.freeze([{
        label: '精确',
        value: '0'
      }, {
        label: '模糊',
        value: '-1'
      }]),
      orgUserDict: Object.freeze(['ORG', 'USER', 'DICT']),
      deptTree: this.$store.getters.deptTree,
      numType: Object.freeze(['INTEGER', 'DOUBLE']),
      sumavgObj: {
        a: null,
        s: null
      },
      loading: false,
      // 动态查询数组
      dynamicSearchArr: [],
      orgArr: [],
      customTypeArr: [],
      tableMetaJson: [], // 页面上显示的列数据
      currentNo: 1,
      // 显示值表格
      viewTable: [],
      // 原始值表格
      originTable: [],
      pageInfo: {
        currentPage: 1,
        pageSize: 20
      },
      totalRecords: 0,
      calcVisible: false,
      calcForm: {
        rule: ''
      },
      calcRules: {},
      disableEditRow: false,
      disableEditCol: false,
      filterOption: Object.freeze([{
        label: '不作为查询条件',
        value: null
      }, {
        label: '作为查询条件',
        value: true
      }]),
      relateOption: Object.freeze([{
        label: '不关联系统参数',
        value: null
      }, {
        label: '关联机构',
        value: 'ORG'
      }, {
        label: '关联人员',
        value: 'USER'
      }]),
      curRow: null,
      curCol: null,
      // 字典集
      dictMap: {},
      // 选择人员id和nickName集合,方便单元格格式化显示
      userMap: {},
      // 机构的id和name集合,方便单元格格式化显示
      orgMap: {},
      sortable2: null,
      tableClientW: 0,
      tableOffsetW: 0,
      // 单元格右键菜单
      tableMenu: {
        body: {
          options: [
            [
              { code: 'edit', name: '修改痕迹', prefixIcon: 'vxe-icon-copy', disabled: false }
            ]
          ]
        }
      },
      // 查看修改痕迹
      showModifyHis: false,
      allColWArr: [],
      formatTree: this.$store.getters.formatListTree,
      formatList: this.$store.getters.formatList,
      formatProps: { emitPath: false, expandTrigger: 'hover', value: 'id', label: 'groupInstanceName' },
      defaultFormatId: '3b583866df798d08b8dd811f1f14fe0a',
      // 排序参数集合
      sortList: [],
      // 查询参数集合
      filterList: [],
      addKeyTimer: null,
      // 记录行编辑过的列code
      editRowColMap: {},
      relateSystemCol: null,
      randomKey: Math.round(Math.random() * 1000 + 1),
      processInfo: null,
      processTimer: null,
      // 刷新表格时默认更新查询条件(如果是点击查询按钮则不更新查询条件)
      updateSearch: true,
      // 规则快捷输入
      quickInputOpts: {},
      quickForm: { o: '', p: '', expressIf: '', t: [], c: [], func: [] },
      opifKey: Object.freeze(['o', 'p']),
      showBackUp: false,
      backUpForm: { name: '' },
      backUpRules: {
        name: [{
          required: true,
          trigger: 'blur',
          message: '请输入版本名称'
        }]
      },
      showRevert: false,
      revertForm: {
        name: ''
      },
      versionList: [],
      originList: [],
      showOrigin: false,
      originForm: {
        name: ''
      },
      validStatus: ['START', 'COMPLETE'],
      // 数据源id
      curViewId: '',
      scrollTimer: null,
      user: this.$store.getters.user,
      socket: null,
      exportInfo: {
        msg: '',
        process: 0,
        extra: ''
      },
      operateTime: '',
      localhost: window.location.host,
      taskInfoTimer: null,
      requestCount: 0,
      showDownloadProcess: false,
      downloadPercentage: 0,
      filesCurrentPage: 0, // 文件开始偏移量
      fileFinalOffset: 0, // 文件最后偏移量
      stopRecursiveTags: true, // 停止递归标签，默认是true 继续进行递归
      contentList: [], // 文件流数组
      fileName: '',
      // 子表弹框
      grandTableShow: false,
      curTableDetail: {},
      relGrandTableId: '',
      parentTableKeyCode: '',
      subTableKeyVal: '',
      childTwoCol: [],
      showCreateDataDia: false
    }
  },
  computed: {
    childFlag() {
      return this.curTableDetail.childFlag
    },
    dialogTitle() {
      return this.curTableDetail.tableName || '打开子表'
    },
    userId() {
      return this.user.userId
    },
    curTableId() {
      return this.curTableDetail && this.curTableDetail.id || ''
    },
    defaultFormat() {
      return this.formatList.filter(item => item.id === this.defaultFormatId)[0]
    },
    // 是否能编辑表格数据
    enableEditRow() {
      return (this.tablePermission && this.tablePermission.dataEdition === 1) || false
    },
    // 是否能定义列
    enableEditCol() {
      return (this.tablePermission && this.tablePermission.tableDefinition === 1) || false
    }
  },
  watch: {
    relSubTableId: {
      handler(newVal, oldVal) {
        if (newVal) {
          // 初始化请求条件
          this.initTableParams()
          this.fetchData(newVal)
          this.getTaskInfoByTableId()
          this.loopTaskInfo()
          this.getParentTableKey()
        }
      },
      immediate: true,
      deep: true
    },
    tableMetaJson: {
      handler(newVal, oldVal) {
        if (newVal.length && this.updateSearch) {
          this.dynamicSearchArr = []
          newVal.forEach(col => {
            const { name, code, condition, dataFormat, dicItems, systemKey } = col
            const { storageType, dicFlag } = dataFormat
            if (condition) {
              this.dynamicSearchArr.push({
                prefixVal: '0',
                name,
                columnCode: code,
                value: '',
                storageType,
                dicFlag,
                dicItems,
                systemKey
              })
            }
          })
        }
      },
      immediate: true,
      deep: true
    },
    sortList: {
      handler(newVal) {
        if (newVal && newVal.length) {
          this.refreshTable()
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.calcRules = calcRules.createCalcRule(this.calcForm, this.relSubTableId)
  },
  beforeDestroy() {
    if (this.sortable2) {
      this.sortable2.destroy()
    }
    if (this.addKeyTimer) {
      window.clearTimeout(this.addKeyTimer)
      this.addKeyTimer = null
    }
    this.clearTaskInfoTimer()
    this.clearProcessTimer()
    this.clearScrollTimer()
  },
  methods: {
    formatAlign(val) {
      let align = 'left'
      if (val) {
        align = val.contentAlign === 'middle' ? 'center' : val.contentAlign
      }
      return align
    },
    showCreateData() {
      this.showCreateDataDia = true
    },
    closeCreateData() {
      this.showCreateDataDia = false
    },
    createDataSuccess() {
      this.closeCreateData()
      this.refreshTable()
    },
    // 递归树列表
    listToTree(list) {
      const obj = {}
      list.forEach(item => { obj[item.id] = item })
      const res = []
      list.forEach(item => {
        const parent = obj[item.parentId]
        // 如果parent存在则item是叶子节点，否则就是根节点
        if (parent) {
          parent.children = parent.children || []
          parent.children.push(item)
        } else {
          res.push(item)
        }
      })
      return res
    },
    cellClassName({ row, column }) {
      const { params, field } = column
      const { parentTableEntry } = params || {}
      if (parentTableEntry && row[field]) return 'subtable-cell'
      return null
    },
    // 点击单元格,如果是入口列则打开子表
    cellClickEvent({ row, column }) {
      console.log('点击单元格', row, column)
      const { params } = column
      const { parentTableEntry, relSubTableId } = params || {}
      if (parentTableEntry && row[column.field] !== '') {
        this.grandTableShow = true
        this.relGrandTableId = relSubTableId
        this.subTableKeyVal = row[this.parentTableKeyCode]
        // 获取前两列
        this.childTwoCol = [row[this.tableMetaJson[0].code], row[this.tableMetaJson[1].code]]
      }
    },
    // 获取父表主键
    getParentTableKey() {
      getTableKey({ id: this.relSubTableId }).then(res => {
        if (res.code === 200) {
          this.parentTableKeyCode = res.data?.code || ''
        }
      })
    },
    closeSubTable() {
      this.$emit('closeSubTable')
    },
    closeGrandTable() {
      this.grandTableShow = false
    },
    commonDownLoad() {
      const filePath = this.exportInfo.extra
      var _this = this
      // 下载地址
      const chunkSize = 1024 * 1024 * 2 // 单个分段大小，这里测试用10M
      let filesTotalSize = chunkSize // 安装包总大小，默认10M
      let filesPages = 1 // 总共分几段下载
      // 计算百分比之前先清空上次的
      if (_this.downloadPercentage === 100) {
        _this.downloadPercentage = 0
        _this.filesCurrentPage = 0
        _this.contentList = []
      }
      const sentAxios = (num) => {
        let rande = chunkSize

        if (num) {
          rande = `${(num - 1) * chunkSize + 2}-${num * chunkSize + 1}`
        } else {
          // 第一次0-1方便获取总数，计算下载进度，每段下载字节范围区间
          rande = '0-1'
        }

        const headers = {
          range: rande
        }
        // 测试用,上线根据项目实际修改
        const params = {
          filePath
        }
        this.showDownloadProcess = true
        spiltDownload(params, headers).then((response) => {
          // 检查了下才发现，后端对文件流做了一层封装，所以将content指向response.data即可
          const content = response.data
          // 截取文件总长度和最后偏移量
          const result = response.headers['content-range'].split('/')
          // 获取文件总大小，方便计算下载百分比 减去第一次获取总数
          filesTotalSize = result[1] - 2
          // 获取最后一片文件位置，用于断点续传
          _this.fileFinalOffset = result[0].split('-')[1]
          // 计算总共页数，向上取整
          filesPages = Math.ceil(filesTotalSize / chunkSize)
          // 文件流数组
          // _this.contentList.push(content);
          _this.contentList[num] = content
          // 计算下载百分比  当前下载的片数/总片数
          if (_this.stopRecursiveTags) {
            _this.downloadPercentage = Number((((_this.contentList.length - 1) / filesPages) * 100).toFixed(2))
          }
          // 递归获取文件数据(判断是否要继续递归)
          if (_this.filesCurrentPage < filesPages && _this.stopRecursiveTags) {
            _this.filesCurrentPage++
            sentAxios(_this.filesCurrentPage)
            // 结束递归
            return
          }
          // 递归标签为true 才进行下载
          if (_this.stopRecursiveTags) {
            // 文件名称
            _this.fileName = decodeURIComponent(response.headers['fname'])

            // 构造一个blob对象来处理数据
            const blob = new Blob(_this.contentList)
            // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
            // IE10以上支持blob但是依然不支持download
            if ('download' in document.createElement('a')) {
              // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = _this.fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else {
              // 其他浏览器
              navigator.msSaveBlob(blob, _this.fileName)
            }
          }
        })
          .catch(function(error) {
            console.log(error)
          })
      }
      // 第一次获取数据方便获取总数
      sentAxios(_this.filesCurrentPage)
    },
    initWs() {
      if (typeof (WebSocket) === 'undefined') {
        alert('您的浏览器不支持socket')
      } else {
        // 实例化socket 111是固定的用户id,正式环境直接获取当前登录用户id
        this.socket = new WebSocket(`${this.global.wsUrl}`)
        this.global.setWs(this.socket)
        // 监听socket连接
        this.socket.onopen = () => {
          this.sendMsg()
        }

        // 监听socket错误信息
        this.socket.onerror = () => {
          console.error('连接错误')
        }
        // 监听socket消息
        this.socket.onmessage = (msg) => {
          this.getWsMsg(msg)
        }
        // 监听socket关闭信息
        this.socket.onclose = (e) => {
          console.error('socket已经关闭')
          console.error(e)
        }
      }
    },
    sendMsg() {
      this.socket.send(JSON.stringify({ userId: this.user.userId }))
    },
    getWsMsg(res) {
      const data = res && res.data
      if (data && !data.includes('服务器')) {
        const progressInfo = JSON.parse(res.data)
        console.log('websocket消息', progressInfo)
        const { process, timestamp } = progressInfo || {}
        if (String(this.operateTime) === timestamp) {
          this.exportInfo = progressInfo
          console.log(111, this.exportInfo)
          this.exportInfo.process = Number((100 * (process)).toFixed(0))
        }
      }
    },
    executeRule() {
      const params = new FormData()
      params.append('tableId', this.curTableId)
      executeRule(params).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg)
          this.loopTaskProcess(res.data.taskId)
        }
      })
    },
    // 下载导入模版
    downloadTemplate() {
      const params = {
        tableId: this.curTableId
      }
      downloadTemplate(params).then(res => {
        createExcel(res, this.curTableDetail.tableName + '.xlsx')
      })
    },
    // 导入
    async impData() {
      try {
        const { file } = await VXETable.readFile({
          types: ['xlsx', 'xls']
        })
        console.log('上传文件', file)
        const params = new FormData()
        params.append('file', file)
        params.append('tableId', this.curTableId)
        impData(params).then(res => {
          if (res.code === 200) {
            this.$message.success('上传成功')
          } else {
            this.$message.error(res.msg)
          }
        }).finally(() => {
          this.refreshTable()
        })
      } catch (e) {
        console.log(e)
      }
    },
    // 导出
    exportData() {
      this.initWs()
      if (this.viewTable.length === 0) {
        this.$message.error('表格无数据，不能导出')
        return
      }
      this.operateTime = new Date().getTime()
      const params = {
        ...this.pageInfo,
        tableId: this.curTableId,
        sortList: this.sortList,
        filterList: this.filterList,
        userId: this.userId,
        timestamp: this.operateTime
      }
      exportTable(params).then(res => {
        console.log(res, 222)
        if (res.code === 200) {
          this.$message.success('正在导出,请稍候')
        }
      })
    },
    initTableParams() {
      this.sortList = []
      this.pageInfo = {
        currentPage: 1,
        pageSize: 20
      }
    },
    formatProgress(progress) {
      let progressCount = 0
      if (progress && progress.includes('%')) {
        progressCount = Number(progress.split('%')[0])
      }
      return progressCount
    },
    getTaskInfoByTableId() {
      getTaskInfoByTableId({ tableId: this.relSubTableId }).then(res => {
        if (!res.data) {
          this.clearTaskInfoTimer()
          return
        }
        this.requestCount++
        const { status, msg, taskName } = res.data
        this.processInfo = status === 'START' ? res.data : null
        // 任务未完成前不能编辑表格
        if (status === 'ERROR' || status === 'COMPLETE') {
          this.disableEditRow = false
          this.disableEditCol = false
          if (status === 'ERROR') {
            this.$message.error(msg)
          }
          if (status === 'COMPLETE' && this.requestCount > 1) {
            this.$message.success(`${taskName}完成`)
            this.refreshTable()
          }
          this.clearTaskInfoTimer()
        } else {
          this.disableEditRow = true
          this.disableEditCol = true
        }
      })
    },
    // 获取规则快捷输入
    getQuickInput(id) {
      getQuickInput({ tableId: id }).then(res => {
        if (res.code === 200) {
          this.quickInputOpts = this.formatQuickData(res.data)
        }
      })
    },
    // 处理规则快捷输入数据
    formatQuickData(data) {
      const newData = cloneDeep(data)
      for (const p in newData) {
        newData[p] = this.recursionArr(newData[p], p)
      }
      return newData
    },
    recursionArr(arr, p) {
      arr.forEach(item => {
        if (p !== 'func') {
          if (item.children.length === 0) {
            delete item.children
            return
          } else {
            this.recursionArr(item.children, p)
          }
        } else {
          if (item.ruleFuncList.length === 0) {
            delete item.ruleFuncList
            return
          } else {
            this.recursionArr(item.ruleFuncList, p)
          }
        }
      })
      return arr
    },
    formatKey(key) {
      let str = '本表'
      switch (key) {
        case 'o':
          str = '本表'
          break
        case 'p':
          str = '父表'
          break
        case 'c':
          str = '子表'
          break
        case 't':
          str = '其他表'
          break
        case 'expressIf':
          str = 'if表达式'
          break
        case 'func':
          str = '函数'
          break
      }
      return str
    },
    // 选中if
    quickSelectIf() {
      const { rule } = this.calcForm
      const expressIf = this.quickInputOpts['expressIf'][0].value
      if (rule) {
        this.$confirm('if语句为基础结构规则，是否清空现有语句后添加if语句？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.calcForm.rule = expressIf
        })
      } else {
        this.calcForm.rule = expressIf
      }
    },
    quickSelect(val, key) {
      var tempInput = document.createElement('input')
      document.body.appendChild(tempInput)
      const validVal = this.opifKey.includes(key) ? val : val[1]
      tempInput.setAttribute('value', validVal)
      tempInput.select()
      document.execCommand('copy')
      document.body.removeChild(tempInput)
      this.$message.success('复制成功，请使用Ctrl+V或鼠标右键粘贴到输入框内')
      this.initQuickIpt()
    },
    clearProcessTimer() {
      if (this.processTimer) {
        window.clearInterval(this.processTimer)
        this.processTimer = null
      }
    },
    clearTaskInfoTimer() {
      if (this.taskInfoTimer) {
        window.clearInterval(this.taskInfoTimer)
        this.taskInfoTimer = null
        this.requestCount = 0
      }
    },
    clearScrollTimer() {
      if (this.scrollTimer) {
        window.clearInterval(this.scrollTimer)
        this.scrollTimer = null
      }
    },
    getTaskProcess(taskId) {
      getTaskProcess({ taskId }).then(res => {
        if (res.code === 200) {
          this.processInfo = res.data
          const { status, msg } = res.data
          // 任务未完成前不能编辑表格
          if (status === 'ERROR' || status === 'COMPLETE') {
            this.disableEditRow = false
            this.disableEditCol = false
            this.clearProcessTimer()
            if (status === 'ERROR') {
              this.$message.error(msg)
            }
            this.refreshTable()
          } else {
            this.disableEditRow = true
            this.disableEditCol = true
          }
        }
      })
    },
    loopTaskProcess(taskId) {
      this.getTaskProcess(taskId)
      this.clearProcessTimer()
      this.processTimer = setInterval(() => {
        this.getTaskProcess(taskId)
      }, 5000)
    },
    loopTaskInfo() {
      this.clearTaskInfoTimer()
      this.taskInfoTimer = setInterval(() => {
        this.getTaskInfoByTableId()
      }, 5000)
    },
    // 打开备份至数据库
    openBackUp() {
      if (this.disableEditRow) return
      this.showBackUp = true
      this.initBackUpForm()
    },
    // 清空弹框
    initBackUpForm() {
      this.backUpForm = { name: '' }
      if (this.$refs.backUpForm) {
        this.$refs.backUpForm.resetFields()
      }
    },
    closeBackUp() {
      this.showBackUp = false
    },
    handleBackUp() {
      if (this.viewTable.length === 0) {
        this.$message.info('表格无数据，不能备份至数据库')
        return
      }
      this.$refs['backUpForm'].validate((valid) => {
        if (valid) {
          const params = {
            id: this.curTableId,
            version: this.backUpForm.name
          }
          backUpData(params).then(res => {
            if (res.code === 200) {
              this.$message.success('备份成功')
              this.loopTaskProcess(res.data.taskId)
              this.showBackUp = false
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    openRevertData() {
      if (this.disableEditRow) return
      getVersionList(this.curTableId).then((res) => {
        if (res.code === 200) {
          this.versionList = res.data
          this.showRevert = true
          this.initRevertForm()
        }
      })
    },
    initRevertForm() {
      this.revertForm = { name: '' }
      if (this.$refs.revertForm) {
        this.$refs.revertForm.resetFields()
      }
    },
    closeRevert() {
      this.showRevert = false
    },
    // 载入备份数据
    handleRevert() {
      if (!this.revertForm.name) {
        this.$message.error('请选择版本')
        return
      }
      revertData(this.curTableId, this.revertForm.name).then(res => {
        if (res.code === 200) {
          this.$message.success('载入备份数据成功')
          this.showRevert = false
          this.loopTaskProcess(res.data.taskId)
        }
      })
    },
    delVersion(event, item) {
      event.preventDefault()
      event.stopPropagation()
      this.$confirm(`确定删除该版本吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delVersionApi(item.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.refreshVersion()
          }
        })
      }).catch(() => {
      })
    },
    refreshVersion() {
      getVersionList(this.curTableId).then((res) => {
        if (res.code === 200) {
          this.versionList = res.data
          this.initRevertForm()
        }
      })
    },
    openOriginData() {
      if (this.disableEditRow) return
      getViewList({ pageNum: 1, pageSize: 9999 }).then(res => {
        if (res.code === 200) {
          this.originList = res.rows
          this.showOrigin = true
          this.originForm.name = this.curViewId
          // this.initOriginForm()
        }
      })
    },
    initOriginForm() {
      this.originForm = { name: '' }
      if (this.$refs.originForm) {
        this.$refs.originForm.resetFields()
      }
    },
    closeOrigin() {
      this.showOrigin = false
    },
    // 载入源数据
    handleOrigin() {
      const params = { id: this.curTableId }
      loadData(params).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg)
          this.showOrigin = false
          this.loopTaskProcess(res.data.taskId)
        }
      })
    },
    // 释放内存
    releaseData() {
      if (this.disableEditRow) return
      const params = { id: this.curTableId }
      releaseData(params).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg)
          this.refreshTable()
        }
      })
    },
    // 单元格激活时触发
    editActived({ row, column }) {
      const { field } = column
      const { _X_ROW_KEY } = row
      this.editRowColMap[_X_ROW_KEY] = this.editRowColMap[_X_ROW_KEY] ? this.editRowColMap[_X_ROW_KEY] : []
      if (!this.editRowColMap[_X_ROW_KEY].includes(field)) {
        this.editRowColMap[_X_ROW_KEY].push(field)
      }
      const targetRow = this.originTable.find(item => item._X_ROW_KEY === _X_ROW_KEY)
      this.viewTable.forEach((item) => {
        if (item._X_ROW_KEY === _X_ROW_KEY) {
          item[field] = targetRow[field]
        }
      })
    },
    // 给原始数据加上X_ROW_KEY
    addKey() {
      if (this.viewTable.length && this.originTable.length) {
        this.viewTable.forEach((item, index) => {
          this.originTable[index]._X_ROW_KEY = item._X_ROW_KEY
        })
      }
    },
    // 获取插入行
    getInsertEvent() {
      const $table = this.$refs.xTable
      const insertRecords = $table.getInsertRecords()
      return insertRecords
    },
    // 获取修改的行
    getUpdateEvent() {
      const $table = this.$refs.xTable
      const updateRecords = $table.getUpdateRecords()
      console.log('修改的行', updateRecords, this.originTable)
      // 修改的行要提交原始值
      updateRecords.forEach(item => {
        const { _X_ROW_KEY } = item
        const targetRow = this.originTable.find(item => item._X_ROW_KEY === _X_ROW_KEY)
        // 找出未编辑的单元格赋原始值
        const hasEditColArr = this.editRowColMap[_X_ROW_KEY]
        for (const p in item) {
          if (!hasEditColArr.includes(p) && p !== '_X_ROW_KEY') {
            // 找出日期类型赋YYYY-MM-DD
            const target = this.tableMetaJson.find(item => item.code === p)
            const storageType = target.dataFormat.storageType
            item[p] = storageType === 'DATE' ? moment(targetRow[p]).format('YYYY-MM-DD') : targetRow[p]
          }
        }
      })
      return updateRecords
    },
    // 根据格式id获取类型
    getTypeById(id) {
      const target = this.formatList.filter(item => item.id === id)[0]
      return target ? target.storageType : 'VARCHAR'
    },
    // 改变格式
    changeFormatTree(val) {
      const target = this.formatList.filter(item => item.id === val)[0]
      this.tableMetaJson.forEach(col => {
        if (col.dataFormatId === val) {
          col.dataFormat = target
        }
      })
    },
    // 获取各列宽度
    getAllColW() {
      const allCol = this.$refs.xTable.getTableColumn().tableColumn
      this.allColWArr = allCol.map(col => col.renderWidth)
    },
    // 列宽拖动事件
    colResizeChange({ $rowIndex, column, columnIndex, $columnIndex, $event }) {
      this.getAllColW()
      this.tableMetaJson[columnIndex].columnWidth = column.renderWidth
      this.saveColumn()
    },
    // 根据字符个数计算列宽(10个英文字符对应140px)
    // formatWidth(count) {
    //   const validCount = (Number(count) <= this.minCount || !count) ? this.minCount : Number(count)
    //   return (Math.ceil(validCount * 7)) + 70
    // },
    // 右键菜单
    contextMenuClickEvent({ menu, row, column }) {
      switch (menu.code) {
        case 'edit':
          if (row && column) {
            this.showModifyHis = true
            console.log(row, column)
          }
          break
        default:
          console.log(`点击了 ${menu.name} 选项`)
      }
    },
    hideModifyHis() {
      this.showModifyHis = false
    },
    // 初始化拖拽逻辑
    columnDrop2() {
      this.$nextTick(() => {
        const $table = this.$refs.xTable
        const $el = $table.$el.querySelector('.body--wrapper .vxe-table--body')
        console.log($el.scrollWidth, $el.offsetWidth)
        this.sortable2 = Sortable.create($table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
          handle: '.vxe-header--column',
          animation: 150,
          // 被禁止拖动的元素,第一列不允许被拖动
          filter: (evt, item) => {
            const attrs = item.getAttribute('class')
            if (attrs.includes('col--fixed')) {
              this.$message.error('第一列不允许拖拽')
              return true
            }
            return false
          },
          //* ********  拖拽中的事件(滚动条跟着拖拽滚动) *********
          onMove: (evt, originalEvent) => {
            // console.log(evt, originalEvent)
            // 鼠标移动距离
            const moveW = originalEvent.clientX
            // 表格滚动的left值
            // const tableScrollL = $table.getScroll().scrollLeft
            // 计算鼠标拖拽到离页面右边70px时开始滚动
            if (moveW > (this.tableClientW - 100)) {
              $table.scrollTo(this.tableOffsetW)
            } else if (moveW < 400) {
              $table.scrollTo(0)
            } else {
              $table.refreshScroll()
            }
          },
          onEnd: ({ item, newIndex, oldIndex }) => {
            const { fullColumn, tableColumn } = $table.getTableColumn()
            const targetThElem = item
            const wrapperElem = targetThElem.parentNode
            const newColumn = fullColumn[newIndex]
            if (newColumn.fixed) {
              const oldThElem = wrapperElem.children[oldIndex]
              // 错误的移动
              if (newIndex > oldIndex) {
                wrapperElem.insertBefore(targetThElem, oldThElem)
              } else {
                wrapperElem.insertBefore(targetThElem, oldThElem ? oldThElem.nextElementSibling : oldThElem)
              }
              this.$message.error('不允许拖到第一列')
              return
            }
            // 获取列索引 columnIndex > fullColumn
            const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
            const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
            // 移动到目标列
            this.dragFun(fullColumn, oldColumnIndex, newColumnIndex)
            this.dragFun(this.tableMetaJson, oldColumnIndex, newColumnIndex)
            $table.loadColumn(fullColumn)
            this.saveColumn()
          }
        })
      })
    },
    // 拖拽
    dragFun(arr, index1, index2) {
      const oldItem = arr.splice(index1, 1)[0]
      arr.splice(index2, 0, oldItem)
      return arr
    },
    // 表格源数据
    async fetchData(id) {
      this.loading = true
      await getTableDetail(id).then(async res => {
        if (res.code === 200) {
          this.curTableDetail = res.data
          const { tableMetaJson, viewId } = res.data
          this.tableMetaJson = tableMetaJson
          this.curViewId = viewId // 数据源id
          // 关联系统参数列
          this.relateSystemCol = this.tableMetaJson.find(item => item.systemKey)
          this.randomKey = Math.round(Math.random() * 80 + 10)
          // 获取tableMetaJson的最大no
          const noArr = this.tableMetaJson.map(item => item.no)
          this.currentNo = Math.max(...noArr)
          await this.load2dTable(id)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    async load2dTable(id) {
      const params = {
        ...this.pageInfo,
        tableId: id,
        sortList: this.sortList,
        filterList: this.filterList,
        parentIdValList: this.parentTableLevel === 1 ? this.twoCol : [this.twoCol[0]]
      }
      this.loading = true
      await getSubTable(params).then(res => {
        if (res.code === 200) {
          const arr = res.rows && res.rows[0] && res.rows[0].rows
          if (arr && arr.length) {
            this.viewTable = this.formatListMem(arr, 'displayValue')
            // 存一份原始值的表格数据
            this.originTable = this.formatListMem(arr, 'originalValue')
            // 给原始表格加上X_ROW_KEY
            this.addKeyTimer = setTimeout(() => {
              this.addKey()
            }, 100)
            // 获取dictMap
            if (arr && arr.length) {
              const dictMap = {}
              const singleRow = arr[0].cells
              singleRow.forEach(item => {
                const { code, dicItems } = item || {}
                if (dicItems) {
                  dictMap[code] = dicItems
                }
              })
              this.dictMap = dictMap
            }
            this.totalRecords = res.total
          } else {
            // 如果没有行数据则添加一行空数据
            this.viewTable = []
            // this.addRow()
          }
        }
      }).finally(() => {
        this.getAllColW()
        this.loading = false
      })
    },
    // 计算表格各个宽度
    getTableW() {
      const $table = this.$refs.xTable
      const $el = $table.$el.querySelector('.body--wrapper .vxe-table--body')
      this.tableOffsetW = $el.offsetWidth
      this.tableClientW = $table.$el.clientWidth
    },
    // 处理listMemData接口数据
    formatListMem(arr, type) {
      const validArr = []
      arr && arr.forEach(item => {
        const { cells } = item
        const obj = {}
        cells.forEach(cell => {
          const { code } = cell
          obj[code] = cell[type] || cell['originalValue']
        })
        validArr.push(obj)
      })
      return validArr
    },
    // 排序
    sortChangeEvent({ sortList }) {
      console.info(sortList)
      this.sortList = sortList.map(item => {
        const { field, order } = item
        return { columnCode: field, sort: order }
      })
    },
    // 添加列
    addColumn() {
      this.disableEditRow = true
      this.currentNo++
      this.tableMetaJson.push({ no: this.currentNo, name: `未命名${this.currentNo}`, dataFormatId: this.defaultFormatId, dataFormat: this.defaultFormat, newColumnType: 'VARCHAR', oldColumnType: 'VARCHAR', condition: null, systemKey: null, systemValue: null, operationType: 'ADD' })
      this.saveColumn('add')
    },
    // 修改列
    updateCol(col) {
      const { code, systemKey } = col
      const newTableJson = cloneDeep(this.tableMetaJson)
      console.log('newTableJson', newTableJson)
      newTableJson.forEach(item => {
        if (item.code === code) {
          item.operationType = 'UPDATE'
        }
      })
      if (systemKey && this.relateSystemCol && this.relateSystemCol.code !== code) {
        this.$confirm(`${this.relateSystemCol.name}列已经关联系统参数，是否覆盖？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          newTableJson.forEach(item => {
            if (item.code !== code) {
              item.systemKey = null
            }
          })
          this.tableMetaJson = newTableJson
          this.saveColumn()
        }).catch(() => {
        })
      } else {
        newTableJson.forEach(item => {
          if (this.relateSystemCol) {
            if (item.code !== code && item.code !== this.relateSystemCol.code) {
              item.systemKey = null
            }
          } else {
            if (item.code !== code) {
              item.systemKey = null
            }
          }
        })
        this.tableMetaJson = newTableJson
        this.saveColumn()
      }
    },
    // 横向滚动条滚动到最右边并高亮最后一列
    scrollToCurCol(idx) {
      this.$nextTick(() => {
        const xTable = this.$refs.xTable
        // 由于固定列的动态切换是无状态的，所以需要手动刷新滚动位置
        xTable.refreshColumn().then(() => {
          const allCols = xTable.getTableColumn().tableColumn
          const lastCol = idx ? allCols[idx] : allCols[allCols.length - 1]
          // 高亮最后一列
          xTable.setCurrentColumn(lastCol)
          // 滚动到最后一列
          // xTable.scrollToColumn(lastCol)
          const allW = this.allColWArr.reduce((prev, cur) => {
            return prev + cur
          }, 0)
          console.log('所有列总宽度', allW)
          xTable.scrollTo(allW)
        })
      })
    },
    // 保存列
    saveColumn(operateType) {
      const { id, parentId, memTableName, type, viewId, tableName } = this.curTableDetail
      const params = {
        id, parentId, memTableName, type, viewId, tableName, tableMetaJson: this.tableMetaJson
      }
      saveCol(params).then(async res => {
        if (res.code === 200) {
          this.disableEditRow = false
          this.$message.success(operateType === 'add' ? '新增列成功' : '更新列成功')
          await this.refreshTable()
          if (operateType === 'add') {
            const allCols = this.$refs.xTable.getTableColumn().tableColumn
            this.curCol = allCols[allCols.length - 1]
            this.clearScrollTimer()
            this.scrollTimer = setTimeout(() => {
              this.scrollToCurCol()
            }, 1000)
          }
          if (operateType === 'delete') {
            this.curCol = null
          }
        }
      }).catch(() => {
        this.refreshTable()
      })
    },
    // 删除列
    delColumn() {
      if (this.tableMetaJson.length <= 1) {
        this.$message.error('请至少保留一列')
        return
      }
      if (this.curCol) {
        this.$confirm('确定删除该列吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const { title, field } = this.curCol
          if (field) {
            this.tableMetaJson.forEach(item => {
              if (item.code === field) {
                item.operationType = 'DELETE'
              }
            })
          } else {
            this.tableMetaJson = this.tableMetaJson.filter(item => item.name !== title)
          }
          this.saveColumn('delete')
        })
      } else {
        this.$message.error('请选择一列进行删除')
      }
    },
    // 添加行获取自增id
    addRow() {
      if (this.disableEditRow) return
      const { userName, dept } = this.$store.getters.user || {}
      let firstCode = ''
      let secondCode = ''
      const allCodeMap = {}
      this.tableMetaJson.forEach((col, idx) => {
        const { systemKey } = col || {}
        // 获取第一列的code
        if (idx === 0) {
          firstCode = col.code
        }
        if (idx === 1) {
          secondCode = col.code
        }
        if (systemKey === 'USER') {
          allCodeMap[col.code] = userName
        } else if (systemKey === 'ORG') {
          allCodeMap[col.code] = dept.deptName
        } else {
          allCodeMap[col.code] = ''
        }
      })
      const params = {
        memTableName: this.curTableDetail.memTableName,
        primaryColumnName: secondCode,
        id: this.relSubTableId,
        parentRelKeyList: [this.twoCol[0]]
      }
      this.loading = true
      createId(params).then(res => {
        if (res.code === 200) {
          allCodeMap[firstCode] = this.parentTableKeyVal
          allCodeMap[secondCode] = res.data
          this.$refs.xTable.insert(allCodeMap)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    currentChangeEvent({ row }) {
      this.curRow = row
    },
    // 将行数据处理成后台需要的
    formatRow(originArr) {
      const arr = cloneDeep(originArr)
      const rows = []
      arr.forEach(row => {
        const cells = []
        for (const code in row) {
          const colData = this.tableMetaJson.find(item => item.code === code)
          // 过滤xTable自带的key
          if (code !== '_X_ROW_KEY' && colData) {
            cells.push({
              code,
              cellValue: row[code],
              type: '',
              no: colData.no
            })
          }
        }
        // cells根据no大小排序
        const sortCells = this.objArrSort(cells, 'no')
        sortCells.forEach(cell => {
          delete cell.no
        })
        // 给cells里的值按no大小排序
        rows.push({ cells: sortCells })
      })
      return rows
    },
    // 根据key给对象数组排序
    objArrSort(arr, key) {
      const newArr = cloneDeep(arr)
      return newArr.sort((a, b) => a[key] - b[key])
    },
    /**
     * 保存数据,分为新增和修改,分别调接口
     */
    saveRowData() {
      if (this.disableEditRow) return
      const insertRecords = this.formatRow(this.getInsertEvent())
      const updateRecords = this.formatRow(this.getUpdateEvent())
      // 调用保存行接口
      this.saveRowApi(insertRecords, updateRecords)
    },
    async saveRowApi(insertRecords, updateRecords) {
      const { id: tableId, memTableName } = this.curTableDetail
      const baseParams = {
        tableId,
        memTableName
      }
      const addParams = {
        rows: insertRecords,
        ...baseParams
      }
      const updateParams = {
        rows: updateRecords,
        ...baseParams
      }
      let addRes = null
      let updateRes = null
      if (insertRecords && insertRecords.length) {
        addRes = await addRowData(addParams)
      }
      if (updateRecords && updateRecords.length) {
        updateRes = await updateRowData(updateParams)
      }
      if ((addRes && addRes.code === 200) || (updateRes && updateRes.code === 200)) {
        this.$message.success('保存成功')
        this.refreshTable()
        // 轮询任务进度
        if (updateRes && updateRes.code === 200) {
          this.loopTaskProcess(updateRes.data.taskId)
        }
      }
    },
    // 删除行
    delRow() {
      if (this.disableEditRow) return
      if (this.curRow) {
        this.$confirm('确定删除该行吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 判断是否是新增的行,新增行走前端删除，原有行走接口删除
          const { _X_ROW_KEY } = this.curRow
          const insertRecords = this.getInsertEvent()
          const target = insertRecords.find(item => item._X_ROW_KEY === _X_ROW_KEY)
          if (target) {
            this.$refs.xTable.remove(target)
            this.curRow = null
            this.$message.success('删除行成功')
          } else {
            const rows = this.formatRow([this.curRow])
            const { id: tableId, memTableName } = this.curTableDetail
            const params = {
              rows,
              tableId,
              memTableName
            }
            deleteRowApi(params).then(res => {
              if (res.code === 200) {
                this.curRow = null
                this.$message.success('删除行成功')
                this.loopTaskProcess(res.data.taskId)
                this.refreshTable()
              }
            })
          }
        })
        // this.viewTable = this.viewTable.filter(item => item._X_ROW_KEY !== rowKey)
      } else {
        this.$message.error('请选择一行进行删除')
      }
    },
    clickCol(col) {
      this.curCol = col
      const target = this.tableMetaJson.find(item => item.code === col.field)
      // const type = target.dataFormat.storageType || 'VARCHAR'
      const params = {
        ...this.pageInfo,
        columnCode: target.code,
        tableId: this.relSubTableId,
        sortList: this.sortList,
        filterList: this.filterList,
        parentIdValList: this.parentTableLevel === 1 ? this.twoCol : [this.twoCol[0]]
      }
      calcSumAvg(params).then(res => {
        if (res.code === 200) {
          this.sumavgObj = res.data || { a: null, s: null }
        }
      })
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.refreshTable()
    },
    handleCurrentChange(val) {
      this.pageInfo.currentPage = val
      this.refreshTable()
    },
    openCalc(config) {
      if (this.disableEditCol) return
      getQuickInput({ tableId: this.curTableId }).then(res => {
        if (res.code === 200) {
          this.quickInputOpts = this.formatQuickData(res.data)
          this.initQuickIpt()
          this.curCol = config
          this.calcForm.rule = config.computationRule
          this.calcRules = calcRules.createCalcRule(this.calcForm, this.curTableId, config.code)
          this.calcVisible = true
        }
      })
    },
    initQuickIpt() {
      this.quickForm = { o: '', p: '', expressIf: '', t: [], c: [], func: [] }
    },
    closeCalc() {
      this.calcForm.rule = ''
      this.calcVisible = false
    },
    handleCalc() {
      this.$refs['calcForm'].validate((valid) => {
        if (valid) {
          const { code } = this.curCol
          const params = {
            tableId: this.curTableId,
            express: this.calcForm.rule,
            columnCode: code
          }
          saveRule(params).then(res => {
            if (res.code === 200) {
              this.$message.success('成功保存计算规则')
              this.calcVisible = false
              this.loopTaskProcess(res.data.taskId)
              this.refreshTable()
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 查询
    searchTable() {
      this.filterList = this.dynamicSearchArr.map(item => {
        const { columnCode, value, prefixVal, storageType } = item
        return { columnCode, value, prefixVal, storageType, op: 'and' }
      })
      this.refreshTable(false)
    },
    // 重置
    resetQuery() {
      this.dynamicSearchArr.forEach(item => {
        item.value = ''
        item.prefixVal = '0'
      })
      this.searchTable()
    },
    // 选择人员
    selectOrg(node, instanceId) {
      const { id, label } = node
      this.orgMap[id] = label
    },
    // 格式化单元格
    formatCell({ cellValue, row, column }, config) {
      if (config.systemKey === 'USER') {
        return this.userMap[cellValue]
      }
      if (config.systemKey === 'ORG') {
        return this.orgMap[cellValue]
      }
      return cellValue
    },
    // 打开人员弹框
    openUser(row, config) {
      this.curEditRow = row
      this.curEditCol = config
      this.$refs.selectUser.show()
    },
    // 选中人员
    handleSelectUser(user) {
      const { userId, nickName } = user
      this.curEditRow[this.curEditCol.code] = userId
      this.userMap[userId] = nickName
    },
    changeSystemKey(config) {
      config.systemValue = null
    },
    // 刷新表格
    refreshTable(updateSearch = true) {
      this.updateSearch = updateSearch
      this.fetchData(this.relSubTableId)
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.table-list {
  position: relative;
}
.search-container {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  margin-top: 10px;
  min-height: 40px;
  .left {
    position: absolute;
    left: 0;
    top: 0;
    right: 45px;
    background: #fff;
    border-radius: 3px;
    padding-left: 3px;
    &:hover {
      z-index: 9999;
      box-shadow:0 2px 12px 0 rgba(0,0,0,.1);
    }
  }

}
.search-form {
  .el-form-item {
    margin-bottom: 10px;
  }
  display: flex;
  .left-area {
    flex: 1;
  }
  .right-area {
    width: 160px;
  }

}
.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .sum-avg {
    font-size: 12px;
    color:#606266;
    margin-right: 20px;
    .label {
      color: $formLabel;
    }
  }
  .right {
    display: flex;
  }
}

.table-box {
    display: flex;
    height: calc(100vh - 270px);
    .add-col {
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-left: 10px;
    }
    &.no-search {
      height: calc(100vh - 222px);
    }
  }
.dynamic-table {
  width: calc(100vw - 68px);
    ::v-deep.vxe-table--header-wrapper .vxe-icon-edit {
      display: none;
    }
  ::v-deep .vxe-header--row .vxe-header--column {
    height: 30px;
  }
  ::v-deep .subtable-cell {
    cursor: pointer;
    color: $primary;
    font-weight: bold;
    .vxe-cell--label {
      text-decoration: underline;
    }
  }
}

.custom-col {
  justify-content: space-between;
  i {
    cursor: pointer;
    color: $formLabel;
  }
  .icon-appjisuanqi {
    margin:0 3px;
  }
}
.popver-box {
  text-align: left;
  .popver-item {
    display: flex;
    align-items: center;
    margin: 8px 0;
    .title {
      margin: 0 10px;
      &.active {
        color: $primary;
        cursor: pointer;
      }
    }

  }
}
  .row-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 44px;
    padding: 5px;
    .el-checkbox {
      margin-right: 10px;
    }
  }

  .col-left {
    span {
      margin-left: 2px;
    }
    i {
      color: $formLabel;
    }
  }
  .setcol-btn {
    height: 24px;
    .el-button {
      font-size: 13px;
    }
    .del-btn {
      color: $primary
    }
  }
  .vxe-table {
    ::v-deep .vxe-cell {
      padding-right: 3px;
      .vxe-select {
        z-index: 2;
      }
      .vxe-cell--title {
        width: 100%;
      }
    }
    // ::v-deep .vxe-body--column.col--dirty {
    //   background: #fdf6ec !important;
    //   &::before {
    //     display: none !important;
    //   }
    // }
  }
.open-user {
  cursor: pointer;
}
.col-sort {
  display: flex;
  flex-direction: column;
  margin-right: 3px;
  i {
    width: 10px;
    height: 10px;
    color: #c0c4cc;
    &.active {
      color:$primary;
    }
  }
}
.btn-group {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .btn-box {
    padding: 0 10px;
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #606266;
    cursor: pointer;
    &:hover {
      background: #f6f7f8;
      color: #077cf5;
    }
    .btn-text {
      margin-top: 8px;
      font-weight: bold;
    }
    .btn-icon {
      font-size: 18px;
      &.create-data {
        width: 18px;
        height: 18px;
        background-image: url("../../../assets/create-data.png");
        background-size: contain;
      }
    }
    &.disabled {
      color: #909399;
    }
  }
  .gap-line {
    width: 1px;
    height: 60px;
    background: #ebeef5
  }
}
.process-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 12px;
  color:#606266;
  ::v-deep .el-progress {
    width: 100px;
    .el-progress__text {
      font-size: 12px !important;
    }
  }
}
.custom-control {
  display: flex;
  align-items: center;
  .vxe-select {
    width: 80px;
    border-right: none;
  }
}
.quick-input {
  display: flex;
  margin-bottom: 15px;
  .control-box {
    min-width: 110px;
    position: relative;
    margin-right: 25px;
    cursor: pointer;
    &:hover {
      .control-btn {
        background: #e8eaec;
      }
    }
    .control-btn {
      position: absolute;
      z-index: 10;
      top: 0;
      left: 0;
      width: 110px;
      height: 30px;
      border-radius: 4px;
      pointer-events:none;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 13px;
      background: #fff;
      border: 1px solid #e8eaec;
      &.if {
        pointer-events:auto;
      }
    }
  }
  .el-select,.el-cascader {
    width: 100px;
  }
}
.del-select {
  width: 100%;
}
.delversion-option {
  padding-right: 0 !important;
  background: #fff;
  .del-icon {
    float: right;
    color: #8492a6;
    font-size: 13px;
    cursor: pointer;
    height: 34px !important;
    width: 34px !important;
    text-align: center;
    line-height: 34px;
  }
}
.download-link {
  color: $primary;
}
.full-dialog {
  ::v-deep .el-dialog.is-fullscreen {
    overflow: hidden !important;
  }
}
</style>
