<template>
  <div>
    <el-dialog class="col-rename-dia" :title="'列自动命名'" :visible="true" width="650px" :close-on-click-modal="false" @close="closeColRename">
      <div class="title-area">
        <div class="left">当前列名</div>
        <div class="right">拟更改后列名</div>
      </div>
      <div class="col-area">
        <div v-for="item in validColArr" :key="item.code" class="col-item">
          <div class="left">{{ item.name }}</div>
          <div class="right">
            <el-input v-model="item.newName" size="small" placeholder="请输入列名" />
            <el-tooltip class="item" effect="dark" content="列名不能为空且不能超过30个字符，且必须为汉字、字母、数字、下划线的组合" placement="top">
              <span class="el-icon-info" />
            </el-tooltip>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button>取消</el-button>
        <el-button type="primary" @click="handleColRename">确认统一更改命名</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
// import { getList } from '@/api/base-table'

export default {
  props: {
    colArr: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      validColArr: []
    }
  },
  computed: {

  },
  created() {
    this.validColArr = this.colArr && this.colArr.map(item => {
      return {
        name: item.name,
        code: item.code,
        newName: ''
      }
    })
  },
  methods: {

    closeColRename() {
      this.$emit('close')
    },
    handleColRename() {
      var reg = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/
      const flag = this.validColArr.every(item => {
        const { newName } = item
        return newName && newName.length <= 30 && reg.test(newName)
      })
      if (!flag) {
        this.$message.error('所有列名不能为空且不能超过30个字符，且必须为汉字、字母、数字、下划线的组合')
        return
      }
      console.log(this.validColArr)
    }

  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.title-area,.col-item {
  display: flex;
  width: 100%;
  margin-bottom: 10px;
  .left {
    flex: 2;
    display: flex;
    align-items: center;
  }
  .right {
    flex: 3;
  }
}
.title-area {
  font-size: 14px;
  font-weight: 500;
  color: $importantText;
  margin-bottom: 20px;
}
.col-item {
  .right {
    display: flex;
    align-items: center;
    span {
      margin-left: 5px;
    }
  }
}
.col-rename-dia {
  ::v-deep .col-area {
    height: 500px !important;
    overflow-y: auto;
    padding-right: 20px;
  }
}
</style>
