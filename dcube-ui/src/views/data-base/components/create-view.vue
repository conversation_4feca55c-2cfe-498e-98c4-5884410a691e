<template>
  <div class="add-base-table">
    <!-- 新增底表对话框 -->
    <el-drawer
      :class="curType"
      :title="dialogTitle"
      :visible="tableVisible"
      size="80%"
      @close="closeAddTable"
    >
      <el-collapse v-model="activeCollapse" class="custom-collapse custom-scroll">
        <el-form ref="addTableForm" class="view-form" size="small" :model="addTableForm" :rules="rules" label-position="right" label-width="110px">
          <el-form-item label="视图名称" prop="viewName">
            <el-input v-model="addTableForm.viewName" />
          </el-form-item>
          <el-form-item label="数据源" prop="sourceName">
            <el-input v-model="addTableForm.sourceName" :disabled="true" />
          </el-form-item>
          <el-form-item label="SQL脚本" prop="sqlScript">
            <el-input v-model="addTableForm.sqlScript" type="textarea" :rows="5" />
          </el-form-item>
          <el-form-item style="text-align:right;">
            <el-button type="primary" plain size="mini" @click="runSqlBtn">运行SQL</el-button>
            <el-button v-if="curType==='add'||curType==='edit'" type="primary" plain size="mini" :loading="loadUrlBtn" @click="testUrl">测试连接</el-button>
            <el-button type="primary" plain size="mini" :loading="loadSave" @click="handleSave">保 存</el-button>
          </el-form-item>
        </el-form>
        <el-collapse-item title="数据预览" name="1">
          <!-- 预览表格 -->
          <template>
            <div class="table-wrapper">
              <vxe-table
                ref="xTable"
                border
                height="auto"
                :loading="loadingPreview"
                size="mini"
                show-overflow="tooltip"
                :data="previewList"
                :row-config="{isCurrent: true, isHover: true}"
              >
                <vxe-column
                  v-for="config in tableMetaJson"
                  :key="config.code"
                  :field="config.code"
                  :title="config.name"
                  show-overflow="title"
                  width="180"
                >
                  <template #header="{ column }">
                    <div slot="reference" class="flex-vertical-center custom-col">
                      <span class="custom-ellipsis" style="width:90px">{{ config.name }}</span>
                    </div>
                  </template>

                </vxe-column>
                <template #empty>
                  <table-empty />
                </template>
              </vxe-table>
            </div>
            <el-pagination
              layout="total"
              :total="totalPreview"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </template>
        </el-collapse-item>
      </el-collapse>
    </el-drawer>
  </div>
</template>
<script>
import { testConnection, addTable, editTable, runSqlApi, generateView } from '@/api/data-base'

export default {
  props: {
    curType: {
      type: [String, Number],
      default: 'add'
    },
    tableVisible: {
      type: Boolean,
      default: false
    },
    currentNodeData: {
      type: Object,
      default: () => {}
    },
    tableId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    var checkSql = (rule, value, callback) => {
      const reg = /^select/i
      if (!value) {
        return callback(new Error('请输入SQL脚本'))
      }
      if (value && !reg.test(value)) {
        return callback(new Error('只能输入以SELECT(Select)开头的SQL脚本'))
      }
      callback()
    }
    return {
      addTableForm: {
        viewName: '',
        sourceName: '',
        sqlScript: ''
      },
      rules: {
        viewName: [
          { required: true, message: '请输入视图名称', trigger: 'blur' }
        ],
        sourceName: [
          { required: true, message: '请输入数据源', trigger: 'blur' }
        ],
        sqlScript: [
          { required: true, validator: checkSql, trigger: ['change', 'blur'] }
        ]
      },
      // sqlPreviewShow: false,
      loadingPreview: false,
      // 底表行数据
      previewList: [],
      // 底表列数据
      tableMetaJson: [],
      sourceId: '',
      previewParams: {
        timestamp: undefined,
        fileName: undefined
      },
      activeCollapse: ['1'],
      loadUrlBtn: false,
      totalPreview: 0,
      saveExcelViewCol: [],
      loadSave: false
    }
  },
  computed: {
    dialogTitle() {
      return '创建视图'
    }
  },
  watch: {
    tableVisible(val) {
      if (val) {
        this.initFormData()
      }
    }
  },
  mounted() {
    this.resetForm()
  },
  methods: {
    initFormData() {
      console.log(this.currentNodeData)
      const { dataSource, sqlScript, tableMeta, result, totalCount } = this.currentNodeData
      const { id, sourceName } = dataSource
      this.addTableForm = {
        viewName: '',
        sourceName,
        sqlScript
      }
      this.sourceId = id
      this.tableMetaJson = tableMeta
      this.previewList = result
      this.totalPreview = totalCount || 0
      this.activeCollapse = ['1']
    },
    // 保存数据源
    handleSave() {
      if (!this.tableMetaJson || this.tableMetaJson.length === 0) {
        this.$message.error('请先运行SQL脚本')
        return
      }
      this.saveView()
      return
    },
    saveView() {
      this.$refs['addTableForm'].validate((valid) => {
        if (valid) {
          this.loadSave = true
          const { viewName, sqlScript } = this.addTableForm
          const { dataSource, tableMeta } = this.currentNodeData
          const params = {
            viewName,
            id: this.tableId,
            sourceId: dataSource.id,
            sqlScript: sqlScript,
            tableMetaJson: tableMeta
          }
          generateView(params).then(res => {
            if (res.code === 200) {
              const msg = '生成成功'
              this.$message.success(msg)
              this.closeAddTable()
              this.$emit('addSuccess', res.data)
            }
          }).finally(() => {
            this.loadSave = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 数据预览(列数据)
    runSqlBtn() {
      if (this.addTableForm.sqlScript) {
        this.runSqlFun()
      } else {
        this.$message.error('请输入SQL脚本')
      }
    },
    runSqlFun() {
      this.loadingPreview = true
      runSqlApi({ sqlScript: this.addTableForm.sqlScript }).then(res => {
        if (res.code === 200) {
          this.$message.success('SQL运行成功')
          this.activeCollapse = ['1']
          const { result, tableMeta, totalCount } = res.data
          this.previewList = result
          this.tableMetaJson = tableMeta
          this.totalPreview = totalCount
        }
      }).finally(() => {
        this.loadingPreview = false
      })
    },
    closeAddTable() {
      this.resetForm('addTableForm')
      this.$emit('close')
    },
    handleSizeChange(val) {

    },
    handleCurrentChange(val) {

    },
    // 重置分组弹框
    resetForm() {
      if (this.$refs.addTableForm) {
        this.addTableForm = {
          viewName: '',
          sourceName: '',
          sqlScript: ''
        }
        this.$refs.addTableForm.resetFields()
      }
      this.activeCollapse = ['1']
      this.previewList = []
      this.tableMetaJson = []
      this.totalPreview = 0
    }

  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.view-form {
  width: 600px;
  margin-top: 20px;
}
.custom-collapse {
  height: calc(100vh - 70px);
  padding: 0 15px;
  overflow: auto;
  ::v-deep .el-collapse-item__wrap {
    border-bottom: none;
  }
  ::v-deep .el-collapse-item__header {
    color: $primary;
  }
  ::v-deep .el-collapse-item__content {
    padding-bottom: 10px;
    .table-wrapper {
      height: calc(100vh - 465px);
    }
  }
}
</style>
