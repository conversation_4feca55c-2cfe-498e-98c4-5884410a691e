<template>
  <el-dialog :title="'添加列'" :visible="true" width="500px" :close-on-click-modal="false" @close="closeDialog">
    <el-form ref="addForm" class="add-form" :model="addForm" :rules="rules" :inline="true" label-width="80px" size="small">
      <el-form-item label="列id" prop="id">
        <el-input v-model="addForm.id" />
      </el-form-item>
      <el-form-item label="列名" prop="name">
        <el-input v-model="addForm.name" />
      </el-form-item>
      <el-form-item label="数据类型" prop="dataType">
        <el-select v-model="addForm.dataType" placeholder="请选择">
          <el-option
            v-for="item in dataTypeArr"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="显示顺序" prop="order">
        <el-input v-model="addForm.order" />
      </el-form-item>
      <el-form-item label="列注释" prop="note">
        <el-input v-model="addForm.note" />
      </el-form-item>
      <el-form-item label="运算规则" prop="calcRules">
        <el-input v-model="addForm.calcRules" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="closeDialog">取 消</el-button>
      <el-button size="mini" type="primary" @click="addColumn">添 加</el-button>
    </div>
  </el-dialog>
</template>
<script>
// import { getList } from '@/api/base-table'

export default {
  props: {

  },
  data() {
    const arr = [{
      value: 'string',
      label: '字符串'
    }, {
      value: 'boolean',
      label: '布尔值'
    }, {
      value: 'number',
      label: '数字'
    }]
    return {
      addForm: {
        id: '',
        name: '',
        dataType: 'string',
        order: '',
        note: '',
        calcRules: ''
      },
      rules: {
        id: [
          { required: true, message: '请输入列id', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
          // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        ],
        dataType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ],
        order: [
          { required: true, message: '请输入显示顺序', trigger: 'blur' }
        ]
        // note: [
        //   { required: true, message: '请输入列注释', trigger: 'blur' }
        // ],
        // calcRules: [
        //   { required: true, message: '请输入运算规则', trigger: 'blur' }
        // ]
      },
      dataTypeArr: Object.freeze(arr)
    }
  },
  created() {
    // this.fetchData()
  },
  methods: {
    closeDialog() {
      this.$emit('close')
    },
    addColumn() {
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          alert('submit!')
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.add-form {
  ::v-deep .el-form-item__content,.el-select {
    width: 330px;
  }
}
</style>
