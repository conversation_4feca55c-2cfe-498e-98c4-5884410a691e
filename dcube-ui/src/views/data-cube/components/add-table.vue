<template>
  <el-dialog class="add-table" :title="dialogTitle" :visible="true" width="1200px" :close-on-click-modal="false" @close="closeAddCube">
    <el-form ref="addForm" :model="addCubeForm" size="small" :inline="true" :rules="addCubeRules">
      <el-form-item label="数方名称:" prop="name">
        <el-input
          v-model="addCubeForm.name"
          :disabled="!isAddCube"
          placeholder="请输入数方名称"
          clearable
        />
      </el-form-item>
    </el-form>
    <div class="bottom-area">
      <div class="bottom-left">
        <div class="top">
          <div class="left">
            单格内存：{{ }}
          </div>
          <div class="right">
            <el-button size="mini" type="primary" @click="openIndDrawer">设置指标</el-button>
            <el-button size="mini" type="primary" @click="openAddDimen">添加维度</el-button>
            <el-button v-if="!isAddCube" size="mini" type="primary" @click="setDimRelate">设置维度关联</el-button>
          </div>
        </div>
        <div class="bottom">
          <div class="dimen-list">
            <vxe-table
              ref="dimensionRef"
              size="mini"
              height="auto"
              show-overflow
              :data="dimensionList"
              :radio-config="{labelField: 'defaultDimen', highlight: true}"
              :row-config="{ isHover: true}"
              @radio-change="radioChange"
            >
              <vxe-column title="维度名称" field="dimDirectoryName" width="200" />
              <vxe-column title="成员个数" field="memCount" />
              <vxe-column type="radio" title="默认行维">
                <template #radio="{ row }">
                  <vxe-radio v-model="defaultDimId" :label="row.id" size="mini" />
                </template>
              </vxe-column>
              <vxe-column title="默认列维">
                <template #default="{ row }">
                  <vxe-checkbox v-model="defaultColumnDimId" size="mini" :checked-value="row.id" :unchecked-value="''" @change="(val)=>changeCheckBox(val,row)" />
                </template>
              </vxe-column>
              <vxe-column title="操作">
                <template #default="{ row }">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="delDimension(row)"
                  >删除</el-button>
                </template>
              </vxe-column>
              <template #empty>
                <table-empty />
              </template>
            </vxe-table>
          </div>
        </div>
      </div>
      <div class="bottom-right">
        <div class="top" style="margin-bottom: 10px;">
          <el-button
            size="mini"
            type="primary"
            @click="previewCube"
          >数方预览</el-button>
        </div>
        <div class="preview-cube">
          <el-form v-if="showPreviewSearch" ref="previewSearchForm" class="preview-search" size="small" :inline="true">
            <el-form-item v-for="item in previewSearchArr" :key="item.id" :label="item.dimDirectoryName" prop="indName">
              <el-input
                placeholder="请输入"
                readonly
              />
            </el-form-item>
          </el-form>
          <div class="preview-table">
            <vxe-table
              ref="previewTableRef"
              align="center"
              border
              size="mini"
              height="auto"
              show-overflow
              :data="previewData"
              :tree-config="{transform: true}"
              :row-config="{ isHover: true}"
            >
              <vxe-column v-if="previewCol.length" align="left" width="130px" field="dimName" tree-node show-overflow />
              <vxe-colgroup v-for="item in previewCol" :key="item.id" :field="String(item.id)" :title="item.indName">
                <vxe-colgroup v-for="child in item.children" v-if="item.children && item.children.length" :key="child.id" :field="String(child.id)" :title="child.indName">
                  <vxe-colgroup v-for="grandson in child.children" v-if="child.children && child.children.length" :key="grandson.id" :field="String(grandson.id)" :title="grandson.indName" />
                </vxe-colgroup>
              </vxe-colgroup>
              <template #empty>
                <table-empty />
              </template>
            </vxe-table>
          </div>

        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="closeAddCube">取 消</el-button>
      <el-button size="mini" type="primary" @click="handleAddCube">{{ isAddCube?'添 加':'保 存' }}</el-button>
    </div>
    <!-- 添加维度 -->
    <el-dialog v-if="addDimenDialog" append-to-body class="" :title="'添加维度'" :visible="true" width="1000px" :close-on-click-modal="false" @close="closeAddDimen">
      <div class="dimen-area">
        <div class="title">选择维度</div>
        <div class="tree-area">
          <div class="left">
            <div class="top">全部维度</div>
            <el-tree default-expand-all highlight-current :expand-on-click-node="false" :data="dimensionTree" :props="dimenTreeProps" @node-click="handleDimenClick" />
          </div>
          <div class="right">
            <div class="top">成员名称</div>
            <el-tree v-loading="loadingMemberTree" :expand-on-click-node="false" default-expand-all :data="memberTree" :props="memberTreeProps" />
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeAddDimen">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleAddDimen">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 设置指标 -->
    <el-drawer
      :append-to-body="true"
      title="设置指标"
      :visible.sync="setIndDrawer"
      direction="rtl"
      size="90%"
    >
      <div class="drawer-btn">
        <el-button size="mini" type="primary" @click="openAddInd(null)">添加指标</el-button>
        <el-button size="mini" type="primary" @click="openAddGroup(null)">添加分组</el-button>
      </div>
      <div class="set-ind">
        <vxe-table
          ref="indTableRef"
          :loading="loadingIndTable"
          size="mini"
          height="auto"
          show-overflow
          :tree-config="{transform: true}"
          :data="indTableList"
          :row-config="{isCurrent: true, isHover: false}"
        >
          <vxe-column field="indName" title="指标" tree-node show-overflow />
          <vxe-column field="dataFormatId" title="数据格式">
            <template #default="{ row }">
              <div>{{ formatDataFormat(row.dataFormatId) }}</div>
            </template>
          </vxe-column>
          <vxe-column field="functionName" title="向上汇总方式" />
          <vxe-column field="avg" title="加权列">
            <template #default="{ row }">
              <div>{{ formatAvg(row.avgParmaIndId) }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="storage" title="占用内存">
            <template #default="{ row }">
              <div>{{ row.memorySize?row.memorySize+'G':'' }}</div>
            </template>
          </vxe-column> -->
          <vxe-column title="操作" width="400">
            <template #default="{ row }">
              <!-- <el-button v-if="row.indType==='GROUP'&&row.parentId===0" size="mini" icon="el-icon-circle-plus-outline" type="text" status="primary" @click="openAddGroup(row)">添加子组</el-button> -->
              <el-button v-if="row.indType==='GROUP'" size="mini" type="text" status="primary" icon="el-icon-circle-plus-outline" @click="openAddInd(row)">添加指标</el-button>
              <el-button v-if="row.indType==='GROUP'" size="mini" type="text" status="primary" icon="el-icon-edit-outline" @click="openReName(row)">重新命名</el-button>
              <el-button size="mini" type="text" status="primary" icon="el-icon-delete" @click="delInd(row)">删除</el-button>
              <el-button size="mini" type="text" status="primary" icon="el-icon-top" @click="moveItem(row.id,'up')">上移</el-button>
              <el-button size="mini" type="text" status="primary" icon="el-icon-bottom" @click="moveItem(row.id,'down')">下移</el-button>
              <el-button v-if="row.indType==='INSTANCE'" size="mini" type="text" status="primary" icon="el-icon-edit-outline" @click="editInd(row)">修改</el-button>
            </template>
          </vxe-column>
          <template #empty>
            <table-empty />
          </template>
        </vxe-table>
        <div class="drawer-btn save">
          <el-button size="mini" @click="closeIndDrawer">取 消</el-button>
          <el-button size="mini" type="primary" @click="handleSetInd">确 定</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 添加分组 -->
    <el-dialog v-if="addGroupDialog" append-to-body class="" :title="addGroupTitle" :visible="true" width="400px" :close-on-click-modal="false" @close="closeAddGroup">
      <el-form ref="addGroupForm" class="add-form" :model="addGroupForm" size="small" :inline="true" :rules="addGroupRules">
        <el-form-item label="分组名称:" prop="indName">
          <el-input
            v-model="addGroupForm.indName"
            placeholder="请输入名称"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeAddGroup">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleAddGroup">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 添加指标 -->
    <el-dialog v-if="addIndDialog" append-to-body class="" :title="indOperateTitle" :visible="true" width="450px" :close-on-click-modal="false" @close="closeAddInd">
      <el-form ref="addIndForm" class="add-form" :model="addIndForm" size="small" :inline="true" :rules="addIndRules" label-width="110px">
        <el-form-item label="指标名称:" prop="indName">
          <el-input
            v-model="addIndForm.indName"
            placeholder="请输入名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="数据格式" prop="dataFormatId">
          <el-cascader
            v-model="addIndForm.dataFormatId"
            size="small"
            placeholder="请选择数据格式"
            :options="formatTree"
            :props="formatProps"
            :show-all-levels="false"
          />
        </el-form-item>
        <el-form-item label="向上汇总方式" prop="functionValue">
          <el-select v-model="addIndForm.functionValue" placeholder="请选择向上汇总方式" @change="changeFunction">
            <el-option
              v-for="item in polyFuncList"
              :key="item.funcVal"
              :label="item.funcName"
              :value="item.funcVal"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="加权列" prop="avgParmaIndId">
          <el-select v-model="addIndForm.avgParmaIndId" placeholder="请选择加权列" clearable>
            <el-option
              v-for="item in avgParmaIndArr"
              :key="item.id"
              :label="item.indName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeAddInd">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleAddInd">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 设置维度关联 -->
    <el-dialog v-if="setDimRelShow" append-to-body class="" :title="'设置维度关联'" :visible="true" width="1000px" :close-on-click-modal="false" @close="closeSetRel">
      <div class="dimen-area">
        <div class="top-area">
          <div class="left">
            <div class="label">选择条件维度:</div>
            <div class="dim-select">
              <el-select v-model="conditionDim" size="small" placeholder="请选择" @change="changeConditionDim">
                <el-option
                  v-for="item in dimensionList"
                  :key="item.id"
                  :label="item.dimDirectoryName"
                  :value="item.id"
                />
              </el-select>
            </div>
          </div>
          <div class="right">
            <div class="label">被影响维度:</div>
            <div class="dim-select">
              <el-select v-model="effectDim" size="small" placeholder="请选择" @change="changeEffectDim">
                <el-option
                  v-for="item in dimensionList"
                  :key="item.id"
                  :label="item.dimDirectoryName"
                  :value="item.id"
                  :disabled="item.id === conditionDim"
                />
              </el-select>
            </div>
          </div>
        </div>
        <div class="tree-area">
          <div class="left">
            <div class="top">成员名称</div>
            <el-tree :key="resTreeKey" default-expand-all highlight-current node-key="id" :expand-on-click-node="false" :data="conditionTree" :props="dimRelProps" :current-node-key="currentConditionId" @node-click="conditionNodeClick" />
          </div>
          <div class="right">
            <div class="top">成员名称</div>
            <el-tree ref="effectTreeRef" show-checkbox node-key="id" :expand-on-click-node="false" default-expand-all check-strictly :data="effectTree" :props="dimRelProps" />
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeSetRel">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleSetRel">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 重命名弹框 -->
    <el-dialog
      title="重新命名"
      append-to-body
      :visible.sync="reNameVisible"
      width="400px"
      :close-on-click-modal="false"
      @close="closeReName"
    >
      <el-form
        ref="reNameForm"
        label-position="right"
        label-width="60px"
        size="small"
        :model="reNameForm"
        :rules="reNameRules"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="reNameForm.name" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeReName">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleReName">确 定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>
<script>
// import VXETable from 'vxe-table'
import { cloneDeep } from 'lodash'
import { getDimensionTree, getMember } from '@/api/system/dimension-set'
import { getIndList, addInd, delInd, getPolyFuncList, getDataFormat, getDimRel, setDimRel } from '@/api/data-cube'
import { listToTree } from '@/utils/index'
export default {
  props: {
    currentNodeData: {
      type: Object,
      default: () => {}
    },
    isAddCube: {
      type: Boolean,
      default: true
    },
    cubeInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    var checkName = (rule, value, callback) => {
      const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9\uFF08\uFF09\uFF1A]+$/u
      if (!value) {
        return callback(new Error('请输入名称'))
      }
      if (value && !reg.test(value)) {
        return callback(new Error('名称只能为汉字、英文、数字、下划线、中文小括号、中文冒号的组合'))
      }
      callback()
    }
    return {
      formatTree: [],
      formatList: [],
      formatProps: { emitPath: false, expandTrigger: 'hover', value: 'id', label: 'groupInstanceName' },
      polyProps: { emitPath: true, expandTrigger: 'hover', value: 'funcVal', label: 'funcName', children: 'ruleFuncList' },
      defaultFormatId: '3b583866df798d08b8dd811f1f14fe0a',
      loading: false,
      tableName: '',
      addDimenDialog: false,
      dimensionList: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 20,
        totalRecords: 0
      },
      addCubeForm: {
        name: ''
      },
      memorySize: 10,
      dimensionTree: [],
      memberTree: [],
      dimenTreeProps: {
        children: 'children',
        label: 'dimDirectoryName'
      },
      memberTreeProps: {
        children: 'children',
        label: 'dimName'
      },
      loadingMemberTree: false,
      currentDimen: null,
      currentMemList: [],
      setIndDrawer: false,
      indTableList: [],
      loadingIndTable: false,
      addGroupDialog: false,
      addGroupTitle: '添加分组',
      addGroupForm: {
        indName: ''
      },
      addGroupRules: {
        indName: [
          { required: true, validator: checkName, trigger: 'blur' }
        ]
      },
      addCubeRules: {
        name: [
          { required: true, validator: checkName, trigger: 'blur' }
        ]
      },
      addIndDialog: false,
      addIndForm: {
        indName: '',
        dataFormatId: '',
        functionValue: '',
        functionName: '',
        avgParmaIndId: ''
      },
      addIndRules: {
        indName: [
          { required: true, validator: checkName, trigger: 'blur' }
        ],
        dataFormatId: [
          { required: true, message: '请选择数据格式', trigger: ['blur', 'change'] }
        ],
        functionValue: [
          { required: true, message: '请选择汇总方式', trigger: ['blur', 'change'] }
        ]
      },
      // 默认维度
      defaultDim: {},
      // 数方预览列
      previewCol: [],
      // 数方预览数据
      previewData: [],
      showPreviewSearch: false,
      polyFuncList: [],
      avgParmaIndArr: [],
      // 指标id自增
      currentIndId: '',
      parentInd: null,
      defaultDimId: null,
      defaultColumnDimId: null,
      refreshTimer: null,
      setDimRelShow: false,
      isAddInd: true,
      conditionDim: '',
      effectDim: '',
      conditionTree: [],
      effectTree: [],
      dimRelProps: {
        children: 'children',
        label: 'dimName'
      },
      treeTimer: null,
      currentConditionId: null,
      resTreeKey: Math.round(Math.random() * 10000 + 1),
      openIndDrawerCount: 0,
      reNameVisible: false,
      reNameForm: {
        name: ''
      },
      reNameRules: {
        name: [
          { required: true, validator: checkName, trigger: 'blur' }
        ]
      },
      curGroup: {}
    }
  },
  computed: {

    defaultFormat() {
      return this.formatList.filter(item => item.id === this.defaultFormatId)[0]
    },
    dimIdList() {
      return this.dimensionList.map(item => item.id)
    },
    previewSearchArr() {
      return this.dimensionList.filter(item => item.id !== this.defaultDimId)
    },
    parentNodeId() {
      return this.currentNodeData?.id || ''
    },
    dialogTitle() {
      return this.isAddCube ? '新增数方' : '修改数方'
    },
    indOperateTitle() {
      return this.isAddInd ? '添加指标' : '修改指标'
    }
  },
  watch: {
    defaultDim: {
      handler(val) {
        this.defaultDimId = val.id
      },
      deep: true,
      immediate: true
    }
    // conditionDim(val) {
    //   this.effectDim = ''
    //   this.effectTree = []
    //   const target = cloneDeep(this.dimensionList.find(item => item.id === val).memberList)
    //   target.forEach(item => {
    //     if (item.children) {
    //       delete item.children
    //     }
    //   })
    //   this.conditionTree = cloneDeep(listToTree(target))
    // },
    // effectDim(val) {
    //   this.getEffectTree(val)
    // }
  },
  created() {
    this.initDialog()
    this.getPolyFuncList()
    this.getDataFormat()
  },
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    // 打开重命名弹框
    openReName(row) {
      console.log(row)
      this.reNameVisible = true
      this.resetRenameForm()
      this.reNameForm.name = row.indName
      this.curGroup = row
    },
    resetRenameForm() {
      if (this.$refs.reNameForm) {
        this.$refs.reNameForm.resetFields()
      }
    },
    closeReName() {
      this.reNameVisible = false
    },
    handleReName() {
      this.$refs['reNameForm'].validate((valid) => {
        if (valid) {
          this.curGroup.indName = this.reNameForm.name
          this.indTableList.forEach(item => {
            if (item.id === this.curGroup.id) {
              item.indName = this.reNameForm.name
            }
          })
          this.reNameVisible = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    changeCheckBox(val, row) {
      if (val.checked) {
        this.defaultColumnDimId = row.id
      } else {
        this.defaultColumnDimId = null
      }
    },
    getConditionTree(val) {
      const target = cloneDeep(this.dimensionList.find(item => item.id === String(val)).memberList)
      target.forEach(item => {
        if (item.children) {
          delete item.children
        }
      })
      this.conditionTree = cloneDeep(listToTree(target))
    },
    getEffectTree(val, selectArr) {
      const target = cloneDeep(this.dimensionList.find(item => item.id === String(val)).memberList)
      target.forEach(item => {
        if (item.children) {
          delete item.children
        }
      })
      this.effectTree = cloneDeep(listToTree(target))
      const arr = selectArr || target.map(item => item.id)
      this.clearTreeTimer()
      this.treeTimer = setTimeout(() => {
        this.$refs.effectTreeRef.setCheckedKeys(arr)
      }, 0)
    },
    clearTreeTimer() {
      if (this.treeTimer) {
        window.clearTimeout(this.treeTimer)
      }
      this.treeTimer = null
    },
    editInd(row) {
      this.isAddInd = false
      this.addIndDialog = true
      // 每次加权列都不一样
      this.avgParmaIndArr = this.indTableList.length ? this.indTableList.filter((item) => item.indType === 'INSTANCE') : []
      this.addIndForm = { ...row }
      this.addIndForm.avgParmaIndId = this.addIndForm.avgParmaIndId ? Number(this.addIndForm.avgParmaIndId) : ''
    },
    swapElements(arr, index1, index2) {
      arr.splice(index1, 1, arr.splice(index2, 1, arr[index1])[0])
    },
    // 指标的上移下移
    moveItem(itemId, direction) {
      const data = cloneDeep(this.indTableList)
      console.log('初始数据', data)
      // 将数据分组为块，每个块包含父元素及其子元素
      const blocks = []
      let currentBlock = []
      for (const item of data) {
        if (item.parentId === '0') {
          if (currentBlock.length > 0) {
            blocks.push(currentBlock)
            currentBlock = []
          }
          currentBlock.push(item)
        } else {
          currentBlock.push(item)
        }
      }
      if (currentBlock.length > 0) {
        blocks.push(currentBlock)
      }

      // 查找目标元素所在的块索引
      let targetBlockIndex = -1
      for (let i = 0; i < blocks.length; i++) {
        if (blocks[i].some(item => item.id === itemId)) {
          targetBlockIndex = i
          break
        }
      }
      if (targetBlockIndex === -1) return data

      // 判断是父元素还是子元素
      const item = data.find(i => i.id === itemId)
      const isParent = item.parentId === '0'

      if (isParent) {
        // 移动父元素块
        const newIndex = direction === 'up' ? targetBlockIndex - 1 : targetBlockIndex + 1
        if (newIndex < 0 || newIndex >= blocks.length) return data;

        // 交换块位置
        [blocks[targetBlockIndex], blocks[newIndex]] = [blocks[newIndex], blocks[targetBlockIndex]]
        this.indTableList = blocks.flat()
        console.log('移动后的位置', this.indTableList)
      } else {
        // 移动子元素在其父块内的位置
        const parentBlock = blocks.find(block => block[0].id === item.parentId)
        if (!parentBlock) return data

        const childIndex = parentBlock.findIndex(child => child.id === itemId)
        if (childIndex <= 0) return data // 首元素为父元素，不能移动

        const newIndex = direction === 'up' ? childIndex - 1 : childIndex + 1
        if (newIndex <= 0 || newIndex >= parentBlock.length) return data;

        // 交换子元素位置
        [parentBlock[childIndex], parentBlock[newIndex]] = [parentBlock[newIndex], parentBlock[childIndex]]
        this.indTableList = blocks.flat()
        console.log('移动后的位置', this.indTableList)
      }
    },
    clearTimer() {
      if (this.refreshTimer) {
        window.clearTimeout(this.refreshTimer)
      }
      this.refreshTimer = null
    },
    changeRadio(val) {
      this.defaultDimId = val.label
    },
    checkMethod({ row }) {
      return true
    },
    initDialog() {
      this.addCubeForm.name = this.isAddCube ? '' : this.currentNodeData.tableName
      if (!this.isAddCube) {
        const { dimList, indList, defaultDimId, defaultColumnDimId } = this.cubeInfo
        this.defaultColumnDimId = defaultColumnDimId
        this.dimensionList = dimList
        this.indTableList = indList
        this.defaultDim = dimList.filter(item => item.id === defaultDimId)[0]
        this.getMemberCount(this.dimensionList)
      }
    },
    // 获取维度成员个数
    getMemberCount(arr) {
      arr.forEach(async node => {
        const params = {
          dimDirectoryId: node.id || '',
          dimName: ''
        }
        await getMember(params).then(res => {
          node.memberList = res.data
          node.memCount = res.data.length
        })
      })
      this.refreshTimer = setTimeout(() => {
        this.$refs.dimensionRef.reloadData(this.dimensionList)
        this.previewCube()
      }, 500)
    },
    formatDataFormat(id) {
      if (!id) return ''
      const target = this.formatList.find(item => item.id === id)
      return target ? target.groupInstanceName : ''
    },
    formatAvg(id) {
      if (!id) return ''
      const target = this.indTableList.find(item => item.avgParmaIndId === id)
      return target.indName
    },
    // 获取数据格式树
    getDataFormat() {
      getDataFormat().then((response) => {
        const { code, data } = response
        if (code !== 200) {
          this.$message.error('获取数据格式失败')
        }
        const treeData = listToTree(data)
        this.formatList = data
        this.formatTree = treeData
      })
    },
    changeFunction(val) {
      const target = this.polyFuncList.find(item => item.funcVal === val)
      this.addIndForm.functionName = target.funcName
    },
    // 获取聚合函数列表
    getPolyFuncList() {
      getPolyFuncList().then(res => {
        if (res.code === 200) {
          this.polyFuncList = this.formatCascaderTree(res.data)
        }
      })
    },
    formatCascaderTree(arr) {
      if (!arr || !arr.length) {
        return []
      }
      arr.forEach(item => {
        if (!item.ruleFuncList.length) {
          delete item.ruleFuncList
        }
      })
      return arr
    },
    radioChange({ row }) {
      console.log(row)
      this.defaultDim = row
    },
    handleSetInd() {
      if (this.indTableList.length === 0) {
        this.$message.error('请设置指标')
        return
      }
      this.$message.success('设置指标成功')
      this.closeIndDrawer()
    },
    // 数方预览
    previewCube() {
      if (!this.indTableList.length || !this.dimensionList.length) {
        this.$message.error('请先设置指标和添加维度')
        return
      } else {
        if (!this.defaultDimId) {
          this.$message.error('请先设置默认行维')
          return
        }
      }
      const originIndList = this.indTableList.map(item => {
        const { avgParmaIndId, dataFormatId, functionName, functionValue, id, indName, indType, parentId } = item
        return { avgParmaIndId, dataFormatId, functionName, functionValue, id, indName, indType, parentId }
      })
      this.showPreviewSearch = true
      this.previewCol = listToTree(cloneDeep(originIndList))
      this.previewData = this.dimensionList.filter(item => item.id === this.defaultDimId)[0].memberList
      console.log(this.previewData)
    },
    openAddGroup(row) {
      this.parentInd = row
      this.addGroupDialog = true
      this.addGroupTitle = row ? '添加子组' : '添加分组'
      this.addGroupForm.indName = ''
      this.$refs.addGroupForm && this.$refs.addGroupForm.resetFields()
    },
    closeAddGroup() {
      this.addGroupDialog = false
    },
    handleAddGroup() {
      this.$refs['addGroupForm'].validate((valid) => {
        if (valid) {
          const randomId = String(Math.round(Math.random() * 1000 + 1))
          const params = {
            indName: this.addGroupForm.indName,
            indType: 'GROUP',
            parentId: this.parentInd ? this.parentInd.id : '0',
            id: randomId
          }
          this.indTableList.push(params)
          this.$message.success('添加分组成功')
          this.addGroupDialog = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    openAddInd(row) {
      this.parentInd = row
      this.addIndDialog = true
      this.isAddInd = true
      this.addIndForm = {
        indName: '',
        dataFormatId: ''
      }
      this.$refs.addIndForm && this.$refs.addIndForm.resetFields()
      // 每次加权列都不一样
      this.avgParmaIndArr = this.indTableList.length ? this.indTableList.filter((item) => item.indType === 'INSTANCE') : []
    },
    closeAddInd() {
      this.addIndDialog = false
    },
    handleAddInd() {
      this.$refs['addIndForm'].validate((valid) => {
        if (valid) {
          if (this.isAddInd) {
            const randomId = String(Math.round(Math.random() * 1000 + 1))
            const extraParams = {
              indType: 'INSTANCE',
              parentId: this.parentInd ? this.parentInd.id : '0',
              id: randomId
            }
            const addParams = { ...this.addIndForm, ...extraParams }
            this.indTableList.push(addParams)
            this.$message.success('添加指标成功')
          } else {
            this.indTableList.forEach(item => {
              if (item.id === this.addIndForm.id) {
                const { indName, dataFormatId, functionName, functionValue, avgParmaIndId } = this.addIndForm
                item.indName = indName
                item.dataFormatId = dataFormatId
                item.functionName = functionName
                item.functionValue = functionValue
                item.avgParmaIndId = avgParmaIndId
                return
              }
            })
            this.$message.success('修改指标成功')
          }
          this.addIndDialog = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    delInd(row) {
      const typeText = row.indType === 'INSTANCE' ? '指标' : '分组'
      this.$confirm(`确定删除该${typeText}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.indTableList = this.indTableList.filter(item => item.id !== row.id)
      })
    },
    openIndDrawer() {
      this.setIndDrawer = true
      if (this.openIndDrawerCount === 0) {
        this.indTableList = this.isAddCube ? [] : this.cubeInfo.indList
        console.log('打开指标框', this.indTableList)
      }
      this.openIndDrawerCount++
      // this.loadingIndTable = true
      // this.getIndList()
    },
    closeIndDrawer() {
      this.setIndDrawer = false
    },
    getIndList() {
      getIndList().then(res => {
        this.indTableList = res.data ? cloneDeep(res.data) : []
      }).finally(() => {
        this.loadingIndTable = false
      })
    },
    conditionNodeClick(node) {
      this.currentConditionId = node.id
      this.getDimRelData(node.id)
    },
    handleDimenClick(node) {
      this.currentDimen = node
      const params = {
        dimDirectoryId: node.id || '',
        dimName: ''
      }
      this.loadingMemberTree = true
      getMember(params).then(res => {
        this.memberTree = listToTree(cloneDeep(res.data))
        this.currentMemList = res.data
      }).finally(() => {
        this.loadingMemberTree = false
      })
    },
    closeAddCube() {
      this.$emit('close')
    },
    getSortArr(arr) {
      const copyIndTable = cloneDeep(arr)
      const sortArr = []
      const idArr = []
      copyIndTable.forEach(item => {
        if (!idArr.includes(item.id)) {
          sortArr.push(item)
          idArr.push(item.id)
        }
        if (item.children && item.children.length) {
          item.children.forEach(child => {
            if (!idArr.includes(child.id)) {
              sortArr.push(child)
              idArr.push(child.id)
            }
          })
        }
      })
      return sortArr
    },
    handleAddCube() {
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          if (!this.isAddCube && (this.defaultDimId === this.defaultColumnDimId)) {
            this.$message.error('默认行维和默认列维不能相同')
            return
          }
          const sortArr = this.getSortArr(this.indTableList)
          const originIndList = sortArr.map(item => {
            const { avgParmaIndId, dataFormatId, functionName, functionValue, id, indName, indType, parentId } = item
            return { avgParmaIndId, dataFormatId, functionName, functionValue, id, indName, indType, parentId }
          })
          const indIdList = originIndList.map(item => item.id)
          const params = this.isAddCube ? {
            dimIdList: this.dimIdList,
            indIdList: indIdList,
            tableName: this.addCubeForm.name,
            indList: sortArr,
            defaultDimId: this.defaultDimId,
            defaultColumnDimId: this.defaultColumnDimId
          } : {
            id: this.currentNodeData.id,
            dimIdList: this.dimIdList,
            indIdList: indIdList,
            tableName: this.addCubeForm.name,
            indList: originIndList,
            defaultDimId: this.defaultDimId,
            defaultColumnDimId: this.defaultColumnDimId
          }

          this.$emit('addCube', params)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    openAddDimen() {
      this.addDimenDialog = true
      getDimensionTree().then(response => {
        if (response.data && response.data.length) {
          this.dimensionTree = listToTree(cloneDeep(response.data))
        }
      }).finally(() => {
      })
    },
    changeConditionDim(val) {
      this.effectDim = ''
      this.effectTree = []
      this.resTreeKey = Math.round(Math.random() * 10000 + 1)
      this.currentConditionId = null
      this.getConditionTree(val)
    },
    changeEffectDim(val) {
      this.getEffectTree(val)
    },
    setDimRelate() {
      getDimRel({ tableId: this.currentNodeData.id }).then(res => {
        if (res.code === 200) {
          const arr = cloneDeep(res.data)
          if (arr && arr.length) {
            const tarInstanceIds = arr.map(item => item.tarInstanceId)
            const validObj = arr[0]
            this.conditionDim = validObj.resDimDirectoryId
            this.effectDim = validObj.tarDimDirectoryId
            this.getConditionTree(validObj.resDimDirectoryId)
            this.getEffectTree(validObj.tarDimDirectoryId, tarInstanceIds)
            this.resTreeKey = Math.round(Math.random() * 10000 + 1)
            this.currentConditionId = validObj.resInstanceId
          } else {
            this.resTreeKey = Math.round(Math.random() * 10000 + 1)
            this.conditionDim = ''
            this.conditionTree = []
            this.effectDim = ''
            this.effectTree = []
            this.currentConditionId = null
          }
          this.setDimRelShow = true
        }
      })
    },
    getDimRelData(resInstanceId) {
      if (this.conditionDim && this.effectDim) {
        const params = {
          tableId: this.currentNodeData.id,
          resDimDirectoryId: this.conditionDim,
          tarDimDirectoryId: this.effectDim,
          resInstanceId
        }
        getDimRel(params).then(res => {
          if (res.code === 200) {
            const arr = cloneDeep(res.data)
            if (arr && arr.length) {
              const validObj = arr[0]
              const validResInstanceId = resInstanceId || validObj.resInstanceId
              const validArr = arr.filter(item => item.resInstanceId === validResInstanceId)
              const tarInstanceIds = validArr.map(item => item.tarInstanceId)
              // this.conditionDim = validObj.resDimDirectoryId
              // this.effectDim = validObj.tarDimDirectoryId
              // this.getConditionTree(this.resDimDirectoryId)
              this.getEffectTree(this.effectDim, tarInstanceIds)
              this.resTreeKey = Math.round(Math.random() * 10000 + 1)
              this.currentConditionId = validResInstanceId
            } else {
              this.resTreeKey = Math.round(Math.random() * 10000 + 1)
              this.currentConditionId = resInstanceId || null
              this.getEffectTree(this.effectDim)
            }
          }
        })
      }
    },
    // setDimRelate() {
    //   this.conditionDim = ''
    //   this.conditionTree = []
    //   this.effectDim = ''
    //   this.effectTree = []
    //   this.currentConditionId = null
    //   this.setDimRelShow = true
    // },
    closeSetRel() {
      this.setDimRelShow = false
    },
    handleSetRel() {
      if (!this.currentConditionId) {
        this.$message.error('请选择条件维度成员')
        return
      }
      console.log(this.$refs.effectTreeRef.getCheckedKeys())
      const params = {
        'resDimDirectoryId': this.conditionDim,
        'resInstanceId': this.currentConditionId,
        'tableId': this.currentNodeData.id,
        'tarDimDirectoryId': this.effectDim,
        'tarInstanceIdList': this.$refs.effectTreeRef.getCheckedKeys()
      }
      setDimRel(params).then(res => {
        if (res.code === 200) {
          this.setDimRelShow = false
          this.$message.success('设置成功')
        }
      })
    },
    closeAddDimen() {
      this.addDimenDialog = false
    },
    handleAddDimen() {
      if (!this.currentMemList || !this.currentMemList.length) {
        this.$message.error('请选择有维度成员的维度')
        return
      }
      const dimenIds = this.dimensionList && this.dimensionList.map(item => item.id)
      console.log(this.currentDimen, this.currentMemList)
      const addItem = {
        ...this.currentDimen, memberList: this.currentMemList, memCount: this.currentMemList.length
      }
      if (!dimenIds.includes(addItem.id)) {
        this.dimensionList.push(addItem)
        this.$message.success('添加维度成功')
        this.addDimenDialog = false
      } else {
        this.$message.error('不可重复添加维度')
      }
    },
    delDimension(row) {
      this.dimensionList = this.dimensionList.filter(item => item.id !== row.id)
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.add-table {
  .cus-header {
    display: flex;
    align-items: center;
    margin: 20px 0;
    div {
      margin: 0 5px;
    }
    button {
      margin-left: 20px;
    }
  }

  ::v-deep .el-dialog__body {
    padding:10px 20px;
  }
}
.popver-box {
  text-align: left;
  .popver-item {
    display: flex;
    align-items: center;
    margin: 15px 0;
    .title {
      margin: 0 10px;
      &.active {
        color: $primary;
        cursor: pointer;
      }
    }

  }
}
.filter-form {
  ::v-deep .el-form-item {
    margin-bottom: 0px;
  }
}
.bottom-area {
  display: flex;
  .bottom-left,.bottom-right {
    flex: 1;
    margin: 0 10px;
  }
  .bottom-left {
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }
}
.dimen-area {
  min-height: 400px;
  .title {
    font-size: 16px;
    text-align: center;
    color: $primary;
    font-weight: bold;
  }
  .top-area {
    width: 100%;
    display: flex;
    .left,.right {
      flex: 1;
      display: flex;
      align-items:center;
      .label {
        margin-right: 10px;
      }
    }
  }
}
.dimen-list {
  height: 400px;
  ::v-deep .vxe-table--header-wrapper {
    & .col--radio.col--last {
      display: none;
    }
  }
}
.tree-area {
  display: flex;
  .left,.right {
    flex: 1;
    margin: 10px;
    border: 1px solid #eee;
    padding: 10px;
    border-radius: 4px;
    height: 400px;
    overflow-y: auto;
  }
  .top {
    margin-bottom: 10px;
    color: $primary;
  }
}
.drawer-btn {
  display: flex;
  justify-content:flex-end;
  align-items: center;
  margin-bottom: 10px;
  &.save {
    margin: 10px;
  }
}
.set-ind {
  height: calc(100vh - 180px)
}
.add-form {
  .el-input,.el-select,.el-cascader {
    width: 250px;
  }
}
.preview-table {
  height: 250px;
}
.preview-cube {
  height: 400px;
  overflow-y: auto;
}
.operate-col {

}
</style>
