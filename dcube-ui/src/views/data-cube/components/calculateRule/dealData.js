export default {
  filters: {
    filterFormatMoney(val) {
      if (val === '-') return val
      if (!val) return ''
      if (isNaN(val)) return val
      val = (+val).toFixed(2)
      return (val + '').replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
    }
  },
  methods: {
    isEmpty(val, type) {
      if (!val) {
        return '-'
      } else {
        if (type == 'money') {
          val = (+val).toFixed(2)
        }
        return val
      }
    },
    // 数字转千分位
    handleFormatMoney(val) {
      if (val == '-') return val
      if (!val) return ''
      if (isNaN(val)) return val
      val = (+val).toFixed(2)
      return (val + '').replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
    },
    /**
     *
     * @param {*} val
     * @param {*} format  {"contentAlign":"left","decimalPlaces":0,"operationalRule":"value/10000",suffixChar:"万元",prefixChar:"￥"}
     * @returns
     */
    handleFormatMoneyFormat(val, format = {}) {
      const { decimalPlaces, operationalRule, suffixChar = '', prefixChar = '' } = format
      if (val === '-') return val
      if (!val) return ''
      if (isNaN(val)) return val
      val = (+val).toFixed(decimalPlaces || 2)
      let value = val
      value = operationalRule ? (eval(encodeURI(operationalRule))).toFixed(decimalPlaces || 2) : val
      val = value
      if (!format || !Object.keys(format).length) {
        val = (val + '').replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
      }
      return `${prefixChar}${val}${suffixChar}`
    }
  }
}
