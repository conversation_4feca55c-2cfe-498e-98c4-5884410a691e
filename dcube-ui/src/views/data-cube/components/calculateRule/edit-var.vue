<!-- 文本组件，文本组件里可以输入变量 -->

<template>
  <div class="text-wrap">
    <div class="left">
      <el-input
        v-model="inputValLeft"
        :readonly="true"
        placeholder="请点击鼠标右键选择变量"
        type="text"
        @blur="handleBlur"
        @contextmenu.native.prevent.stop="(e)=> showMenu(e,'Left')"
      />
    </div>
    <span class="minus">=</span>
    <div class="right">
      <!-- <div class="operate-area">
        <div class="box" @click="operateVar('+')"><span class="iconfont icon-jia" /></div>
        <div class="box" @click="operateVar('-')"><span class="iconfont icon-jian" /></div>
        <div class="box" @click="operateVar('*')"><span class="iconfont icon-cheng" /></div>
        <div class="box" @click="operateVar('/')"><span class="iconfont icon-chu" /></div>
      </div> -->
      <el-input
        v-model="inputValRight"
        placeholder="请点击鼠标右键选择变量"
        type="text"
        @blur="handleBlur"
        @contextmenu.native.prevent.stop="(e)=>showMenu(e,'Right')"
      />
    </div>

    <div class="save-btn">
      <el-button type="primary">保存</el-button>
    </div>
    <div
      v-if="isShowMenu"
      :style="{'left': menuLeft + 'px', 'top': menuTop + 'px'}"
      class="customer-menu"
    >
      <div class="menu-first">
        <div>
          选择变量
          <div class="menu-second">
            <div
              v-for="sysVar in variablesList"
              :key="sysVar.id"
              @click="handleSelectVar(sysVar)"
            >
              {{ sysVar.variableName }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div></template>

<script>
import dealData from './dealData'
export default {
  mixins: [dealData],
  props: {
    variablesList: { // 变量获取的最终数值
      type: Array,
      default() {
        return []
      }
    },
    dimTableId: {
      type: String,
      default: ''
    },
    text: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      menuLeft: 0,
      menuTop: 0,
      isShowMenu: false,
      inputValLeft: '',
      inputValRight: '',
      curDirection: 'Left'
    }
  },
  computed: {

  },
  watch: {

  },
  methods: {
    handleBlur() {
      if (this.isShowMenu) {
        setTimeout(() => {
          this.isShowMenu = false
        }, 1000)
      }
    },
    handleSelectVar(varItem) {
      if (this.curDirection === 'Left') {
        if (varItem.dimTableId !== this.dimTableId) {
          this.$message.error('请选择由本数方构建的多维变量作为等式左侧值！')
          return
        }
        this[`inputVal${this.curDirection}`] = `$${varItem.variableName}$`
      } else {
        this[`inputVal${this.curDirection}`] += `$${varItem.variableName}$`
      }
      this.isShowMenu = false
    },
    operateVar(type) {
      switch (type) {
        case '+':
          this.inputValRight += '+'
          break
        case '-':
          this.inputValRight += '-'
          break
        case '*':
          this.inputValRight += '×'
          break
        case '/':
          this.inputValRight += '÷'
          break
      }
    },
    showMenu(e, dic) {
      this.isShowMenu = true
      this.curDirection = dic
      this.menuLeft = e.pageX
      this.menuTop = e.pageY
    },
    /**
     * 点击的变量名需要弹出配置项来存储值
     * @param item
     */
    handleConfigVar(item) {
      console.log(item, 'iiiii')
      this.$emit('handleConfigVar', item)
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/variables';
.text-wrap{
  display: flex;
  align-items: center;
  font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
  ::v-deep{
    .el-textarea__inner{
      border:none;
    }
  }
  .left {
    flex: 1;
  }
  .minus {
    margin: 0 10px;
    font-size: 18px;
    font-weight: 500;
  }
  .right {
    flex: 3;
    position: relative;
    .operate-area {
      position: absolute;
      z-index: 1;
      top: -28px;
      left: 0px;
      display: flex;
      .box {
        background: $primary;
        color: #fff;
        padding: 5px;
        margin: 0 5px;
        cursor: pointer;
      }
    }
  }
  .save-btn {
    margin-left: 30px;
  }
  .el-input {
    flex: 1;
  }
  .content{
    padding: 5px 15px;
    font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
    font-size:12px;
    .isVar{
      color:$primary;
      // text-decoration: underline;
      cursor: pointer;
    }
  }
  .customer-menu{
    position: fixed;
    z-index: 1004;
    background-color: white;
    border-radius: 5px;
    text-align: left;
    font-size:14px;
    color:#606266;
    box-shadow: 0 0 5px 1px #ccc;
    padding: 6px;
    .menu-first{
      padding:10px;
      margin:0;
      font-weight: 700;
      .menu-second{
        padding-left:25px;
        font-weight: 400;
        max-height:100px;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 6px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: #c1c1c1;
          border-radius: 8px;
        }
        scrollbar-color: #c1c1c1 transparent;
        scrollbar-width: thin;
        &>div{
          cursor: pointer;
          line-height: 25px;
          &:hover{
            color:#004DE7;
          }
        }
      }
    }
  }
}

</style>
