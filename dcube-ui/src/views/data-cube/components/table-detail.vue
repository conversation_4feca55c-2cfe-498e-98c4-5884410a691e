<template>
  <div>
    <div v-if="cubeFlowRow.tableId===curTableDetail.id" class="current-node-bar">当前节点：{{ cubeFlowRow.nodeName }}</div>
    <div v-if="cubeFlowRow.tableId===curTableDetail.id" class="cube-flow-btns">
      <div style="flex:1">
        <template v-if="handleNodes.length>0">
          <span style="fontSize:13px">待处理节点：</span>
          <el-select v-model="handleFlowNode" class="todo-node-select" size="small" placeholder="请选择">
            <el-option
              v-for="item in handleNodes"
              :key="item.id"
              :label="item.nodeName"
              :value="item.id"
              :disabled="item.taskState==='PRE_SUBMIT'"
            >
              <span style="float: left">{{ item.nodeName }}</span>
              <span style="float: right; font-size: 13px">{{ item.taskStateName }}</span>
            </el-option>
          </el-select>
          <el-button size="mini" type="primary" @click="handleAgree">同意</el-button>
          <el-button size="mini" type="primary" @click="handleReject">退回</el-button>
          <el-button size="mini" type="primary" @click="lookCentralizedInfo">查看归口审批信息</el-button>
        </template>
      </div>
      <div>
        <el-button size="mini" type="primary" @click="getDeptApproveList">查看节点状态</el-button>
        <el-button
          v-if="!cubeFlowRow.centralizeDimDefineId"
          size="mini"
          type="primary"
          @click="openFlowModal('SUBMIT')"
        >提交
        </el-button>
        <el-button size="mini" type="primary" @click="backFlowList">返回</el-button>
      </div>
    </div>
    <div class="btn-group">
      <div v-if="enableEditRow" class="left-btns">
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="saveTableData">
          <div class="btn-icon el-icon-check" />
          <div class="btn-text">保存数据</div>
        </div>
        <div class="btn-box" @click="refreshTable">
          <div class="btn-icon el-icon-refresh" />
          <div class="btn-text">刷 新</div>
        </div>
        <div class="gap-line" />
        <div class="btn-box" @click="impData">
          <div class="btn-icon el-icon-upload" />
          <div class="btn-text">导 入</div>
        </div>
        <div class="btn-box" @click="exportData">
          <div class="btn-icon el-icon-download" />
          <div class="btn-text">导 出</div>
        </div>
        <div class="gap-line" />
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="handleOrigin">
          <div class="btn-icon el-icon-receiving" />
          <div class="btn-text">载入源数据</div>
        </div>
        <div class="btn-box" :class="{'disabled':disableEditRow}" @click="executeRule">
          <div class="btn-icon el-icon-caret-right" />
          <div class="btn-text">执行规则</div>
        </div>
      </div>
      <div class="dim-icon" @click="showDimFilter">
        维度切换
      </div>
    </div>
    <div class="main-area">
      <div class="table-list" :style="{width:dimFilterShow?'calc(100vw - 320px)':'calc(100vw)'}">
        <div v-if="dynamicSearchArr.length" class="search-container">
          <!-- 动态查询条件 -->
          <el-form ref="searchForm" :inline="true" class="search-form" size="mini">
            <el-form-item v-for="item in dynamicSearchArr" :key="item.id" :label="item.dimName">
              <treeselect v-model="item.selectDimId" :flat="true" :clearable="false" :normalizer="normalizer" :options="item.options" :show-count="true" placeholder="请选择" @select="(val)=>selectFilterDim(val,item)" />
            </el-form-item>
          </el-form>
        </div>
        <!-- 配置式表格 -->
        <div class="table-box" :class="{'no-search':dynamicSearchArr.length===0}" :style="{'--height':tableHeight}">
          <config-table ref="configTable" :config-data="configData" :loading="loading" />
        </div>
      </div>
      <!-- 维度设置 -->
      <div v-if="dimFilterShow" class="dim-switch">
        <div class="bottom-dim">
          <div class="dim-area">
            <div class="dim-title">
              <div class="left">筛选维</div>
              <div class="el-icon-close" @click="closeDimFilter" />
            </div>
            <draggable :list="filterDimList" class="filter-dim" chosen-class="chosen" force-fallback="true" group="dimension" animation="1000" :move="onMoveDim">
              <div v-for="item in filterDimList" :key="item.id" class="drag-item" @click.stop.prevent="openDimTree(item)">
                <el-tag size="small">
                  <span class="dim-name">{{ item.dimName }}</span>
                  <!-- <span class="vxe-icon-chart-radar" /> -->
                  <span v-if="item.show" class="vxe-icon-eye-fill" @click.stop.prevent="clickDimEye(item,'filter',false)" />
                  <span v-if="!item.show" class="vxe-icon-eye-fill-close" @click.stop.prevent="clickDimEye(item,'filter',true)" />
                </el-tag>
              </div>
            </draggable>
          </div>
          <div class="dim-area">
            <div class="dim-title">行维(最多3个)</div>
            <draggable :list="rowDimList" class="row-dim" chosen-class="chosen" force-fallback="true" group="dimension" animation="1000" :move="onMoveDim">
              <div v-for="item in rowDimList" :key="item.id" class="drag-item" @click.stop.prevent="openDimTree(item)">
                <el-tag size="small">
                  <span class="dim-name">{{ item.dimName }}</span>
                  <!-- <span class="vxe-icon-chart-radar" /> -->
                  <span v-if="item.show" class="vxe-icon-eye-fill" @click.stop.prevent="clickDimEye(item,'row',false)" />
                  <span v-if="!item.show" class="vxe-icon-eye-fill-close" @click.stop.prevent="clickDimEye(item,'row',true)" />
                </el-tag>
              </div>
            </draggable>
          </div>
          <div class="dim-area">
            <div class="dim-title">列维(最多2个)</div>
            <draggable :list="colDimList" class="col-dim" chosen-class="chosen" force-fallback="true" group="dimension" animation="1000" :move="onMoveDim">
              <div v-for="item in colDimList" :key="item.id" class="drag-item" @click.stop.prevent="openDimTree(item)">
                <el-tag size="small">
                  <span class="dim-name">{{ item.dimName }}</span>
                  <!-- <span class="vxe-icon-chart-radar" /> -->
                  <span v-if="item.show" class="vxe-icon-eye-fill" @click.stop.prevent="clickDimEye(item,'col',false)" />
                  <span v-if="!item.show" class="vxe-icon-eye-fill-close" @click.stop.prevent="clickDimEye(item,'col',true)" />
                </el-tag>
              </div>
            </draggable>
          </div>
        </div>
        <div class="save-style">
          <el-button type="primary" size="large" @click="saveStyle">保存查看样式</el-button>
        </div>
      </div>
    </div>
    <!-- 载入源数据 -->
    <el-dialog title="载入源数据" :visible.sync="showOrigin" width="400px" :close-on-click-modal="false" @close="closeOrigin">
      <el-form ref="originForm" label-position="right" label-width="90px" size="small" :model="originForm">
        <el-form-item label="数据源名称" prop="name">
          <el-select v-model="originForm.name" :disabled="true" class="w-full" placeholder="请选择">
            <el-option
              v-for="item in originList"
              :key="item.id"
              :label="item.viewName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeOrigin">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleOrigin">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 维度成员显示/隐藏 -->
    <el-dialog :title="'维度成员显示/隐藏'" :visible.sync="dimTreeShow" width="600px" append-to-body @close="closeDimTree">
      <div class="dim-tree">
        <div class="top-area">
          <div class="left">成员名称</div>
          <div class="right">
            <el-checkbox v-model="checkStrick" @change="handleCheckedTreeConnect($event)">父子联动</el-checkbox>
            <el-button type="text" status="primary" size="mini" @click="checkDimTree(1)">仅选中1级</el-button>
            <el-button type="text" status="primary" size="mini" @click="checkDimTree(2)">选中到2级</el-button>
            <el-button type="text" status="primary" size="mini" @click="checkDimTree(3)">选中到3级</el-button>
          </div>
        </div>
        <el-tree
          ref="dimTree"
          class="tree-border"
          :data="dimTreeData"
          show-checkbox
          default-expand-all
          node-key="id"
          :check-strictly="!checkStrick"
          empty-text="加载中，请稍候"
          :props="defaultProps"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeDimTree">取 消</el-button>
        <el-button size="mini" type="primary" @click="changeDimMember">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 审批提交 -->
    <el-dialog v-if="showFlowModal" :visible="showFlowModal" width="600px" append-to-body center :close-on-click-modal="false" @close="showFlowModal=false">
      <div slot="title">
        <span style="fontWeight:500">{{ cubeFlowRow.nodeName }} {{ cubeFlowRow.userName }}</span>
        {{ flowModalType==='AGREE'?' 同意 ':flowModalType==='RETURN'?' 退回 ':' 提交至 ' }}
        <span style="fontWeight:500">{{ flowModalType==='AGREE'||flowModalType==='RETURN' ? this.handleNodes.find(item => item.id === this.handleFlowNode).nodeName : nextSubmitNodeName }}</span>
      </div>
      <el-input
        v-model="commitContent"
        type="textarea"
        placeholder="请输入备注意见"
        rows="4"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="showFlowModal=false">取 消</el-button>
        <el-button size="mini" type="primary" @click="saveFlowHandle">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 查看归口审批信息 -->
    <el-dialog v-if="showCenralizedModal" :visible="showCenralizedModal" :title="`查看${centralizedModalTitle}归口节点审批状态`" width="600px" append-to-body center :close-on-click-modal="false" @close="showCenralizedModal=false">
      <el-table :data="centralizedStatusList">
        <el-table-column prop="centralizeName" label="归口节点名称" width="180" />
        <el-table-column prop="userName" label="审批人" width="180" />
        <el-table-column prop="taskStateName" label="审批状态" />
        <el-table-column label="操作" width="100">
          <template slot-scope="{row}">
            <el-button type="text" size="small" @click="lookApproveRecord(row)">查看审批记录</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 查看审批记录 -->
    <el-dialog
      v-if="showApproveModal"
      :visible="showApproveModal"
      title="查看审批记录"
      width="600px"
      append-to-body
      center
      :close-on-click-modal="false"
      @close="showApproveModal=false"
    >
      <el-steps direction="vertical" :active="approveList.length">
        <div style="textAlign:right;marginBottom: 8px;">
          <el-link :underline="false" @click="sortData('asc')">升序</el-link>
          |
          <el-link :underline="false" @click="sortData('desc')">降序</el-link>
        </div>
        <el-step v-for="item in approveList" v-if="approveList.length" :key="item.id" class="flow-approve-steps">
          <div slot="title" style="color: black;">
            <span>{{ item.operateTypeName }}</span>
            <span style="margin-left: 24px;">处理人：{{ item.userName }}</span>
            <span style="float: right">{{ item.createTime }}</span>
          </div>
          <div slot="description" style="lineHeight:24px;background: #f2f2f2;padding: 0 8px;color: #303133">
            <span>{{ item.commentContent }}</span>
          </div>
        </el-step>
      </el-steps>
      <div v-if="!approveList||!approveList.length" style="textAlign:center;lineHeight:64px;">暂无数据</div>
    </el-dialog>
    <!-- 查看节点状态 -->
    <el-dialog v-if="nodeStatusModal" :visible="nodeStatusModal" title="查看节点状态" width="60%" append-to-body center :close-on-click-modal="false" @close="nodeStatusModal=false">
      <vxe-table
        ref="tableRef"
        style="height: 60vh;overflow: auto;"
        border="inner"
        :column-config="{resizable: true}"
        :tree-config="{transform: true, rowField: 'nodeId', parentField: 'parentId',expandAll: true}"
        :data="deptApproveTree"
      >
        <vxe-column field="nodeName" title="节点名称" tree-node />
        <vxe-column field="userName" title="审批人" />
        <vxe-column field="taskStateName" title="节点状态" />
        <vxe-column title="查看详情信息">
          <template #default="{ row }">
            <el-button
              :style="row.centralizeList&&row.centralizeList.length?'':'visibility: hidden;'"
              type="text"
              size="small"
              @click="lookCentralizedNode(row)"
            >查看归口节点
            </el-button>
            <el-button type="text" size="small" @click="lookApproveRecord(row)">查看审批记录</el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </el-dialog>
  </div>
</template>

<script>
// import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { cloneDeep } from 'lodash'
import calcRules from '../rule'
import { getViewList } from '@/api/system/data-view'
import { createExcel, listToTree } from '@/utils/index'
import Treeselect from '@riophae/vue-treeselect'
import {
  downloadTemplate,
  executeDimRule,
  exportTable,
  getDim,
  getDimRel,
  getTableDetail,
  getTableKey,
  impData,
  loadOriginData,
  saveTable,
  saveLayout
} from '@/api/data-cube'
import { getMember } from '@/api/system/dimension-set'
import { spiltDownload } from '@/api/system/approve-role'
import VXETable from 'vxe-table'
import draggable from 'vuedraggable'
import ConfigTable from './config-table.vue'
import {
  getCommitParamsById,
  getFlowCubeChild,
  getRecordAgree,
  getRecordMsgList,
  viewNodeState
} from '@/api/approve-progress.js'
import moment from 'moment'

export default {
  components: {
    draggable,
    ConfigTable,
    Treeselect
  },
  props: {
    curTableDetail: {
      type: Object,
      default: () => {}
    },
    dictType: {
      type: Array,
      default: () => []
    },
    tablePermission: {
      type: [Object, null],
      default: null
    },
    cubeFlowRow: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      originIndList: [],
      searchOpts1: Object.freeze([{
        label: '等于',
        value: '0'
      }, {
        label: '小于',
        value: '-1'
      }, {
        label: '小于等于',
        value: '-2'
      }, {
        label: '大于',
        value: '1'
      }, {
        label: '大于等于',
        value: '2'
      }]),
      searchOpts2: Object.freeze([{
        label: '精确',
        value: '0'
      }, {
        label: '模糊',
        value: '-1'
      }]),
      orgUserDict: Object.freeze(['ORG', 'USER', 'DICT']),
      deptTree: this.$store.getters.deptTree,
      numType: Object.freeze(['INTEGER', 'DOUBLE']),
      sumavgObj: {
        a: null,
        s: null
      },
      loading: false,
      // 动态查询数组
      dynamicSearchArr: [],
      originDynamicSearchArr: [],
      orgArr: [],
      customTypeArr: [],
      currentNo: 1,
      // 显示值表格
      viewTable: [],
      // 原始值表格
      originTable: [],
      pageInfo: {
        currentPage: 1,
        pageSize: 20
      },
      totalRecords: 0,
      calcVisible: false,
      calcForm: {
        rule: ''
      },
      calcRules: {},
      disableEditRow: false,
      disableEditCol: false,
      filterOption: Object.freeze([{
        label: '不作为查询条件',
        value: null
      }, {
        label: '作为查询条件',
        value: true
      }]),
      relateOption: Object.freeze([{
        label: '不关联系统参数',
        value: null
      }, {
        label: '关联机构',
        value: 'ORG'
      }, {
        label: '关联人员',
        value: 'USER'
      }]),
      curRow: null,
      curCol: null,
      // 字典集
      dictMap: {},
      // 选择人员id和nickName集合,方便单元格格式化显示
      userMap: {},
      // 机构的id和name集合,方便单元格格式化显示
      orgMap: {},
      sortable2: null,
      tableClientW: 0,
      tableOffsetW: 0,
      // 查看修改痕迹
      showModifyHis: false,
      allColWArr: [],
      formatTree: this.$store.getters.formatListTree,
      formatList: this.$store.getters.formatList,
      formatProps: { emitPath: false, expandTrigger: 'hover', value: 'id', label: 'groupInstanceName' },
      defaultFormatId: '3b583866df798d08b8dd811f1f14fe0a',
      // 排序参数集合
      sortList: [],
      // 记录行编辑过的列code
      editRowColMap: {},
      relateSystemCol: null,
      treeKey: Math.round(Math.random() * 1000 + 1),
      processInfo: null,
      processTimer: null,
      // 刷新表格时默认更新查询条件(如果是点击查询按钮则不更新查询条件)
      updateSearch: true,
      // 规则快捷输入
      quickInputOpts: {},
      quickForm: { o: '', p: '', expressIf: '', t: [], c: [], func: [] },
      opifKey: Object.freeze(['o', 'p']),
      showBackUp: false,
      backUpForm: { name: '' },
      backUpRules: {
        name: [{
          required: true,
          trigger: 'blur',
          message: '请输入版本名称'
        }]
      },
      showRevert: false,
      revertForm: {
        name: ''
      },
      versionList: [],
      originList: [],
      showOrigin: false,
      originForm: {
        name: ''
      },
      validStatus: ['START', 'COMPLETE'],
      // 数据源id
      curViewId: '',
      scrollTimer: null,
      user: this.$store.getters.user,
      socket: null,
      exportInfo: {
        msg: '',
        process: 0,
        extra: ''
      },
      operateTime: '',
      localhost: window.location.host,
      taskInfoTimer: null,
      requestCount: 0,
      showDownloadProcess: false,
      downloadPercentage: 0,
      filesCurrentPage: 0, // 文件开始偏移量
      fileFinalOffset: 0, // 文件最后偏移量
      stopRecursiveTags: true, // 停止递归标签，默认是true 继续进行递归
      contentList: [], // 文件流数组
      fileName: '',
      // 子表弹框
      subTableShow: false,
      // 子表id
      relSubTableId: '',
      // 父表主键code
      parentTableKeyCode: '',
      // 父表主键的值
      parentTableKeyVal: '',
      rowDimList: [],
      colDimList: [],
      filterDimList: [],
      // colGroup: {
      //   name: 'dimension',
      //   put: () => {
      //     if (this.colDimList.length >= 1) {
      //       return false
      //     }
      //     return true
      //   }
      // },
      dimFilterShow: true,
      filterDimIds: [],
      rowDimIds: [],
      colDimIds: [],
      loadTimer: null,
      // 配置表格原始数据
      configData: {},
      allDimData: {},
      filterList: [],
      selectTimer: null,
      dimTreeShow: false,
      defaultProps: {
        children: 'children',
        label: 'dimName'
      },
      checkStrick: false,
      dimTreeData: [],
      originDimTree: [],
      checkTreeTimer: null,
      showDimList: [],
      curShowHideDim: null,
      conditionDim: '', // 条件维度只能作为筛选维
      messageTimer: null,
      showFlowModal: false, // 审批弹框
      flowModalType: 'SUBMIT',
      commitContent: '',
      handleNodes: [],
      handleFlowNode: '', // 待处理节点
      showCenralizedModal: false,
      centralizedModalTitle: '', // 归口审批信息弹框标题
      centralizedStatusList: [],
      showApproveModal: false, // 审批记录弹框
      approveList: [],
      nodeStatusModal: false,
      deptApproveTree: [],
      nextSubmitNodeName: '', // 流程提交下一节点名称
      totalSearchRows: 1,
      defaultColumnDimId: '',
      defaultDimId: '',
      dimTypeIdObj: {}
    }
  },
  computed: {
    childFlag() {
      return this.curTableDetail.childFlag
    },
    userId() {
      return this.user.userId
    },
    curTableId() {
      return this.curTableDetail && this.curTableDetail.id || ''
    },
    defaultFormat() {
      return this.formatList.filter(item => item.id === this.defaultFormatId)[0]
    },
    // 是否能编辑表格数据
    enableEditRow() {
      return (this.tablePermission && this.tablePermission.dataEdition === 1) || true
    },
    // 是否能定义列
    enableEditCol() {
      return (this.tablePermission && this.tablePermission.tableDefinition === 1) || false
    },
    tableHeight() {
      let h = 'calc(100vh - 200px)'
      if (this.dynamicSearchArr.length) {
        h = `calc(100vh - ${205 + this.totalSearchRows * 50}px)`
      }
      return h
    }
  },
  watch: {
    async filterDimList(val) {
      console.log('筛选维更新', val)
      this.filterChange()
      this.initResizeObserver()
    },
    rowDimList(val) {
      console.log('行维更新', val)
      this.changeRowColDim('row')
    },
    colDimList(val) {
      console.log('列维更新', val)
      this.changeRowColDim('col')
    },
    curTableDetail: {
      handler(newVal, oldVal) {
        if (newVal && newVal.id) {
          const { id } = newVal
          // 获取条件维度
          this.getConditionDim(id)
          // 初始化请求条件
          this.initTableParams()
          this.fetchData(id)
        }
      },
      immediate: true,
      deep: true
    },
    sortList: {
      handler(newVal) {
        if (newVal && newVal.length) {
          this.refreshTable()
        }
      },
      immediate: true,
      deep: true
    },
    cubeFlowRow: {
      handler(newVal, oldVal) {
        if (newVal && newVal.nodeId) {
          const { id } = newVal
          this.getFlowHandleNodes(id)
        }
      },
      immediate: true,
      deep: true
    }

  },
  created() {
    this.calcRules = calcRules.createCalcRule(this.calcForm, this.curTableId)
    if (this.cubeFlowRow.tableId === this.curTableDetail.id) {
      getCommitParamsById({ taskId: this.cubeFlowRow.id, flowType: this.cubeFlowRow.flowType }).then(res => {
        this.nextSubmitNodeName = res.data.nextNodeName
      })
    }
  },
  beforeDestroy() {
    if (this.sortable2) {
      this.sortable2.destroy()
    }
    this.clearLoadTimer()
    this.clearSelectTimer()
    this.clearCheckTimer()
    this.clearMessageTimer()
  },
  methods: {
    // 初始化ResizeObserver监听
    initResizeObserver() {
      this.resizeObserver = new ResizeObserver(this.calculateRows)
      this.$nextTick(() => {
        if (this.$refs.searchForm) {
          const container = this.$refs.searchForm.$el
          if (container) this.resizeObserver.observe(container)
        }
      })
    },

    // 计算行数（优化版）
    calculateRows() {
      const items = this.$refs.searchForm.$el.querySelectorAll('.el-form-item')
      if (items.length === 0) return
      let rowCount = 1
      let prevTop = null
      items.forEach((item, index) => {
        const rect = item.getBoundingClientRect()
        if (index === 0) {
          prevTop = rect.top
        } else if (rect.top > prevTop + 5) { // 允许5px的误差
          rowCount++
          prevTop = rect.top
        }
      })
      this.totalSearchRows = rowCount
    },
    executeRule() {
      const params = new FormData()
      params.append('dimTableId', this.curTableId)
      executeDimRule(params).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg)
        }
      })
    },
    getConditionDim(id) {
      getDimRel({ tableId: id }).then(res => {
        if (res.code === 200) {
          const arr = cloneDeep(res.data)
          if (arr && arr.length) {
            this.conditionDim = arr[0].resDimDirectoryId
          }
        }
      })
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value) {
      this.checkStrick = !!value
    },
    // 勾选到几级
    checkDimTree(idx) {
      const arr = cloneDeep(this.originDimTree)
      const checkIdArr = []
      if (idx === 1) {
        arr.forEach(item => {
          checkIdArr.push(item.id)
        })
      } else if (idx === 2) {
        arr.forEach(item => {
          checkIdArr.push(item.id)
          if (item.children && item.children.length) {
            item.children.forEach(child => {
              checkIdArr.push(child.id)
            })
          }
        })
      } else {
        arr.forEach(item => {
          checkIdArr.push(item.id)
          if (item.children && item.children.length) {
            item.children.forEach(child => {
              checkIdArr.push(child.id)
              if (child.children && child.children.length) {
                child.children.forEach(grand => {
                  checkIdArr.push(grand.id)
                })
              }
            })
          }
        })
      }
      this.clearCheckTimer()
      this.checkTreeTimer = setTimeout(() => {
        this.$refs.dimTree.setCheckedKeys(checkIdArr)
      }, 10)
    },
    changeDimMember() {
      if (!this.$refs.dimTree.getCheckedKeys().length) {
        this.$message.error('请选择维度成员')
        return
      }
      if (this.curShowHideDim && this.curShowHideDim.id) {
        const selectIds = this.$refs.dimTree.getCheckedKeys()
        // 父子联动时获取半选的id集合
        const halfIds = this.$refs.dimTree.getHalfCheckedKeys()
        const allSelectIds = [...selectIds, ...halfIds]
        console.log('选择的维度实例集合', selectIds, halfIds, allSelectIds)
        const target = this.showDimList.find(dim => dim.dimId === this.curShowHideDim.id)
        if (target && target.dimId) {
          target.instanceIds = allSelectIds
        } else {
          this.showDimList.push({
            dimId: this.curShowHideDim.id,
            instanceIds: allSelectIds
          })
        }
        this.dynamicSearchArr.forEach(item => {
          if (item.id === this.curShowHideDim.id) {
            const filterArr = cloneDeep(item.originOptions.filter(opt => allSelectIds.includes(opt.id)))
            item.options = listToTree(filterArr)
            item.selectDimId = item.options[0].id
          }
        })
        this.originDynamicSearchArr = cloneDeep(this.dynamicSearchArr)
        this.load2dTable()
        this.dimTreeShow = false
      }
    },
    closeDimTree() {
      this.dimTreeData = []
      this.dimTreeShow = false
    },
    openDimTree(item) {
      if (item.dimName === '指标') return
      this.checkStrick = false
      this.dimTreeShow = true
      this.curShowHideDim = item
      const arr = item.dimName === '指标' ? item.children : this.allDimData[item.id]
      this.dimTreeData = listToTree(cloneDeep(arr))
      this.originDimTree = cloneDeep(this.dimTreeData)
      const target = this.showDimList.find(dim => dim.dimId === item.id)
      const idArr = target ? target.instanceIds : arr.map(item => item.id)
      this.clearCheckTimer()
      this.checkTreeTimer = setTimeout(() => {
        this.$refs.dimTree.setCheckedKeys(idArr)
      }, 10)
    },
    clearCheckTimer() {
      if (this.checkTreeTimer) {
        window.clearTimeout(this.checkTreeTimer)
      }
      this.checkTreeTimer = null
    },
    clearMessageTimer() {
      if (this.messageTimer) {
        window.clearTimeout(this.messageTimer)
      }
      this.messageTimer = null
    },
    changeRowColDim(type) {
      const val = type === 'row' ? this.rowDimList : this.colDimList
      const validArr = val.filter(item => item.show)
      const arr = this.getAllIds(validArr)
      if (type === 'row') {
        this.rowDimIds = arr
      } else {
        this.colDimIds = arr
      }
      this.callBackFun()
    },
    async  filterChange() {
      this.dynamicSearchArr = this.filterDimList.filter(item => item.show)
      const _this = this
      if (this.dynamicSearchArr.length) {
        async function loopGetFilterOps() {
          for (let i = 0; i < _this.dynamicSearchArr.length; i++) {
            const item = _this.dynamicSearchArr[i]
            item.type = 'filterDim'
            if (item.dimName === '指标') {
              item.options = listToTree(cloneDeep(item.children))
              item.originOptions = cloneDeep(item.children)
              function judgeDisabled(nodeArr) {
                nodeArr.forEach(node => {
                  if (node.indType === 'GROUP') {
                    node.isDisabled = true
                  } else {
                    node.isDisabled = false
                  }
                  if (node && node.children && node.children.length) {
                    judgeDisabled(node.children)
                  }
                })
              }
              judgeDisabled(item.options)
              // 默认选择第一个
              if (!item.selectDimId) {
                item.selectDimId = (item.filterValues && item.filterValues[0]) || item.options[0].id
              }
            } else {
              await _this.getFilterOpts(item.id, _this.dynamicSearchArr)
            }
          }
        }
        await loopGetFilterOps()
        this.originDynamicSearchArr = cloneDeep(this.dynamicSearchArr)
        this.filterList = this.dynamicSearchArr.map(item => {
          const { id, selectDimId, dimName } = item
          return { columnCode: dimName === '指标' ? selectDimId : id, value: selectDimId, prefixVal: '', storageType: '', op: 'and' }
        })
        // this.load2dTable() // 重复请求，暂时屏蔽
      } else {
        this.filterList = []
      }
    },
    getAllIds(data) {
      const ids = []
      function traverse(nodes, isRootLevel = true) {
        nodes.forEach(node => {
          // 检查是否是根节点且dimName为"指标"
          const isIndicatorRoot = isRootLevel && node.dimName === '指标'

          if (!isIndicatorRoot) {
            // 收集当前节点id（除非是dimName为"指标"的根节点）
            ids.push(node.id)
          }

          // 递归处理子节点（子节点不再是根层级）
          if (node.children && Array.isArray(node.children)) {
            traverse(node.children, false)
          }
        })
      }
      traverse(data) // 从根数组开始遍历
      return ids
    },

    /**
     *
     * @param {*} item
     * @param {*} type
     * @param {*} show
     * 点击小眼睛显示隐藏，如果隐藏的是行维列维，则视图不变，传参变化，相当于把行维列维拖到筛选维。如果隐藏的是筛选维，则视图变化，传参不变
     */
    clickDimEye(item, type, show) {
      console.log(item, type, show)
      const { id } = item
      if (type === 'row') {
        // 点击行维小眼睛
        const arr = this.rowDimList.filter(row => {
          if (row.id === id || (row.dimName === '指标' && row.dimName === '指标')) {
            row.show = show
          }
          return row.show
        })
        let rowId = []
        arr && arr.length && arr.forEach(row => {
          if (row.dimName === '指标') {
            rowId = rowId.concat(row.children.map(child => child.id))
          } else {
            rowId.push(row.id)
          }
        })
        this.rowDimIds = rowId
        if (!show) {
          this.filterList.push({
            columnCode: id,
            value: this.allDimData[id][0].id,
            prefixVal: '',
            storageType: '',
            op: ''
          })
        } else {
          this.filterList = this.filterList.filter(obj => obj.columnCode !== id)
        }
      } else if (type === 'col') {
        // 点击列维的小眼睛
        const arr = this.colDimList.filter(col => {
          if (col.id === id || (col.dimName === '指标' && item.dimName === '指标')) {
            col.show = show
          }
          return col.show
        })
        let colId = []
        arr && arr.length && arr.forEach(col => {
          if (col.dimName === '指标') {
            colId = colId.concat(col.children.map(child => child.id))
          } else {
            colId.push(col.id)
          }
        })
        this.colDimIds = colId
        if (!show) {
          if (item.dimName === '指标') {
            const indId = item.children[0].id
            this.filterList.push({
              columnCode: indId,
              value: indId,
              prefixVal: '',
              storageType: '',
              op: ''
            })
          } else {
            this.filterList.push({
              columnCode: id,
              value: this.allDimData[id][0].id,
              prefixVal: '',
              storageType: '',
              op: ''
            })
          }
        } else {
          if (item.dimName === '指标') {
            this.filterList = this.filterList.filter(obj => obj.columnCode !== obj.value)
          } else {
            this.filterList = this.filterList.filter(obj => obj.columnCode !== id)
          }
        }
      } else {
        // 点击筛选维的小眼睛
        this.dynamicSearchArr = this.originDynamicSearchArr.filter(filter => {
          if (filter.id === id || (filter.dimName === '指标' && item.dimName === '指标')) {
            filter.show = show
          }
          return filter.show
        })
        this.filterDimList.forEach(filter => {
          if (filter.id === id || (filter.dimName === '指标' && item.dimName === '指标')) {
            filter.show = show
          }
        })
      }
      this.callBackFun()
    },
    onMoveDim(e, originalEvent) {
      const fromTarget = e.from.getAttribute('class')
      const toTarget = e.to.getAttribute('class')
      if (fromTarget === 'filter-dim') {
        if (e.draggedContext.element.id === this.conditionDim) {
          this.clearMessageTimer()
          this.messageTimer = setTimeout(() => {
            this.$message.error('条件维度只能作为筛选维')
          }, 500)
          return false
        }
      }
      if (toTarget === 'row-dim') {
        if (fromTarget !== 'row-dim' && this.rowDimList.length === 3) return false
        return true
      }
      if (toTarget === 'col-dim') {
        if (fromTarget !== 'col-dim' && this.colDimList.length === 2) return false
        return true
      }
      return true
    },
    getMemberByDimId() {
      this.allDimData = {}
      const arr = this.filterDimList.concat(this.rowDimList).concat(this.colDimList)
      this.loopAjax(arr)
    },
    async loopAjax(paramArr) {
      const _this = this
      for (let i = 0; i < paramArr.length; i++) {
        const { id, dimName } = paramArr[i]
        if (dimName !== '指标') {
          const params = {
            dimDirectoryId: id,
            dimName: ''
          }
          if (!_this.allDimData[id]) {
            await getMember(params).then(res => {
              _this.allDimData[id] = res.data
            })
          }
        }
      }
    },
    async getFilterOpts(id, arr) {
      const params = {
        dimDirectoryId: id,
        dimName: ''
      }
      await getMember(params).then(res => {
        arr.forEach(item => {
          if (item.id === id) {
            item.options = listToTree(cloneDeep(res.data))
            item.originOptions = cloneDeep(res.data)
            // 默认选择第一个
            if (!item.selectDimId) {
              item.selectDimId = (item.filterValues && item.filterValues[0]) || item.options[0].id
            }
          }
        })
      })
    },
    // vuetreeselect 格式化
    normalizer(node) {
      return {
        id: node.id,
        label: node.dimName,
        children: node.children
      }
    },
    selectFilterDim(val, instance) {
      this.clearSelectTimer()
      this.selectTimer = setTimeout(() => {
        this.filterList = this.dynamicSearchArr.map(item => {
          const { id, selectDimId, dimName } = item
          return { columnCode: dimName === '指标' ? selectDimId : id, value: selectDimId, prefixVal: '', storageType: '', op: 'and' }
        })
        this.originDynamicSearchArr = cloneDeep(this.dynamicSearchArr)
        this.load2dTable()
      }, 10)
      console.log('选择筛选维', val, instance, this.dynamicSearchArr)
    },
    clearSelectTimer() {
      if (this.selectTimer) {
        window.clearTimeout(this.selectTimer)
        this.selectTimer = null
      }
    },
    callBackFun() {
      this.clearLoadTimer()
      this.loadTimer = setTimeout(() => {
        this.load2dTable()
      }, 300)
    },
    clearLoadTimer() {
      if (this.loadTimer) {
        window.clearTimeout(this.loadTimer)
        this.loadTimer = null
      }
    },
    setFilterData(dataTransfer) {
      console.log('筛选维更新', dataTransfer)
      // to avoid Firefox bug
      // Detail see : https://github.com/RubaXa/Sortable/issues/1012
      dataTransfer.setData('Text', '')
    },
    setRowData(dataTransfer) {
      console.log('行维更新', dataTransfer)
      // to avoid Firefox bug
      // Detail see : https://github.com/RubaXa/Sortable/issues/1012
      dataTransfer.setData('Text', '')
    },
    setColData(dataTransfer) {
      console.log('列维更新', dataTransfer)
      // to avoid Firefox bug
      // Detail see : https://github.com/RubaXa/Sortable/issues/1012
      dataTransfer.setData('Text', '')
    },
    closeDimFilter() {
      this.dimFilterShow = false
    },
    showDimFilter() {
      this.dimFilterShow = true
    },
    formatSaveDim(arr, type) {
      const newArr = cloneDeep(arr)
      const resArr = []
      newArr.forEach(item => {
        const { id, dimType, selectDimId, children } = item || {}
        if (dimType && dimType === 'DIM') {
          if (type === 'filter') {
            resArr.push({
              id,
              dimType,
              filterValues: [String(selectDimId)]
            })
          } else {
            resArr.push({
              id,
              dimType
            })
          }
        } else {
          if (type === 'filter') {
            resArr.push({
              filterValues: [String(selectDimId)],
              dimType: 'IND'
            })
          } else {
            children.forEach(ind => {
              resArr.push({
                dimType: 'IND',
                id: ind.id
              })
            })
          }
        }
      })
      return resArr
    },
    saveStyle() {
      console.log('保存查看样式', this.filterDimList, this.rowDimList, this.colDimList)
      const params = {
        tableId: this.curTableId,
        filterDimList: this.formatSaveDim(this.filterDimList, 'filter'),
        rowDimList: this.formatSaveDim(this.rowDimList, 'row'),
        columnDimList: this.formatSaveDim(this.colDimList, 'col')
      }
      console.log('保存查看样式参数', params)
      saveLayout(params).then(res => {
        if (res.code === 200) {
          this.$message.success('样式保存成功')
          this.refreshTable()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 获取父表主键
    getParentTableKey() {
      getTableKey({ id: this.curTableId }).then(res => {
        if (res.code === 200) {
          this.parentTableKeyCode = res.data?.code || ''
        }
      })
    },
    closeSubTable() {
      this.subTableShow = false
    },
    // 单元格样式
    cellClassName({ row, column }) {
      const { params, field } = column
      const { parentTableEntry } = params
      if (parentTableEntry && row[field]) return 'subtable-cell'
      return null
    },
    // 点击单元格,如果是入口列则打开子表
    cellClickEvent({ row, column }) {
      console.log('点击单元格', row, column)
      const { params } = column
      const { parentTableEntry, relSubTableId } = params
      if (parentTableEntry && row[column.field] !== '') {
        this.subTableShow = true
        this.relSubTableId = relSubTableId
        this.parentTableKeyVal = row[this.parentTableKeyCode]
      }
    },
    commonDownLoad() {
      const filePath = this.exportInfo.extra
      var _this = this
      // 下载地址
      const chunkSize = 1024 * 1024 * 2 // 单个分段大小，这里测试用10M
      let filesTotalSize = chunkSize // 安装包总大小，默认10M
      let filesPages = 1 // 总共分几段下载
      // 计算百分比之前先清空上次的
      if (_this.downloadPercentage === 100) {
        _this.downloadPercentage = 0
        _this.filesCurrentPage = 0
        _this.contentList = []
      }
      const sentAxios = (num) => {
        let rande = chunkSize

        if (num) {
          rande = `${(num - 1) * chunkSize + 2}-${num * chunkSize + 1}`
        } else {
          // 第一次0-1方便获取总数，计算下载进度，每段下载字节范围区间
          rande = '0-1'
        }

        const headers = {
          range: rande
        }
        // 测试用,上线根据项目实际修改
        const params = {
          filePath
        }
        this.showDownloadProcess = true
        spiltDownload(params, headers).then((response) => {
          // 检查了下才发现，后端对文件流做了一层封装，所以将content指向response.data即可
          const content = response.data
          // 截取文件总长度和最后偏移量
          const result = response.headers['content-range'].split('/')
          // 获取文件总大小，方便计算下载百分比 减去第一次获取总数
          filesTotalSize = result[1] - 2
          // 获取最后一片文件位置，用于断点续传
          _this.fileFinalOffset = result[0].split('-')[1]
          // 计算总共页数，向上取整
          filesPages = Math.ceil(filesTotalSize / chunkSize)
          // 文件流数组
          // _this.contentList.push(content);
          _this.contentList[num] = content
          // 计算下载百分比  当前下载的片数/总片数
          if (_this.stopRecursiveTags) {
            _this.downloadPercentage = Number((((_this.contentList.length - 1) / filesPages) * 100).toFixed(2))
          }
          // 递归获取文件数据(判断是否要继续递归)
          if (_this.filesCurrentPage < filesPages && _this.stopRecursiveTags) {
            _this.filesCurrentPage++
            sentAxios(_this.filesCurrentPage)
            // 结束递归
            return
          }
          // 递归标签为true 才进行下载
          if (_this.stopRecursiveTags) {
            // 文件名称
            _this.fileName = decodeURIComponent(response.headers['fname'])

            // 构造一个blob对象来处理数据
            const blob = new Blob(_this.contentList)
            // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
            // IE10以上支持blob但是依然不支持download
            if ('download' in document.createElement('a')) {
              // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = _this.fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else {
              // 其他浏览器
              navigator.msSaveBlob(blob, _this.fileName)
            }
          }
        })
          .catch(function(error) {
            console.log(error)
          })
      }
      // 第一次获取数据方便获取总数
      sentAxios(_this.filesCurrentPage)
    },
    initWs() {
      if (typeof (WebSocket) === 'undefined') {
        alert('您的浏览器不支持socket')
      } else {
        // 实例化socket 111是固定的用户id,正式环境直接获取当前登录用户id
        this.socket = new WebSocket(`${this.global.wsUrl}`)
        this.global.setWs(this.socket)
        // 监听socket连接
        this.socket.onopen = () => {
          this.sendMsg()
        }

        // 监听socket错误信息
        this.socket.onerror = () => {
          console.error('连接错误')
        }
        // 监听socket消息
        this.socket.onmessage = (msg) => {
          this.getWsMsg(msg)
        }
        // 监听socket关闭信息
        this.socket.onclose = (e) => {
          console.error('socket已经关闭')
          console.error(e)
        }
      }
    },
    sendMsg() {
      this.socket.send(JSON.stringify({ userId: this.user.userId }))
    },
    getWsMsg(res) {
      const data = res && res.data
      if (data && !data.includes('服务器')) {
        const progressInfo = JSON.parse(res.data)
        console.log('websocket消息', progressInfo)
        const { process, timestamp } = progressInfo || {}
        if (String(this.operateTime) === timestamp) {
          this.exportInfo = progressInfo
          console.log(111, this.exportInfo)
          this.exportInfo.process = Number((100 * (process)).toFixed(0))
        }
      }
    },
    // 下载导入模版
    downloadTemplate() {
      const params = {
        tableId: this.curTableId
      }
      downloadTemplate(params).then(res => {
        createExcel(res, this.curTableDetail.tableName + '.xlsx')
      })
    },
    // 导入
    async impData() {
      try {
        const { file } = await VXETable.readFile({
          types: ['xlsx', 'xls']
        })
        console.log('上传文件', file)
        const params = new FormData()
        params.append('file', file)
        params.append('tableId', this.curTableId)
        impData(params).then(res => {
          if (res.code === 200) {
            this.$message.success('上传成功')
          } else {
            this.$message.error(res.msg)
          }
        }).finally(() => {
          this.refreshTable()
        })
      } catch (e) {
        console.log(e)
      }
    },
    // 导出
    exportData() {
      this.initWs()
      if (this.viewTable.length === 0) {
        this.$message.error('表格无数据，不能导出')
        return
      }
      this.operateTime = new Date().getTime()
      const params = {
        ...this.pageInfo,
        tableId: this.curTableId,
        sortList: this.sortList,
        filterList: this.filterList,
        userId: this.userId,
        timestamp: this.operateTime
      }
      exportTable(params).then(res => {
        console.log(res, 222)
        if (res.code === 200) {
          this.$message.success('正在导出,请稍候')
        }
      })
    },
    initTableParams() {
      this.sortList = []
      this.pageInfo = {
        currentPage: 1,
        pageSize: 20
      }
    },
    openOriginData() {
      if (this.disableEditRow) return
      getViewList({ pageNum: 1, pageSize: 9999 }).then(res => {
        if (res.code === 200) {
          this.originList = res.rows
          this.showOrigin = true
          this.originForm.name = this.curViewId
          // this.initOriginForm()
        }
      })
    },
    initOriginForm() {
      this.originForm = { name: '' }
      if (this.$refs.originForm) {
        this.$refs.originForm.resetFields()
      }
    },
    closeOrigin() {
      this.showOrigin = false
    },
    // 载入源数据
    handleOrigin(flag) {
      // const params = { id: this.curTableId }
      loadOriginData(this.curTableId).then(res => {
        if (res.code === 200) {
          if (flag) {
            this.$message.success(res.msg)
            this.callBackFun()
          }
        }
      })
    },
    // 单元格激活时触发
    editActived({ row, column }) {
      const { field } = column
      const { _X_ROW_KEY } = row
      this.editRowColMap[_X_ROW_KEY] = this.editRowColMap[_X_ROW_KEY] ? this.editRowColMap[_X_ROW_KEY] : []
      if (!this.editRowColMap[_X_ROW_KEY].includes(field)) {
        this.editRowColMap[_X_ROW_KEY].push(field)
      }
      const targetRow = this.originTable.find(item => item._X_ROW_KEY === _X_ROW_KEY)
      this.viewTable.forEach((item) => {
        if (item._X_ROW_KEY === _X_ROW_KEY) {
          item[field] = targetRow[field]
        }
      })
    },
    // 给原始数据加上X_ROW_KEY
    addKey() {
      if (this.viewTable.length && this.originTable.length) {
        this.viewTable.forEach((item, index) => {
          this.originTable[index]._X_ROW_KEY = item._X_ROW_KEY
        })
      }
    },
    // 获取插入行
    getInsertEvent() {
      const $table = this.$refs.xTable
      const insertRecords = $table.getInsertRecords()
      return insertRecords
    },
    // 根据格式id获取类型
    getTypeById(id) {
      const target = this.formatList.filter(item => item.id === id)[0]
      return target ? target.storageType : 'VARCHAR'
    },
    // 获取各列宽度
    getAllColW() {
      const allCol = this.$refs.xTable.getTableColumn().tableColumn
      this.allColWArr = allCol.map(col => col.renderWidth)
    },
    // 处理维度数据
    formatDim(list, dimList, indTree, type) {
      const cloneList = cloneDeep(list)
      console.log('indTree', indTree)
      let selectIndId = ''
      let indLastArr = []
      cloneList.forEach(dim => {
        dim.show = true
        const { id, dimType, filterValues } = dim
        if (dimType === 'DIM') {
          const dimTarget = dimList.find(item => item.id === id)
          dim.dimName = dimTarget?.dimDirectoryName
        }
        if (dimType === 'IND') {
          indLastArr = indTree.map(item => {
            const { indName, id, parentId, children, indType } = item
            return {
              dimType: 'IND',
              indType,
              dimName: indName,
              id,
              parentId,
              children
            }
          })
          if (type === 'filter') {
            selectIndId = filterValues[0]
          }
        }
        if (filterValues && filterValues.length) {
          dim.selectDimId = filterValues[0]
        }
      })
      if (indLastArr && indLastArr.length) {
        const indObj = { id: Math.round(Math.random() * 900 + 100), dimName: '指标', children: indLastArr, selectDimId: selectIndId, show: true }
        cloneList.push(indObj)
      }
      return cloneList.filter(item => item.dimType !== 'IND')
    },
    // 表格源数据
    async fetchData(id) {
      // this.loading = true
      await this.handleOrigin(false)
      getDim(id).then(async(res) => {
        if (res.code === 200) {
          const { dimList, indList, defaultDimId, defaultColumnDimId, layout } = res.data
          const { columnDimList, filterDimList, rowDimList } = layout
          this.getDimTypes(dimList, indList)
          this.defaultDimId = defaultDimId
          this.defaultColumnDimId = defaultColumnDimId
          this.originIndList = cloneDeep(indList)
          const validIndList = cloneDeep(indList).map(item => {
            return { ...item, dimName: item.indName }
          })
          const indTree = listToTree(validIndList)
          this.rowDimList = this.formatDim(rowDimList, dimList, indTree, 'row')
          this.colDimList = this.formatDim(columnDimList, dimList, indTree, 'col')
          this.filterDimList = this.formatDim(filterDimList, dimList, indTree, 'filter')
          await this.load2dTable()
          this.getMemberByDimId()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    getDimTypes(dimList, indList) {
      const obj = {}
      dimList.forEach(item => {
        obj[item.id] = 'DIM'
      })
      indList.forEach(item => {
        obj[item.id] = 'IND'
      })
      this.dimTypeIdObj = obj
    },
    async  load2dTable() {
      if (!this.colDimIds.length && !this.filterList.length && !this.rowDimIds.length) return
      this.filterList.forEach(item => {
        if (item.columnCode) {
          item.dimType = this.dimTypeIdObj[item.columnCode]
        } else {
          item.dimType = 'IND'
        }
      })
      const params = {
        'columnDimIds': this.colDimIds,
        'filterList': this.filterList,
        'rowDimIds': this.rowDimIds,
        'tableId': this.curTableId,
        'showDimList': this.showDimList
      }
      const columnDimTypes = []
      const rowDimTypes = []
      this.colDimIds.forEach(item => {
        columnDimTypes.push(this.dimTypeIdObj[item])
      })
      this.rowDimIds.forEach(item => {
        rowDimTypes.push(this.dimTypeIdObj[item])
      })
      const validParams = { ...params, columnDimTypes, rowDimTypes }
      this.loading = true
      await getTableDetail(validParams).then(res => {
        if (res.code === 200) {
          this.configData = res.data
        }
      }).finally(() => {
        // this.getAllColW()
        this.loading = false
      })
    },
    // 计算表格各个宽度
    getTableW() {
      const $table = this.$refs.xTable
      const $el = $table.$el.querySelector('.body--wrapper .vxe-table--body')
      this.tableOffsetW = $el.offsetWidth
      this.tableClientW = $table.$el.clientWidth
    },
    // 横向滚动条滚动到最右边并高亮最后一列
    scrollToCurCol(idx) {
      this.$nextTick(() => {
        const xTable = this.$refs.xTable
        // 由于固定列的动态切换是无状态的，所以需要手动刷新滚动位置
        xTable.refreshColumn().then(() => {
          const allCols = xTable.getTableColumn().tableColumn
          const lastCol = idx ? allCols[idx] : allCols[allCols.length - 1]
          // 高亮最后一列
          xTable.setCurrentColumn(lastCol)
          // 滚动到最后一列
          // xTable.scrollToColumn(lastCol)
          const allW = this.allColWArr.reduce((prev, cur) => {
            return prev + cur
          }, 0)
          console.log('所有列总宽度', allW)
          xTable.scrollTo(allW)
        })
      })
    },
    currentChangeEvent({ row }) {
      this.curRow = row
    },
    // 将行数据处理成后台需要的
    formatRow(originArr) {
      const arr = cloneDeep(originArr)
      const rows = []
      arr.forEach(row => {
        const cells = []
        for (const code in row) {
          const colData = this.tableMetaJson.find(item => item.code === code)
          // 过滤xTable自带的key
          if (code !== '_X_ROW_KEY' && colData) {
            cells.push({
              code,
              cellValue: row[code],
              type: '',
              no: colData.no
            })
          }
        }
        // cells根据no大小排序
        const sortCells = this.objArrSort(cells, 'no')
        sortCells.forEach(cell => {
          delete cell.no
        })
        // 给cells里的值按no大小排序
        rows.push({ cells: sortCells })
      })
      return rows
    },
    // 根据key给对象数组排序
    objArrSort(arr, key) {
      const newArr = cloneDeep(arr)
      return newArr.sort((a, b) => a[key] - b[key])
    },
    /**
     * 保存数据,分为新增和修改,分别调接口
     */
    saveTableData() {
      const configTableRef = this.$refs.configTable
      if (this.disableEditRow) return
      const dimTableDataCellList = []
      const tableData = cloneDeep(configTableRef.tableData)
      tableData.forEach(row => {
        Object.keys(row).forEach(key => {
          if (key.includes('col') && !key.includes('align') && !key.includes('isEdit') && !key.includes('originValue') && !key.includes('cellValue')) {
            dimTableDataCellList.push(this.formatCellData(row, key, row[`${key}-originValue`], row[`${key}-cellValue`]))
          }
        })
      })
      const params = {
        dimTableDataCellList,
        tableId: this.curTableId
      }
      saveTable(params).then(res => {
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.load2dTable()
        }
      })
    },
    // 处理单元格数据
    formatCellData(row, field, val, cellVal) {
      const originIndNameArr = this.originIndList.map(item => item.indName)
      const emptyIdArr = ['empty0-id', 'empty1-id', 'empty2-id', 'empty3-id', 'empty0-relId', 'empty1-relId', 'empty2-relId', 'empty3-relId', 'empty0-parentId', 'empty1-parentId', 'empty2-parentId', 'empty3-parentId', 'empty0-isExpand', 'empty1-isExpand', 'empty2-isExpand', 'empty3-isExpand']
      const target = {
        'dimInstanceIdList': [],
        'indId': '',
        'opType': (!val && cellVal) ? 'delete' : 'add',
        'originalValue': val || cellVal,
        'relDimIdList': [],
        'field': field,
        'rowKey': row['_X_ROW_KEY']
      }
      const dimInstanceIdList = []
      // 先找出筛选维实例id
      this.dynamicSearchArr.forEach(item => {
        if (item.dimName !== '指标') {
          dimInstanceIdList.push(item.selectDimId)
        } else {
          target.indId = item.selectDimId
        }
      })
      // 再找行维id，判断行维是否有指标
      Object.keys(row).forEach(m => {
        if (!emptyIdArr.includes(m)) {
          if (m.includes('empty')) {
            if (originIndNameArr.includes(row[m])) {
              target.indId = this.originIndList.find(item => item.indName === row[m]).id
            } else {
              const emptyIdx = m.split('empty')[1]
              const arr = this.allDimData[this.rowDimIds[emptyIdx]]
              arr.forEach(dim => {
                if (dim.dimName === row[m]) {
                  dimInstanceIdList.push(dim.id)
                }
              })
            }
          }
        }
      })
      // 最后找列维
      const colArr = cloneDeep(this.$refs.configTable.tableMetaJson)
      function findFieldData(field) {
        function traverse(nodes, currentPath = []) {
          for (const node of nodes) {
            // 创建当前节点的新路径副本
            const newPath = [...currentPath]

            // 如果当前节点是dim或ind-group，添加到路径中
            if (node.params &&
                (node.params.type === 'dim' || node.params.type === 'ind-group')) {
              newPath.push(node.params.id)
            }

            // 检查是否是目标节点
            if (node.field === field) {
              // 处理目标节点
              if (node.params) {
                if (node.params.type === 'ind') {
                  target.indId = node.params.id
                } else if (node.params.type === 'dim' || node.params.type === 'ind-group') {
                  // 确保目标节点本身的id也被加入（如果是dim/ind-group类型）
                  if (!newPath.includes(node.params.id)) {
                    newPath.push(node.params.id)
                  }
                }
              }
              // 将路径添加到结果
              dimInstanceIdList.push(...newPath)
              return true
            }

            // 递归遍历子节点
            if (node.children && node.children.length > 0) {
              const found = traverse(node.children, newPath)
              if (found) return true
            }
          }
          return false
        }

        traverse(colArr)
      }
      findFieldData(field)
      target.dimInstanceIdList = dimInstanceIdList
      const relDimIdList = []
      this.filterDimList.forEach(item => {
        if (item.dimName !== '指标') {
          relDimIdList.push(item.id)
        }
      })
      this.rowDimList.forEach(item => {
        if (item.dimName !== '指标') {
          relDimIdList.push(item.id)
        }
      })
      this.colDimList.forEach(item => {
        if (item.dimName !== '指标') {
          relDimIdList.push(item.id)
        }
      })
      target.relDimIdList = relDimIdList
      return target
    },

    // 选择人员
    selectOrg(node, instanceId) {
      const { id, label } = node
      this.orgMap[id] = label
    },
    // 格式化单元格
    formatCell({ cellValue, row, column }, config) {
      if (config.systemKey === 'USER') {
        return this.userMap[cellValue]
      }
      if (config.systemKey === 'ORG') {
        return this.orgMap[cellValue]
      }
      return cellValue
    },
    // 打开人员弹框
    openUser(row, config) {
      this.curEditRow = row
      this.curEditCol = config
      this.$refs.selectUser.show()
    },
    // 选中人员
    handleSelectUser(user) {
      const { userId, nickName } = user
      this.curEditRow[this.curEditCol.code] = userId
      this.userMap[userId] = nickName
    },
    changeSystemKey(config) {
      config.systemValue = null
    },
    cellClick(row) {
      console.log(row)
    },
    // 刷新表格
    refreshTable(updateSearch = true) {
      this.load2dTable()
    },
    // 审批提交
    openFlowModal(type) {
      this.flowModalType = type
      this.commitContent = ''
      this.showFlowModal = true
    },
    saveFlowHandle() {
      if (!this.commitContent.length) return this.$message.warning('请输入审批意见！')
      const { id, tableId, flowType, instanceId } = this.flowModalType === 'SUBMIT' ? this.cubeFlowRow : this.handleNodes.find(item => item.id === this.handleFlowNode) || {}
      const { centralizeDimDefineId } = this.cubeFlowRow
      const params = {
        id,
        instanceId,
        flowType,
        executeType: this.flowModalType,
        tableId,
        commentContent: this.commitContent,
        centralizeDimDefineId
      }
      getRecordAgree(params).then(res => {
        this.$message({
          type: res.code === 200 ? 'success' : 'error',
          message: res.msg
        })
        if (this.flowModalType !== 'SUBMIT') {
          this.getFlowHandleNodes(this.cubeFlowRow.id)
          this.handleFlowNode = ''
        }
        this.showFlowModal = false
      })
    },
    // 返回流程 待办列表
    backFlowList() {
      this.$router.push({ name: 'Dashboard', params: { name: 'FlowList' }})
    },
    getFlowHandleNodes(id) {
      getFlowCubeChild(id).then(res => {
        this.handleNodes = res.data || []
      })
    },
    handleAgree() {
      if (!this.handleFlowNode || !this.handleFlowNode.length) {
        return this.$message.warning('请选择待处理节点！')
      }
      this.openFlowModal('AGREE')
    },
    handleReject() {
      if (!this.handleFlowNode || !this.handleFlowNode.length) {
        return this.$message.warning('请选择待处理节点！')
      }
      this.openFlowModal('RETURN')
    },
    // 查看归口审批信息
    lookCentralizedInfo() {
      if (!this.handleFlowNode || !this.handleFlowNode.length) {
        return this.$message.warning('请选择待处理节点！')
      }
      // 通过选中节点，查归口信息
      const flag = this.handleNodes.find(item => item.id === this.handleFlowNode)
      this.centralizedModalTitle = flag && flag.nodeName
      viewNodeState(this.cubeFlowRow.id, flag.nodeId).then(res => {
        this.centralizedStatusList = res.data[0].centralizeList
        this.showCenralizedModal = true
      })
    },
    lookApproveRecord(row) {
      const instanceId = this.cubeFlowRow.instanceId
      const nodeId = row.nodeId
      const centralizeDimDefineId = row.id ? row.id : ''
      getRecordMsgList(instanceId, nodeId, 'desc', centralizeDimDefineId).then(res => {
        if (res.code === 200) {
          this.approveList = res.data
          this.showApproveModal = true
        }
      })
    },
    lookCentralizedNode(row) {
      // 通过选中节点，查归口信息
      this.centralizedModalTitle = row.nodeName
      viewNodeState(this.cubeFlowRow.id, row.nodeId).then(res => {
        this.centralizedStatusList = res.data[0].centralizeList
        this.showCenralizedModal = true
      })
    },
    sortData(sortType) {
      if (sortType === 'asc') {
        this.approveList.sort((a, b) => moment(a.createTime).valueOf() - moment(b.createTime).valueOf())
      } else {
        this.approveList.sort((a, b) => moment(b.createTime).valueOf() - moment(a.createTime).valueOf())
      }
    },
    getDeptApproveList() {
      viewNodeState(this.cubeFlowRow.id)
        .then((res) => {
          if (res.code === 200) {
            console.log('维度树列表', res)
            this.deptApproveTree = res.data
            this.nodeStatusModal = true
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.search-container {
  margin-top: 10px;
  min-height: 40px;
}
.search-form {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  ::v-deep .el-form-item {
  margin: 0px 20px 10px 0px;
  display: flex;
  align-items: center;

  .el-form-item__label {
    font-size: 12px;
  }
  .el-form-item__content {
      flex: 1;
    }
  }
  ::v-deep .vue-treeselect {
      font-size: 12px !important;
      width: 170px !important;
  }
}
.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .sum-avg {
    font-size: 12px;
    color:#606266;
    margin-right: 20px;
    .label {
      color: $formLabel;
    }
  }
  .right {
    display: flex;
  }
}

.table-box {
    display: flex;
    height: var(--height);
    .add-col {
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-left: 10px;
    }
    &.no-search {
      height: calc(100vh - 200px);
    }
  }
.dynamic-table {
  width: 100%;
    ::v-deep.vxe-table--header-wrapper .vxe-icon-edit {
      display: none;
    }
  ::v-deep .vxe-header--row .vxe-header--column {
    height: 30px;
  }
  ::v-deep .subtable-cell {
    cursor: pointer;
    color: $primary;
    font-weight: bold;
    .vxe-cell--label {
      text-decoration: underline;
    }
  }
}

.custom-col {
  justify-content: space-between;
  i {
    cursor: pointer;
    color: $formLabel;
  }
  .icon-appjisuanqi {
    margin:0 3px;
  }
}
.popver-box {
  text-align: left;
  .popver-item {
    display: flex;
    align-items: center;
    margin: 8px 0;
    .title {
      margin: 0 10px;
      &.active {
        color: $primary;
        cursor: pointer;
      }
    }

  }
}
  .row-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 44px;
    padding: 5px;
    .el-checkbox {
      margin-right: 10px;
    }
  }

  .col-left {
    span {
      margin-left: 2px;
    }
    i {
      color: $formLabel;
    }
  }
  .setcol-btn {
    height: 24px;
    .el-button {
      font-size: 13px;
    }
    .del-btn {
      color: $primary
    }
  }
  .vxe-table {
    ::v-deep .vxe-cell {
      padding-right: 3px;
      .vxe-select {
        z-index: 2;
      }
      .vxe-cell--title {
        width: 100%;
      }
    }
    // ::v-deep .vxe-body--column.col--dirty {
    //   background: #fdf6ec !important;
    //   &::before {
    //     display: none !important;
    //   }
    // }
  }
.open-user {
  cursor: pointer;
}
.col-sort {
  display: flex;
  flex-direction: column;
  margin-right: 3px;
  i {
    width: 10px;
    height: 10px;
    color: #c0c4cc;
    &.active {
      color:$primary;
    }
  }
}
.btn-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  .left-btns {
    display: flex;
  }
  .btn-box {
    padding: 0 10px;
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #606266;
    cursor: pointer;
    &:hover {
      background: #f6f7f8;
      color: $primary;
    }
    .btn-text {
      margin-top: 8px;
      font-weight: bold;
    }
    .btn-icon {
      font-size: 18px;
    }
    &.disabled {
      color: #909399;
    }
  }
  .gap-line {
    width: 1px;
    height: 60px;
    background: #ebeef5
  }
}
.process-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 12px;
  color:#606266;
  ::v-deep .el-progress {
    width: 100px;
    .el-progress__text {
      font-size: 12px !important;
    }
  }
}
.custom-control {
  display: flex;
  align-items: center;
  .vxe-select {
    width: 80px;
    border-right: none;
  }
}
.quick-input {
  display: flex;
  margin-bottom: 15px;
  .control-box {
    min-width: 110px;
    position: relative;
    margin-right: 25px;
    cursor: pointer;
    &:hover {
      .control-btn {
        background: #e8eaec;
      }
    }
    .control-btn {
      position: absolute;
      z-index: 10;
      top: 0;
      left: 0;
      width: 110px;
      height: 30px;
      border-radius: 4px;
      pointer-events:none;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 13px;
      background: #fff;
      border: 1px solid #e8eaec;
      &.if {
        pointer-events:auto;
      }
    }
  }
  .el-select,.el-cascader {
    width: 100px;
  }
}
.del-select {
  width: 100%;
}
.delversion-option {
  padding-right: 0 !important;
  background: #fff;
  .del-icon {
    float: right;
    color: #8492a6;
    font-size: 13px;
    cursor: pointer;
    height: 34px !important;
    width: 34px !important;
    text-align: center;
    line-height: 34px;
  }
}
.download-link {
  color: $primary;
}
.main-area {
  display: flex;
  .table-list {
    width: calc(100vw - 320px);
  }
  .dim-switch {
    width: 300px;
    margin-left: 20px;
    .top-btn {
      width: 100%;
      text-align: right;
      margin-bottom: 20px;
    }
    .dim-area {
      .dim-title {
        position: relative;
        height: 32px;
        line-height: 32px;
        text-align: center;
        background: $primary;
        color: #fff;
        font-size: 14px;
        .el-icon-close {
          position: absolute;
          right: 8px;
          top: 8px;
          color:#fff;
          font-size: 18px;
          cursor: pointer;
        }
      }
    }
  }
}
.filter-dim,.row-dim,.col-dim {
  width: 100%;
  padding: 5px;
  border:1px solid #DCDFE6;
  min-height: 110px;
  .drag-item {
    margin: 4px;
    cursor: move;
    height: 24px;
    display: inline-block;
    .el-tag {
      user-select: none !important;
    }
    .vxe-icon-chart-radar {
      margin-left: 2px;
    }
  }
  .vxe-icon-eye-fill,.vxe-icon-eye-fill-close {
    cursor: pointer;
    width: 18px;
    height: 18px;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    font-size: 18px;
    margin: 0 3px;
  }
}
.top-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f6f7f8;
  padding: 5px 10px;
  margin-bottom: 15px;
  ::v-deep .el-checkbox__label {
    font-size: 12px;
    padding-left: 3px;
    padding-right: 15px;
  }
}
.dim-tree {
  ::v-deep .el-tree {
    height: 350px;
    overflow-y: auto;
  }
}
.cube-flow-btns{
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #4472c4;
    margin-bottom: 8px;
    padding-bottom: 8px;
}
.current-node-bar{
  line-height: 32px;
  padding-left: 8px;
  background-color: #ecf8ff;
  border-radius: 2px;
  border-left: 5px solid #50bfff;
  color: #303133;
  margin-bottom: 8px;
}
.todo-node-select{
  margin-right: 10px;
}

.flow-approve-steps {
  ::v-deep .el-step__description {
    padding: 0;
  }
}
.dim-icon {
  cursor: pointer;
  color: $primary;
  margin-top: 35px;
  font-size: 14px;
  font-weight: 500;
}
.save-style {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #e8eaec;
  border-top: none;
  .el-button {
    width: 250px;
    height: 40px;
  }
}
</style>
