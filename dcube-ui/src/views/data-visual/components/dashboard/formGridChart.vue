<!-- 二维表单组件 -->
<template>
  <div ref="formGrid" :class="`form-grid-wrapper ${echartsTheme} ${reportType}`">
    <h4 v-if="titlePosition === 'top'" style="min-height:40px;">{{ title }}</h4>
    <vxe-table
      border
      show-overflow
      :max-height="tableMaxHeight"
      :row-config="{isHover: true}"
      :data="defaultData"
      :scroll-y="{enabled: true,gt: 0}"
    >
      <vxe-column v-for="(item,index) in legendDimColumns" :key="item+index" :field="item" :title="item" :align="handleGetColumnAlign(index)" />
      <!-- <vxe-column v-for="(item,index) in legendDimColumns" :key="item+index" :width="index === 0 ? 140 : ''" :field="item" :title="item+'kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk'" :align="handleGetColumnAlign(index)">
        <template v-if="index === 0" v-slot:header="{ column }">
          <div class="out">
            <span class="row vxe-cell--label">
              {{ item.split("\\")[0] }}888888888888888899999
            </span>
            <span class="line" />
            <span class="col vxe-cell--label">{{ item.split("\\")[1] }}9999999999999999999999999444</span>
          </div>
        </template>
      </vxe-column> -->
    </vxe-table>
    <h4 v-if="titlePosition === 'bottom'">{{ title }}</h4>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import dealData from '../mixins/dealData'
import resize from '../mixins/resize'
import switchTheme from '../mixins/switchTheme'
export default {
  mixins: [dealData, resize, switchTheme],
  props: {
    isDownloading: { // 当前是否在下载打印页面
      type: Boolean,
      default: false
    },
    chartData: {
      type: Array,
      default() {
        return []
      }
    },
    dataColumns: {
      type: Array,
      default() {
        return []
      }
    },
    originLegendDimColumns: {
      type: Array,
      default() {
        return []
      }
    },
    title: {
      type: String,
      default: ''
    },
    titlePosition: {
      type: String,
      default: 'bottom'
    },
    xDim: {
      type: String,
      default: '横轴'
    },
    yDim: {
      type: String,
      default: '列维'
    },
    tableMaxHeightFull: {
      type: Number,
      default: 0
    },
    isMax: { // 是否是全屏展示所有数据模式
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // test: [],
      chartType: 'formGrid',
      tableMaxHeight: 400,
      defaultData: [], // 默认展示的数据
      dimColumns: [], // 行维结果
      legendDimColumns: [], // 列维结果
      dataSource: [], // 数据
      defaultLegendDimColumns: ['横轴\\列维', '列维1', '列维2', '列维3', '列维4', '列维5'] // 列维结果
      // dimColumns: ['横轴1', '横轴2', '横轴3', '横轴4', '横轴5'], // 行维结果
      // legendDimColumns: ['横轴\\列维', '列维1', '列维2', '列维3', '列维4', '列维5'], // 列维结果
      // dataSource: [[1, 2, 3, 4, 5], [11, 22, 33, 44, 55],
      //   [111, 222, 333, 444, 555]]// 数据
    }
  },
  watch: {
    // dataColumns(newVal) {
    //   console.log('999999999999999999999')
    //   if (this.titlePosition === 'top') {
    //     this.initData()
    //   }
    // },
    dataColumns: {
      deep: true,
      handler(val) {
        if (this.titlePosition === 'top') {
          this.initData()
        }
      }
    },
    chartData(newVal) {
      this.initData()
    },
    isDownloading(newVal) {
      // if (this.test && this.test.length > 16 && newVal) {
      if (this.dimColumns && this.dimColumns.length > 16 && newVal) {
        // 打印的时候展示出所有的table数据，最大只展示到1000条
        // this.tableMaxHeight = 410 + (this.test.slice(16, 100).length) * 26
        this.tableMaxHeight = 410 + (this.dimColumns.slice(16, 100).length) * 26
      } else {
        this.tableMaxHeight = 400
      }
    }
  },
  created() {
    if (this.tableMaxHeightFull > 480) {
      this.tableMaxHeight = this.tableMaxHeightFull - 80
    }
    this.initData('init')
  },
  methods: {
    /**
     * 根据系统设置的格式展示列的对齐方式
     * @param index
     */
    handleGetColumnAlign(index) {
      if (this.dataColumns) {
        if (this.dataColumns.length > 1) {
          // 指标多选的情况根据配置的对齐格式来 {"contentAlign":"left","decimalPlaces":0,"operationalRule":"value/10000"}
          const format = this.dataColumns[index - 1]?.format ? JSON.parse(this.dataColumns[index - 1].format) : {}
          return format.contentAlign ? (format.contentAlign === 'middle' ? 'center' : format.contentAlign) : 'center'
        } else {
          const format = this.dataColumns[0]?.format ? JSON.parse(this.dataColumns[0].format) : {}
          return format.contentAlign ? (format.contentAlign === 'middle' ? 'center' : format.contentAlign) : 'center'
        }
      }
      return 'center'
    },
    initData(type) {
      if (type === 'init' && !this.chartData.length) {
        this.dimColumns = ['横轴1', '横轴2', '横轴3'] // 行维结果
        this.legendDimColumns = ['横轴\\列维', '列维1'] // 列维结果
        this.dataSource = [[1], [11], [111]]
      } else {
        this.handleChartData(this.chartData)
      }
      const result = []
      this.dimColumns.forEach((item, ind) => {
        const data = { }
        data[`${this.xDim || '横轴'}\\${(this.dataColumns && this.dataColumns.length > 1 ? '指标' : '') || this.yDim || '列维'}`] = item
        this.dataSource[ind] && this.dataSource[ind].forEach((d, i) => {
          data[this.legendDimColumns[i + 1]] = d
        })
        result.push(data)
      })
      this.defaultData = result
      // console.log(this.dimColumns, this.legendDimColumns, this.dataSource, result, 'lccccc')
    },
    handleChartData(newVal) {
      const isMultipleDataColumns = (!this.originLegendDimColumns || !this.originLegendDimColumns.length) && this.dataColumns && this.dataColumns.length >= 1
      if (!newVal || !newVal.length || isMultipleDataColumns ? newVal[0].length < 2 : newVal[0].length < 3) {
        this.legendDimColumns = this.defaultLegendDimColumns
        this.dataSource = []
        return
      }

      // 模拟如果一次请求10000数据，界面滚动是否会有卡顿效果
      // newVal = deepClone(newVal)
      // for (let i = 0; i < 10000; i++) {
      //   newVal.push([newVal[0][0] + i].concat([newVal[0][1], newVal[0][2]]))
      // }
      // this.test = newVal

      let xData = new Set()
      let legendData = new Set()
      const dataSource = []
      // const isMultipleDataColumns = false
      if (isMultipleDataColumns) {
        // 多指标情况，列维是指标名
        legendData = this.dataColumns.map(item => {
          return item.desc
        })
      }
      for (const item of newVal) {
        // item 是一个数组有三个值 [横轴值，图例值，指标值]
        if (item[0]) {
          xData.add(item[0])
        }
        if (item[1] && !isMultipleDataColumns) {
          // 这里要区分如果是指标多选的情况，列维的名称需要是选择的指标维度名称，而不是动态的列维成员了
          legendData.add(item[1])
        }
      }// 横坐标
      xData = [...xData]
      if (!xData.length) {
        this.legendDimColumns = this.defaultLegendDimColumns
        this.dataSource = []
        return
      }
      legendData = [...legendData]
      legendData.forEach((item, index) => {
        // 找到对应图例的横坐标对应的指标的值
        const data = {}
        const result = []
        newVal.forEach(item2 => { // [[横轴，纵轴，数据]]
          if (isMultipleDataColumns) {
            data[`${item2[0]}`] = item2[index + 1]
          } else {
            if (item2[1] === item) { // 找到图例相同的
              data[`${item2[0]}`] = item2[2]
            }
          }
        })
        let format = {}
        if (this.dataColumns) {
          if (this.dataColumns.length > 1) {
            format = this.dataColumns[index]?.format ? JSON.parse(this.dataColumns[index].format) : {}
          } else {
            format = this.dataColumns[0]?.format ? JSON.parse(this.dataColumns[0].format) : {}
          }
        }
        xData.forEach(item3 => {
          // console.log(this.handleFormatMoneyFormat(data[item3], format))
          // 处理格式这里需要根据配置的数据格式来处理 {"contentAlign":"left","decimalPlaces":0,"operationalRule":"value/10000",suffixChar:"万元",prefixChar:"￥"}
          result.push(this.handleFormatMoneyFormat(data[item3], format))
        })
        dataSource.push(result)
      })
      // console.log(dataSource, 'kkkkkkkkkkkkkkkkkkkkkkkk')
      const result = []
      xData.forEach((x, i) => {
        const arr = []
        dataSource.forEach(d => {
          arr.push(d[i])
        })
        result.push(arr)
      })
      this.legendDimColumns = [`${this.xDim || '横轴'}\\${(this.dataColumns && this.dataColumns.length > 1 ? '指标' : '') || this.yDim || '列维'}`].concat(legendData)
      this.dimColumns = xData
      this.dataSource = result
      // console.log(this.dimColumns, this.legendDimColumns, this.dataSource, 'lccccc')
    }
  }
}
</script>
<style lang="scss">
@import '@/styles/variables';
  .chart-pic-wrap:has(.dark){
    background:$primary !important;
    // background:#095093!important;
  }
</style>
<style scoped lang="scss">
@import '@/styles/variables';
.form-grid-wrapper{
  height:auto;
  padding:0px 15px 0;
  font-size:12px;
  font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
  &.report{
    h4{
      font-size:12px;
      font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
    }
  }
  &.dark{
    // background:rgba(9,80,147,0.8);
    color:white;
    h4{
      color:white;
    }
    ::v-deep{
      .vxe-table--render-default.border--default .vxe-table--header-wrapper, .vxe-table--render-default.border--full .vxe-table--header-wrapper, .vxe-table--render-default.border--outer .vxe-table--header-wrapper{
        background:$primary-hover;
        color:$secondText;
      }
      .vxe-table--render-default .vxe-body--row.row--hover, .vxe-table--render-default .vxe-body--row.row--hover.row--stripe{
        background:$primary-hover;
      }
      .vxe-table--render-default .vxe-table--body-wrapper table, .vxe-table--render-default .vxe-table--footer-wrapper table{
        background:$primary-hover;
      }
      .vxe-table--render-default .vxe-table--body-wrapper, .vxe-table--render-default .vxe-table--footer-wrapper{
        background:$primary-hover;
        color: $secondText;
      }
      .vxe-body--row {
        div {
          color: #fff;
        }
        &:hover {
          background: $primary !important;
        }
      }
    }
  }
  ::v-deep{
    .vxe-table--render-default{
      font-size:12px;
      font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
      .vxe-header--row{
        .wu-xiao .vxe-header--column {
            line-height: 45px;
            padding: 0!important;
            overflow: hidden;
            &:nth-of-type(n+2){
              .vxe-cell--title{
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: block;
              }
            }
        }
        .vxe-cell{
          text-align: center;
        }
      }
      .vxe-body--row{
        td:nth-of-type(1){
          text-align: center;
        }
      }
    }
  }
  /*模拟对角线*/
  .out {
    width:0px;height:0px;position:relative;
    span:not(.line){
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .col {font-style:normal;display:block;position:absolute;top:-32px;left:54px;width:77px;text-align: right;}
    .row {font-style:normal;display:block;position:absolute;top:-12px;left:0px;width:77px;text-align: left;}
    .line{ display:block; width:165px; height:1px; background:#000;-webkit-transform:rotate(15deg);-ms-transform:rotate(15deg); -moz-transform:rotate(15deg); transform:rotate(15deg); position:absolute; top:0px; left:-12px;}
  }
}
h4{
  text-align: center;
  font-family: "Microsoft Yahei";
  font-weight: bold;
  line-height: 40px;
  font-size:16px;
  margin:0;
  color:#464646;
}
</style>
