<!-- 文本组件，文本组件里可以输入变量 -->

<template>
  <div class="text-wrap">
    <el-input
      v-if="showInput"
      v-model="inputVal"
      type="textarea"
      :rows="10"
      @blur="handleBlur"
      @contextmenu.native.prevent.stop="showMenu"
    />
    <div v-else class="content">
      <template v-for="(item,index) in inputValArr">
        <span v-if="item.type==='text'" :key="index" style="white-space:pre-wrap;">
          {{ item.value }}
        </span>
        <!-- 这个是变量情况的处理 -->
        <span v-else-if="item.type==='var'" :key="index" class="isVar" @click.stop="handleConfigVar(item)">
          {{ handleGerVarData(item.code,item.value) | filterFormatMoney }}
          <!-- {{ modelType === 'view' ? (handleGerVarData(item.code,item.value)) : item.value }} -->
        </span>
        <br v-else :key="index">
      </template>

    </div>

    <div
      v-if="isShowMenu"
      :style="{'left': menuLeft + 'px', 'top': menuTop + 'px'}"
      class="customer-menu"
    >
      <div class="menu-first">
        <div>
          选择系统变量
          <div class="menu-second">
            <div
              v-for="sysVar in sysVarsList"
              :key="sysVar.code"
              @click="handleSelectVar(sysVar,'sys')"
            >
              {{ sysVar.name }}
            </div>
          </div>
        </div>
        <div>
          选择自定义变量
          <div class="menu-second">
            <div
              v-for="sysVar in variablesList"
              :key="sysVar.code"
              @click="handleSelectVar(sysVar,'custom')"
            >
              {{ sysVar.title }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div></template>

<script>
import dealData from '../mixins/dealData'
export default {
  mixins: [dealData],
  props: {
    sysVarsList: { // 系统变量列表
      type: Array,
      default() {
        return [

        ]
      }
    },
    variablesList: { // 变量获取的最终数值
      type: Array,
      default() {
        return []
      }
    },
    text: {
      type: String,
      default: ''
    },
    modelType: {
      type: String,
      default: ''
    },
    showInput: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      menuLeft: 0,
      menuTop: 0,
      isShowMenu: false
    }
  },
  computed: {
    inputVal: {
      get() {
        return this.text
      },
      set(val) {
        this.$emit('update:text', val)
      }
    },
    inputValArr() {
      const text = (this.inputVal || '').replace(/\n/g, '<br>')
      const varDot = text.match(/\$[^\$]+\$/g) || [] // 匹配到所有的含有变量的
      const textArr = text.split(/\$[^\$]+\$/) // 得到别的非变量的字符
      const result = []
      textArr.forEach(item => {
        if (item) {
          // 处理文本和文本里是否有换行符
          const newItem = item.split('<br>')
          if (newItem.length > 1) { // 有换行符
            newItem.slice(0).forEach(br => {
              if (br) {
                result.push({
                  type: 'text',
                  value: br
                })
              }
              const v2 = newItem.splice(0, 1)
              if (v2.length) {
                result.push({
                  type: 'br',
                  value: '<br>'
                })
              }
            })
          } else { // 没有换行符
            result.push({
              type: 'text',
              value: item
            })
          }
        }
        const v = varDot.splice(0, 1)
        if (v.length) { // 加入变量
          result.push({
            type: 'var',
            value: v[0],
            code: v[0].substr(1, v[0].length - 2)
          })
        }
      })
      // console.log(result, 'gggggg222')
      return result // 格式类似于 ['$www$', '123', '$www$', 'fff', '$www$']
    }
  },
  watch: {

  },
  methods: {
    handleBlur() {
      if (this.isShowMenu) {
        setTimeout(() => {
          this.isShowMenu = false
        }, 1000)
      }
    },
    handleSelectVar(varItem, type) {
      this.inputVal += `$${type === 'sys' ? varItem.name : varItem.title}$`
      this.isShowMenu = false
    },
    showMenu(e) {
      this.isShowMenu = true
      this.menuLeft = e.pageX
      this.menuTop = e.pageY
    },
    handleGerVarData(key, symbolName) {
      if (!this.variablesList || !this.variablesList.length) return '-'
      const varChart = this.variablesList.find(item => {
        return item.title === key
      })
      if (!varChart) { // 找不到变量名对应的当普通文本处理
        // 不是自定义变量，看是否是系统变量
        const varSysChart = this.sysVarsList.find(item => item.name === key)
        if (varSysChart) {
          // 接口获取变量的值
          // const val = getSystemVariableValue(varSysChart.code)
          return varSysChart && varSysChart.chart ? varSysChart.chart : '-'
        }
        return symbolName
      }
      if (!varChart.chart || !varChart.chart[0] || !varChart.chart[0][1]) {
        return '-'
      }
      const lastIndex = varChart.chart[0].length - 1
      return varChart.chart[0][lastIndex]
    },
    /**
     * 点击的变量名需要弹出配置项来存储值
     * @param item
     */
    handleConfigVar(item) {
      console.log(item, 'iiiii')
      this.$emit('handleConfigVar', item)
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/variables';
.text-wrap{
  font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
  ::v-deep{
    .el-textarea__inner{
      border:none;
    }
  }
  .content{
    padding: 5px 15px;
    font-family: "SimSun", "宋体","Avenir", "Helvetica Neue", Arial, Helvetica, sans-serif;
    font-size:12px;
    .isVar{
      color:$primary;
      // text-decoration: underline;
      cursor: pointer;
    }
  }
  .customer-menu{
    position: fixed;
    z-index: 1004;
    background-color: white;
    border-radius: 5px;
    text-align: left;
    font-size:14px;
    color:#606266;
    box-shadow: 0 0 5px 1px #ccc;
    .menu-first{
      padding:10px;
      padding-right:0;
      margin:0;
      font-weight: 700;
      .menu-second{
        padding-left:25px;
        font-weight: 400;
        max-height:100px;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 6px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: #c1c1c1;
          border-radius: 8px;
        }
        scrollbar-color: #c1c1c1 transparent;
        scrollbar-width: thin;
        &>div{
          cursor: pointer;
          line-height: 25px;
          &:hover{
            color:#004DE7;
          }
        }
      }
    }
  }
}

</style>
