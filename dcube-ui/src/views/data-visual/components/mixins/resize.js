import { debounce } from '@/utils'

export default {
  data() {
    return {
      $_sidebarElm: null,
      $_resizeHandler: null
    }
  },
  props: {
    reportType: {
      type: String,
      default: 'dashboard' // report
    },
    w: {
      type: Number,
      default: 1
    },
    h: {
      type: Number,
      default: 1
    },
    modelType: {
      type: String,
      default: 'edit'
    },
    autoSize: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    w() {
      this.$_resizeHandler()
    },
    h() {
      this.$_resizeHandler()
    },
    modelType() {
      this.$_resizeHandler()
    },
    autoSize() {
      this.$_resizeHandler()
    }
  },
  mounted() {
    this.$_resizeHandler = debounce(() => {
      if (this.reportType === 'report' && this.chartType === 'formGrid') {
        return
      }
      if (this.chart) {
        this.chart.resize()
      }
      if (this.reportType === 'dashboard' && this.chartType === 'formGrid' && this.$refs.formGrid) { // 面板的表单需要高度和当前格子一致
        this.tableMaxHeight = this.$refs.formGrid.parentElement.clientHeight - 40
      }
    }, 100)
    if (this.reportType === 'report' && this.chartType === 'formGrid') {
      return
    }
    this.$_initResizeEvent()
    this.$_initSidebarResizeEvent()
  },
  beforeDestroy() {
    if (this.reportType === 'report' && this.chartType === 'formGrid') {
      return
    }
    this.$_destroyResizeEvent()
    this.$_destroySidebarResizeEvent()
  },
  // to fixed bug when cached by keep-alive
  // https://github.com/PanJiaChen/vue-element-admin/issues/2116
  activated() {
    if (this.reportType === 'report' && this.chartType === 'formGrid') {
      return
    }
    this.$_initResizeEvent()
    this.$_initSidebarResizeEvent()
  },
  deactivated() {
    if (this.reportType === 'report' && this.chartType === 'formGrid') {
      return
    }
    this.$_destroyResizeEvent()
    this.$_destroySidebarResizeEvent()
  },
  methods: {
    // use $_ for mixins properties
    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential
    $_initResizeEvent() {
      // window.addEventListener('resize', this.$_resizeHandler)
    },
    $_destroyResizeEvent() {
      // window.removeEventListener('resize', this.$_resizeHandler)
    },
    $_sidebarResizeHandler(e) {
      if (e.propertyName === 'width') {
        this.$_resizeHandler()
      }
    },
    $_initSidebarResizeEvent() {
      this.$_sidebarElm = document.getElementsByClassName('sidebar-container')[0]
      this.$_sidebarElm && this.$_sidebarElm.addEventListener('transitionend', this.$_sidebarResizeHandler)
    },
    $_destroySidebarResizeEvent() {
      this.$_sidebarElm && this.$_sidebarElm.removeEventListener('transitionend', this.$_sidebarResizeHandler)
    }
  }
}
