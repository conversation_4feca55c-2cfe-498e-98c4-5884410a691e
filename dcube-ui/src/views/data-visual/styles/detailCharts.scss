@import '@/styles/variables';
.app-container:has(.chart-view-wrap){
  background:#f0f2f5;
}
.chart-board-wrap{
  height: calc(100vh - 50px - 34px - 12px - 12px - 10px);
  display: flex;
  justify-content: space-between;
  &.chart-board-wrap-dark{
    // background-image: url("../../../assets/dashboard-bg.png");
    // background-size: cover;
    .grid-wrap{
      background: $primary-hover;
      .chart-pic-wrap{
        background:none;
        .add-icon {
          color: #fff;
        }
      }
    }
  }
  // gap: 24px;
  &.chart-rt-model-wrap{
    height: calc(100vh - 50px - 30px);
    // padding:15px;
  }
  .grid-wrap{
    flex: 4;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    grid-auto-flow: row dense;
    grid-gap: 8px;
    &.hidden-over{
      overflow: hidden;
    }
    .chart-pic-wrap{
      background:white;
      position: relative;
      border: 1px solid $formBorder;
      &.hidden-over{
        overflow: hidden;
      }
      &:hover{
        cursor: pointer;
        border: 1px dashed $primary;
        .add-icon{
          color: $primary;
        }
        .operateBtn{
          opacity: 1;
        }
      }
      &-active{
        border-color: $primary;
      }
      .operateBtn{
        cursor: pointer;
        opacity: 0;
        position: absolute;
        left:5px;
        top:5px;
        transition: all linear .5s;
        z-index:9;
      }
      .deleteBtn,.filterBtn,.fullScreenBtn{
        padding:2px;
        margin-right:5px;
        margin-left:0;
      }
      .add-icon-wrap{
        height: 100%;
        position: relative;
        .add-icon{
          font-size: 32px;
          position: absolute;
          top: calc(50% - 18px);
          left: calc(50% - 18px);
        }
      }
    }
    // 视图模式
    .chart-view-model-wrap{
      border:none;
      &:hover{
        cursor: auto;
        border: none;
      }
    }
  }
  .chart-props-wrap{
    flex: 1;
    overflow: auto;
    overflow-x: hidden;
    padding-left:24px;
    position: relative;
    display: flex;
    flex-direction: column;
    .chart-collapse{
      position: absolute;
      left:0;
      top:50%;
      transform: translate(6px,-50%);
      button{
        padding: 10px 2px;
        border-radius: 6px 0 0 6px;
      }
    }
    .save-btn{
      width: 100%;
    }
    .fold-btn-top{
      margin-right:10px;
    }
    &.is-fold{
      flex: 0;
      // 折叠放到侧边的时候放开
      // flex-basis: 20px;

      // 折叠放到顶部的时候放开
      padding-left: 0;
      flex-basis: 40px;

      overflow: hidden;
      .fold-btn-top{
        margin-right:0px;
        margin-left:5px;
      }
      .save-btn,.switch-mode-btn,.prop-content,.props-theme{
        display: none;
      }
    }
    .fold-btn{
      
    }
    &.chart-props-view{
      position: absolute;
      top:12px;
      right:12px;
    }
    .prop-header{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      .props-theme{
        cursor: pointer;
        .svg-icon{
          font-size:16px;
          font-weight: bold;
        }
        &.light{
          .svg-icon{
            color:$primary;
          }
        }
        &.dark{
          .svg-icon{
            color:#001852;
          }
        }
      }
    }
    .prop-content{
      flex:1;
      font-size:14px;
      .prop-filter-wrap{
        .el-tag--light{
          background-color:$primary;
          color:white;
          border-color:$primary;
          &:hover{
            cursor: pointer;
            background-color: #66b1ff;
          }
          .el-tag__close{
            background:white;
            color:$primary;
          }
        }
      }
    }
    .prop-bottom{
      padding:15px 0 0 0;
      text-align: right;
    }
    .switch-mode-btn{
      font-size: 18px;
      color: $primary;
      text-decoration: underline;
      text-align: right;
      flex:1;
      &:hover{
        cursor: pointer;
      }
    }
    .prop-label{
      line-height: 28px;
      background-color: $formBorder;
      color: $primary;
      text-align: center;
    }
    .prop-value:last-child{
      // padding: 4px 8px;
      // border: 1px solid $formBorder;
    }
    .prop-value{
      font-size:12px; 
      &:not(.prop-filter-wrap){
        .el-select__tags{
          display: none;
        }
        .el-input--prefix.el-input--suffix{
          .el-input__inner{
            padding-left:15px;
          }
        }
      }
      
      .el-input__prefix{
        color:white;
        width:80%;
        height:calc(100% - 14px);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-top:7px;
        .select-prefix{
          flex:1;
          .el-menu--horizontal{
            border-bottom: 0;
            width: 100%;
            &>.el-submenu{
              width: 100%;
              .el-submenu__title{
                background:#004DE7;
                line-height: 26px;
                border-radius: 5px;
                width: 100%;
                height:auto;
                color: white;
                padding:0;
                border-bottom: 0;
                text-align: center;
                &:hover{
                  background:#66b1ff;
                }
                .el-submenu__icon-arrow{
                  display: none;
                }
              }
            }
          }
        }
        .select-prefix-multi{
          display: flex;
          justify-content: flex-start;
          background:white;
          &:hover{
            background:white;
          }
          .el-menu--horizontal{
            width: auto;
            &:hover{
              background:none;
            }
            &>.el-submenu{
              width: auto;
              background:none;
              .el-submenu__title{
                background:none;
                &:hover{
                  background:none;
                }
                span{
                  display: inline-block;
                  background:#004DE7;
                  padding:0 5px;
                  margin-right:10px;
                  border-radius: 5px;
                  &:hover{
                    background:#66b1ff;
                  }
                }
              }
            }
          }
        }
      }
      .el-input--small,.el-submenu__title{
        font-size:12px;
      }
      .vue-treeselect__control{
        padding-left:0;
      }
      &>.el-input .el-input__inner{
        padding-left:5px;
      }
    }
  }
  .el-select, .el-input {
    width: 100%;
  }
  .vue-treeselect__placeholder, .vue-treeselect__single-value{
    line-height: 30px;
  }
  .size-change-input.el-input-number .el-input>input{
    pointer-events: none;
  }
  .size-change-input{
    width: 36%;
  }
  .block_middle{
    display: block;
    text-align: center;
    position: relative;
    top:50%;
    transform: translateY(-50%);
  }
}
.img-radio{
  width: 80px;
  height: 80px;
  vertical-align: middle;
}
.el-submenu{
  .el-menu--popup{
    min-width: 80px;
  }
  .el-menu-item{
    min-width: 80px;
  }
}
.el-menu--popup{
  min-width: 120px;
}
.filterCustomDialog{
  .el-form-item{
    display: inline-block;
  }
}
.submenu-12 .el-menu-item, 
.submenu-12 .el-menu .el-submenu__title{
  height:26px;
  line-height: 26px;
}
.select-dropdown-12 .el-select-dropdown__item,.el-submenu__title,.submenu-12,.submenu-12 .el-menu-item{
  font-size:12px;
}
.max-chart-show-dialog{
  .el-dialog__header{
    padding:0;
    .el-dialog__headerbtn{
      z-index: 2;
      padding:10px;
      cursor: pointer;
    }
  }
  .el-dialog__body{
    height:100%;
  }
}