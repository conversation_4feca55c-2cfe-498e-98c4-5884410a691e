<template>
  <div class="login-container">
    <div class="login-bg">
      <div class="slogan-box">
        <!-- <img class="slogan-img" src="@/assets/slogan.png" alt=""> -->
        <span class="slogan-text">超大数据，超快计算，超好上手</span>
      </div>
    </div>
    <div class="login-form-box">
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on" label-position="left">
        <div class="title-container">
          <img src="@/assets/form-logo.png" alt="">
        </div>
        <el-form-item prop="username">
          <span class="svg-container user" />
          <el-input
            ref="username"
            v-model="loginForm.username"
            placeholder="用户名"
            name="username"
            type="text"
            tabindex="1"
            auto-complete="on"
          />
        </el-form-item>

        <el-form-item prop="password">
          <span class="svg-container pwd" />
          <el-input
            :key="passwordType"
            ref="password"
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="密码"
            name="password"
            tabindex="2"
            auto-complete="on"
            @keyup.enter.native="handleLogin"
          />
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-form-item>
        <el-button :loading="loading" type="primary" @click.native.prevent="handleLogin">登录</el-button>
      </el-form>
    </div>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'

export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('请填写用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 3) {
        callback(new Error('密码不能小于3位字符'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: 'admin',
        password: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      // this.$router.push({ path: '/dashboard' })
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          this.loading = true
          const res = await this.$store.dispatch('user/getPublicKey')
          if (res) {
            this.$store.dispatch('user/login', this.loginForm).then(() => {
              this.loading = false
              this.$message.success({ message: '登录成功', duration: 1000 })
              sessionStorage.removeItem('navActiveIdx')
              this.$router.push({ path: '/' })
            }).catch(() => {
              this.loading = false
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 560px 1fr;
  background-color: #fff;
  .login-bg {
    background-image: url("../../assets/login-bg.png");
    background-size: cover;
    background-position: bottom;
    position: relative;
    .slogan-box {
      position: absolute;
      top: 90px;
      left: 50px;
      display: flex;
      align-items: center;
      .slogan-img {
        width: 160px;
      }
      .slogan-text {
        font-size: 32px;
        color: #fff;
        font-weight: 500;
      }
    }
  }
  .login-form-box {
    display: grid;
    justify-content: center;
    align-content: center;
  }
  .login-form {
    width: 480px;
    height: 640px;
    box-sizing: border-box;
    /* 背景色/边框 */
    border: 1px solid #E1E5EB;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 9px;
    display: flex;
    flex-direction: column;
    .el-form-item  {
      margin-bottom: 24px;
    }
    ::v-deep .el-form-item__content {
      width: 368px;
      height: 52px;
      margin: 0 auto;
      display: flex;
      .el-input__inner {
        border: none;
        height: 52px;
        background: #f5f7fa;
        padding-left: 40px;
        color: #1a253b;
      }
    }
    .el-button {
      width: 368px;
      height: 52px;
      margin: 0 auto;
      font-size: 20px;
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    position: absolute;
    z-index: 10;
    width: 20px;
    height: 20px;
    left: 10px;
    top: 16px;
    background-image: url("../../assets/user-line.svg");
    &.pwd {
      background-image: url("../../assets/lock-line.svg");
    }
  }

  .title-container {
    height: 264px;
    padding: 80px 0 64px 0;
    img {
      width: 125px;
      height: 120px;
      display: block;
      margin: 0 auto;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: #fff;
    cursor: pointer;
    user-select: none;
  }
  ::v-deep .el-form-item__error {
    color: red;
  }
}
</style>
