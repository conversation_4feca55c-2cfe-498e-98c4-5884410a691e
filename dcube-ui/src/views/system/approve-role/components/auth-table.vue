<template>
  <div>
    <el-dialog :title="addDeptTitle" :visible="true" width="600px" :close-on-click-modal="false" @close="closeAddDept">
      <el-form ref="deptForm" size="small" :model="addDeptForm" :rules="rules" label-position="right" label-width="90px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="上级机构" prop="parentId">
              <treeselect v-model="addDeptForm.parentId" :options="deptTree" :show-count="true" placeholder="请选择上级机构" @select="selectDept" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构名称" prop="deptName">
              <el-input v-model="addDeptForm.deptName" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="机构层级" prop="deptLevel">
              <el-input-number v-model="addDeptForm.deptLevel" class="w-full" :min="0" :max="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示顺序" prop="orderNum">
              <el-input-number v-model="addDeptForm.orderNum" class="w-full" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="机构属性" prop="deptType">
              <el-select v-model="addDeptForm.deptType" class="w-full">
                <el-option v-for="item in deptTypeArr" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="addDeptForm.remark" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeAddDept">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleAddDept">添 加</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Treeselect from '@riophae/vue-treeselect'
// import the styles
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: { Treeselect },
  props: {
    currentDeptId: {
      type: [String, Number],
      default: ''
    },
    currentDeptName: {
      type: [String],
      default: ''
    }
  },

  data() {
    return {
      addDeptForm: {
        deptName: '',
        parentName: '',
        deptLevel: 0,
        parentId: undefined,
        orderNum: 0,
        deptType: '',
        remark: ''
      },
      rules: {
        deptName: [
          { required: true, message: '请输入机构名称', trigger: 'blur' }
        ],
        parentId: [
          { required: true, message: '请选择上级', trigger: ['change', 'blur'] }
        ],
        deptLevel: [
          { required: true, message: '请选择机构层级', trigger: ['change', 'blur'] }
        ],
        orderNum: [
          { required: true, message: '请选择显示顺序', trigger: ['change', 'blur'] }
        ],
        deptType: [
          { required: true, message: '请输入机构属性', trigger: ['blur'] }
        ]
      },
      showParentLevel: false,
      deptTree: this.$store.getters.deptTree,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      deptTypeArr: Object.freeze([{
        value: 'front',
        label: '前台'
      }, {
        value: 'middle',
        label: '中台'
      }, {
        value: 'back',
        label: '后台'
      }])
    }
  },
  computed: {
    addDeptTitle() {
      return this.currentDeptId ? '添加子机构' : '添加机构'
    }
  },
  watch: {
    currentDeptId: {
      handler(val) {
        if (val) {
          this.setParentDept(val, this.currentDeptName)
        }
      },
      immediate: true
    }
  },
  created() {
    this.resetForm()
  },
  methods: {
    handleNodeClick(data) {
      const { label, id } = data
      this.addDeptForm.parentName = label
      this.addDeptForm.parentId = id
      this.$refs.xDown.hidePanel()
    },
    selectDept(val) {
      this.$refs.deptForm.clearValidate('parentId')
    },
    // 添加子机构时设置当前上层机构
    setParentDept(id, name) {
      this.addDeptForm.parentName = name
      this.addDeptForm.parentId = id
    },
    // 打开
    showTree() {
      this.$refs.xDown.showPanel()
    },
    // 重置弹框
    resetForm() {
      if (this.$refs.deptForm) {
        this.$refs.deptForm.resetFields()
      }
    },
    // 关闭分组弹框
    closeAddDept() {
      this.$emit('close')
    },
    handleAddDept() {
      this.$refs['deptForm'].validate((valid) => {
        if (valid) {
          this.$emit('addDept', this.addDeptForm)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }

  }
}
</script>
<style lang="scss" scoped>
.dept-tree {
  height: 200px;
  overflow: auto;
  border:1px solid #f6f7f8;
}

</style>
