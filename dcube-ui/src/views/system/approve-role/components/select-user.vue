<template>
  <!-- 授权用户 -->
  <el-dialog title="选择用户" :visible.sync="visible" width="900px" top="5vh" append-to-body>
    <el-form ref="queryForm" :model="queryParams" size="small" :inline="true">
      <el-form-item label="用户名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <vxe-table
        ref="xTable1"
        size="mini"
        :data="userList"
        :row-config="{isCurrent: true, isHover: true}"
        @checkbox-all="handleSelectionChange"
        @checkbox-change="handleSelectionChange"
      >
        <vxe-column type="checkbox" width="60" />
        <vxe-column field="userName" title="用户名称" show-overflow />
        <vxe-column field="nickName" title="用户昵称" show-overflow />
        <vxe-column field="email" title="邮箱" />
        <vxe-column field="phonenumber" title="手机" show-overflow />
        <vxe-column field="status" title="状态">
          <template #default="{ row }">
            <dict-tag :options="dict.type.sys_normal_disable" :value="row.status" />
          </template>
        </vxe-column>
        <vxe-column field="createTime" title="创建时间" width="160">
          <template #default="{ row }">
            <span>{{ parseTime(row.createTime) }}</span>
          </template>
        </vxe-column>
        <!-- <vxe-column title="操作" width="100">
          <template #default="{ row }">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-circle-close"
              @click="cancelAuthUser(row)"
            >取消授权</el-button>
          </template>
        </vxe-column> -->
        <template #empty>
          <table-empty />
        </template>
      </vxe-table>
      <el-pagination
        :current-page="queryParams.pageNum"
        :page-sizes="[20,50,100]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="visible = false">取 消</el-button>
      <el-button size="mini" type="primary" @click="handleSelectUser">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { unallocatedUserList, authUserSelectAll, authUserCancel } from '@/api/system/func-role'
export default {
  dicts: ['sys_normal_disable'],
  props: {
    // 角色编号
    roleId: {
      default: 0,
      type: [Number, String]
    }
  },
  data() {
    return {
      // 遮罩层
      visible: false,
      // 选中数组值
      userIds: [],
      // 总条数
      total: 0,
      // 未授权用户数据
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        roleId: undefined,
        userName: undefined,
        phonenumber: undefined
      }
    }
  },
  methods: {
    /** 取消授权按钮操作 */
    cancelAuthUser(row) {
      const roleId = this.queryParams.roleId
      this.$confirm('确认要取消该用户"' + row.userName + '"角色吗？', '提示').then(function() {
        return authUserCancel({ userId: row.userId, roleId: roleId })
      }).then(() => {
        this.getList()
        this.$message.success('取消授权成功')
      }).catch(() => {})
    },
    // 显示弹框
    show() {
      this.queryParams.roleId = this.roleId
      this.getList()
      this.visible = true
    },
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row)
    },
    // 多选框选中数据
    handleSelectionChange() {
      const selection = this.$refs.xTable1.getCheckboxRecords()
      this.userIds = selection.map(item => item.userId)
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    // 查询表数据
    getList() {
      unallocatedUserList(this.queryParams).then(res => {
        this.userList = res.rows
        this.total = res.total
      })
    },
    /** 查询按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 选择授权用户操作 */
    handleSelectUser() {
      const roleId = this.queryParams.roleId
      const userIds = this.userIds.join(',')
      if (userIds === '') {
        this.$message.error('请选择要分配的用户')
        return
      }
      authUserSelectAll({ roleId: roleId, userIds: userIds }).then(res => {
        this.$message.success(res.msg)
        if (res.code === 200) {
          this.visible = false
          this.$emit('ok')
        }
      })
    }
  }
}
</script>
