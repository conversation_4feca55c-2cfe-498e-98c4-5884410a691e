<template>
  <div class="app-container dimension-set">
    <div class="left">
      <div class="top-search-area">
        <el-form ref="queryForm" :model="queryParams" size="small" :inline="true">
          <el-form-item label="维度名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入维度名称"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="getDimensionList">查询</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <div class="main-btn">

          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="openAddGroup"
          >添加分组</el-button>
        </div>
      </div>
      <vxe-table
        ref="xTree"
        size="mini"
        :loading="loadingDimension"
        show-overflow
        :data="dimensionList"
        :tree-config="treeConfig"
        :row-config="{isCurrent: true, isHover: true}"
      >
        <vxe-column title="维度名称" tree-node width="160">
          <template #default="{ row }">
            <span class="flex-vertical-center custom-node" :class="row.dimDirectoryType==='INSTANCE'?'table-node':''" @click="clickTreeNode(row)">
              <template v-if="row.dimDirectoryType==='GROUP'">
                <i class="tree-node-icon" :class="$refs.xTree.isTreeExpandByRow(row) ? 'vxe-icon-folder-open' : 'vxe-icon-folder'" />
              </template>
              <template v-else>
                <i class="tree-node-icon vxe-icon-chart-radar" />
              </template>
              <div class="custom-ellipsis">{{ row.dimDirectoryName }}</div>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="维度类型">
          <template #default="{ row }">
            <div class="flex-vertical-center">{{ row.dimDirectoryType==='INSTANCE'? (row.dimType==='COMMON'?'一般维度':'主数据维度'):'' }}</div>
          </template>
        </vxe-column>
        <vxe-column title="操作" width="340">
          <template #default="{ row }">
            <el-button
              v-if="row.dimDirectoryType==='GROUP'"
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="openChildGroup(row)"
            >添加子组</el-button>
            <el-button
              v-if="row.dimDirectoryType==='GROUP'"
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="openAddDimension(row)"
            >添加维度</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="openRename(row,'dim')"
            >重命名</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="delDimension(row)"
            >删除</el-button>
          </template>
        </vxe-column>
        <template #empty>
          <table-empty />
        </template>
      </vxe-table>

      <!-- <el-pagination
        :current-page="queryParams.pageNum"
        :page-sizes="[20,50,100]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      /> -->
    </div>
    <div class="right">
      <template v-if="showRight">
        <div class="dimension-title">
          维度名称：<el-tag v-if="currentDimName" size="small" effect="plain">{{ currentDimName }}</el-tag>
        </div>
        <div
          class="top-search-area"
          style="margin-top:10px"
        >
          <el-form ref="queryForm1" :model="queryParams1" size="small" :inline="true">
            <el-form-item label="维度成员" prop="name">
              <el-input
                v-model="queryParams1.name"
                placeholder="请输入维度成员"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="getMemberList">查询</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery1">重置</el-button>
            </el-form-item>
          </el-form>

          <div class="main-btn">
            <el-button size="mini" type="warning" plain icon="el-icon-upload" @click="importData">导入Excel</el-button>
            <el-button size="mini" type="warning" plain icon="el-icon-download" @click="downTemplate">下载Excel模板</el-button>
            <el-button
              :disabled="!currentDim"
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="openAddMember"
            >添加成员</el-button>
            <!-- <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="openSelectDim"
            >打开选择维度</el-button>
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="openDefineTable"
            >打开表定义</el-button> -->
          </div>
        </div>
        <vxe-table
          ref="memberTree"
          size="mini"
          :loading="loadingMember"
          show-overflow
          :data="memberList"
          :tree-config="memberTreeConfig"
          :row-config="{isCurrent: true, isHover: true}"
        >
          <vxe-column field="dimName" title="成员名称" tree-node />
          <vxe-column title="操作" width="400">
            <template #default="{ row }">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="openAddChildMember(row)"
              >添加子成员</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit-outline"
                @click="openRename(row,'member')"
              >重命名</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="delMember(row)"
              >删除</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-top"
                @click="moveItem(row.id,'UP')"
              >上移</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-bottom"
                @click="moveItem(row.id,'DOWN')"
              >下移</el-button>
            </template>
          </vxe-column>
          <template #empty>
            <table-empty />
          </template>
        </vxe-table>
      </template>

    </div>
    <!-- 重命名弹框 -->
    <el-dialog :title="reNameDiaTitle" :visible.sync="reNameVisible" width="400px" :close-on-click-modal="false" @close="closeReName">
      <el-form ref="reNameForm" label-position="right" label-width="60px" size="small" :model="reNameForm" :rules="reNameRules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="reNameForm.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeReName">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleReName">确 认</el-button>
      </div>
    </el-dialog>
    <!-- 添加成员 -->
    <el-dialog :title="memberDiaTitle" :visible.sync="memberVisible" width="400px" :close-on-click-modal="false" @close="closeMember">
      <el-form ref="memberForm" label-position="right" label-width="60px" size="small" :model="memberForm" :rules="memberRules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="memberForm.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeMember">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleAddMember">添 加</el-button>
      </div>
    </el-dialog>
    <!-- 添加分组 -->
    <el-dialog :title="groupDiaTitle" :visible.sync="groupVisible" width="400px" :close-on-click-modal="false" @close="closeGroup">
      <el-form ref="groupForm" label-position="right" label-width="60px" size="small" :model="groupForm" :rules="groupRules">
        <el-form-item label="名称" prop="name">
          <el-input v-model="groupForm.name" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeGroup">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleAddGroup">添 加</el-button>
      </div>
    </el-dialog>
    <!-- 添加维度 -->
    <el-dialog :title="dimenDiaTitle" :visible.sync="dimenVisible" width="400px" :close-on-click-modal="false" @close="closeDimen">
      <el-form ref="dimenForm" label-position="right" label-width="90px" size="small" :model="dimenForm" :rules="dimenRules">
        <el-form-item label="维度名称" prop="dimDirectoryName">
          <el-input v-model="dimenForm.dimDirectoryName" />
        </el-form-item>
        <el-form-item label="维度类型" prop="dimType">
          <el-select
            v-model="dimenForm.dimType"
            style="width: 100%;"
            placeholder="维度类型"
          >
            <el-option
              v-for="item in dict.type.dim_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeDimen">取 消</el-button>
        <el-button size="mini" type="primary" @click="handleAddDimen">添 加</el-button>
      </div>
    </el-dialog>
    <!-- 选择维度 -->
    <select-dim v-if="showSelectDim" :dim-tree-data="dimTreeData" @closeSelectDim="closeSelectDim" @selectDim="selectDim" />
    <!-- 表定义 -->
    <define-table :show-drawer="showDrawer" @closeDrawer="closeDrawer" />
    <!-- 添加主数据维度 -->
    <el-dialog :title="'添加主数据维度'" :visible.sync="deptTreeShow" width="600px" append-to-body @close="closeDimTree">
      <div class="dim-tree">
        <div class="top-area">
          <div class="left">成员名称</div>
          <div class="right">
            <el-checkbox v-model="checkStrick" @change="handleCheckedTreeConnect($event)">父子联动</el-checkbox>
            <el-button type="text" status="primary" size="mini" @click="checkDimTree(1)">仅选中1级</el-button>
            <el-button type="text" status="primary" size="mini" @click="checkDimTree(2)">选中到2级</el-button>
            <el-button type="text" status="primary" size="mini" @click="checkDimTree(3)">选中到3级</el-button>
          </div>
        </div>
        <el-tree
          ref="deptTreeRef"
          class="tree-border"
          :data="deptTreeData"
          show-checkbox
          default-expand-all
          node-key="deptId"
          :check-strictly="!checkStrick"
          empty-text="加载中，请稍候"
          :props="defaultProps"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeDimTree">取 消</el-button>
        <el-button size="mini" type="primary" @click="changeDimMember">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import VXETable from 'vxe-table'
import { masterDimGetDeptList } from '@/api/system/department'
import { listToTree } from '@/utils/index'
import { cloneDeep } from 'lodash'
import { getDimensionTree, getMember, addDim, delDim, updateDim, addMember, delMember, updateMember, moveDim } from '@/api/system/dimension-set'
import SelectDim from '@/components/SelectDim'
import DefineTable from '@/components/DefineTable'
export default {
  components: {
    SelectDim,
    DefineTable
  },
  dicts: ['dim_type', 'biz_storage_type'],
  data() {
    return {
      currentDim: null,
      treeConfig: {
        transform: true,
        rowField: 'id',
        parentField: 'parentId'
      },
      memberTreeConfig: {
        transform: true,
        rowField: 'id',
        parentField: 'parentId',
        expandAll: true
      },
      loadingDimension: false,
      loadingMember: false,
      queryParams: {
        name: undefined
      },
      queryParams1: {
        name: ''
      },
      // total: 0,
      dimensionList: [],
      memberList: [],
      reNameDiaTitle: '重命名',
      reNameForm: {
        name: ''
      },
      reNameRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]

      },
      reNameVisible: false,
      memberDiaTitle: '添加成员',
      memberVisible: false,
      memberForm: {
        name: ''
      },
      memberRules: {
        name: [
          { required: true, message: '请输入成员名称', trigger: 'blur' }
        ]
      },
      groupDiaTitle: '添加分组',
      groupVisible: false,
      groupForm: {
        name: ''
      },
      groupRules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' }
        ]
      },
      dimenDiaTitle: '添加维度',
      dimenVisible: false,
      dimenForm: {
        dimDirectoryName: '',
        dimType: 'COMMON'
      },
      dimenRules: {
        name: [
          { required: true, message: '请输入维度名称', trigger: 'blur' }
        ]
      },
      showSelectDim: false,
      dimTreeData: [{
        label: '一级 1',
        children: [{
          label: '二级 1-1',
          children: [{
            label: '三级 1-1-1'
          }]
        }]
      }, {
        label: '一级 2',
        children: [{
          label: '二级 2-1',
          children: [{
            label: '三级 2-1-1'
          }]
        }, {
          label: '二级 2-2',
          children: [{
            label: '三级 2-2-1'
          }]
        }]
      }, {
        label: '一级 3',
        children: [{
          label: '二级 3-1',
          children: [{
            label: '三级 3-1-1'
          }]
        }, {
          label: '二级 3-2',
          children: [{
            label: '三级 3-2-1'
          }]
        }]
      }],
      showDrawer: false,
      dimTypeArr: [{
        label: '一般维度',
        value: 0
      }, {
        label: '主数据维度',
        value: 1
      }],
      reNameType: 'dim',
      currentMember: null,
      showRight: false,
      expandTimer: null,
      activeTimer: null,
      deptTreeShow: false,
      deptTreeData: [],
      originDeptTree: [],
      checkStrick: false,
      defaultProps: {
        children: 'children',
        label: 'deptName'
      },
      checkTreeTimer: false,
      expandMemberTimer: null
    }
  },
  computed: {
    currentDimName() {
      return this.currentDim && this.currentDim.dimDirectoryName
    }
  },
  beforeDestroy() {
    if (this.expandTimer) clearTimeout(this.expandTimer)
    if (this.activeTimer) clearTimeout(this.activeTimer)
    if (this.expandMemberTimer) clearTimeout(this.expandMemberTimer)
    this.expandTimer = null
    this.activeTimer = null
    this.expandMemberTimer = null
  },
  created() { console.log(this.dict.type.dim_type) },
  mounted() {
    this.getDimensionList()
  },
  methods: {
    // 树权限（父子联动）
    handleCheckedTreeConnect(value) {
      this.checkStrick = !!value
    },
    // 勾选到几级
    checkDimTree(idx) {
      const arr = cloneDeep(this.originDeptTree)
      const checkIdArr = []
      if (idx === 1) {
        arr.forEach(item => {
          checkIdArr.push(item.deptId)
        })
      } else if (idx === 2) {
        arr.forEach(item => {
          checkIdArr.push(item.deptId)
          if (item.children && item.children.length) {
            item.children.forEach(child => {
              checkIdArr.push(child.deptId)
            })
          }
        })
      } else {
        arr.forEach(item => {
          checkIdArr.push(item.deptId)
          if (item.children && item.children.length) {
            item.children.forEach(child => {
              checkIdArr.push(child.deptId)
              if (child.children && child.children.length) {
                child.children.forEach(grand => {
                  checkIdArr.push(grand.deptId)
                })
              }
            })
          }
        })
      }
      this.clearCheckTimer()
      this.checkTreeTimer = setTimeout(() => {
        this.$refs.deptTreeRef.setCheckedKeys(checkIdArr)
      }, 10)
    },
    clearCheckTimer() {
      if (this.checkTreeTimer) {
        window.clearTimeout(this.checkTreeTimer)
      }
      this.checkTreeTimer = null
    },
    changeDimMember() {
      if (!this.$refs.deptTreeRef.getCheckedKeys().length) {
        this.$message.error('请选择维度成员')
        return
      }
    },
    closeDimTree() {
      this.deptTreeData = []
      this.deptTreeShow = false
    },
    clickTreeNode(row) {
      this.currentDim = row
      if (row.dimDirectoryType === 'GROUP') {
        this.showRight = false
      } else {
        if (row.dimType === 'COMMON') {
          this.showRight = true
          this.getMemberList()
        } else {
          this.showRight = false
          masterDimGetDeptList().then(res => {
            this.loading = false
            if (res.code === 200) {
              this.deptTreeData = listToTree(res.data, 'deptId')
              this.originDeptTree = cloneDeep(this.deptTreeData)
            }
          })
          this.deptTreeShow = true
        }
      }
    },
    treeToList(tree) {
      const res = []
      const dgFunc = (tree) => {
        for (let i = 0; i < tree.length; i++) {
          const node = tree[i]
          node.parentId = node.parentId || 0
          res.push(node)
          if (node.children) {
            node.children.forEach((child) => { child.parentId = node.id })
            dgFunc(node.children)
          }
        }
      }
      dgFunc(tree)
      return res
    },
    openSelectDim() {
      this.showSelectDim = true
    },
    closeSelectDim() {
      this.showSelectDim = false
    },
    selectDim() {

    },
    openDefineTable() {
      this.showDrawer = true
    },
    closeDrawer() {
      this.showDrawer = false
    },
    async importData() {
      try {
        const { file } = await VXETable.readFile({
          types: ['xlsx', 'xls']
        })
        VXETable.modal.alert(`文件名：${file.name}，文件大小：${file.size}`)
      } catch (e) {
        console.log(e)
      }
    },
    downTemplate() {

    },
    /** 查询维度列表 */
    getDimensionList(id) {
      this.loadingDimension = true
      getDimensionTree({ dimDirectoryName: this.queryParams.name }).then(response => {
        this.dimensionList = response.data
        if (id) {
          this.loopExpandTree(id, 'xTree')
        }
      }).finally(() => {
        this.loadingDimension = false
      })
    },
    expandAllMember(id) {
      if (this.expandMemberTimer) {
        window.clearTimeout(this.expandMemberTimer)
        this.expandMemberTimer = null
      }
      this.expandMemberTimer = setTimeout(() => {
        this.$refs.memberTree.setAllTreeExpand(true)
        if (id) {
          const target = this.memberList.find(item => item.id === id)
          this.setCurrentRow(target, 'memberTree')
        }
      }, 10)
    },
    /** 查询维度成员列表 */
    getMemberList(id) {
      this.loadingMember = true
      const params = {
        dimDirectoryId: this.currentDim?.id || '',
        dimName: this.queryParams1.name
      }
      getMember(params).then(res => {
        this.memberList = res.data
        this.expandAllMember(id)
      }).finally(() => {
        this.loadingMember = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.name = ''
      this.getDimensionList()
    },
    /** 重置按钮操作 */
    resetQuery1() {
      this.queryParams1.name = ''
      this.getMemberList()
    },
    // 重置弹框
    resetForm() {
      if (this.$refs.addOriginForm) {
        this.$refs.addOriginForm.resetFields()
      }
    },
    loadChildrenMethod({ row }) {
    // 异步加载子节点
      return new Promise(resolve => {
        setTimeout(() => {
          const childs = [
            { id: row.id + 100000, parentId: row.id, name: row.name + 'Test45', type: 'mp4', size: null, date: '2021-10-03', hasChild: true },
            { id: row.id + 150000, parentId: row.id, name: row.name + 'Test56', type: 'mp3', size: null, date: '2021-07-09', hasChild: false }
          ]
          resolve(childs)
        }, 500)
      })
    },
    // 添加分组
    openAddGroup() {
      this.currentDim = null
      this.groupVisible = true
    },
    // 添加子组
    openChildGroup(row) {
      this.currentDim = row
      this.groupVisible = true
    },
    closeGroup() {
      this.$refs.groupForm.resetFields()
      this.groupVisible = false
    },
    handleAddGroup() {
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          const params = {
            dimDirectoryName: this.groupForm.name,
            dimDirectoryType: 'GROUP',
            parentId: this.currentDim ? this.currentDim.id : 0
          }
          addDim(params).then(res => {
            if (res.code === 200) {
              this.$message.success('添加成功')
              this.groupVisible = false
              this.getDimensionList(res?.data?.id)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 需要循环遍历该节点上的所有父节点并一一展开
    loopExpandTree(id, ref) {
      const validList = ref === 'xTree' ? this.dimensionList : this.memberList
      const addRow = validList.find(item => item.id === id)
      if (addRow && addRow.parentId !== 0) {
        const expandRow = validList.find(item => item.id === addRow.parentId)
        if (expandRow && expandRow.id) {
          if (this.$refs[ref]) {
            // this.$refs.treeTable.setAllTreeExpand(false)
            this.expandTree(expandRow, ref)
          }
          this.loopExpandTree(expandRow.id, ref)
        }
      } else {
        this.expandTree(addRow, ref)
      }
      this.setCurrentRow(addRow, ref)
    },
    // 展开树
    expandTree(row, ref) {
      if (this.expandTimer) clearTimeout(this.expandTimer)
      this.expandTimer = setTimeout(() => {
        this.$refs[ref].setTreeExpand(row, true)
      }, 0)
    },
    // 高亮行
    setCurrentRow(row, ref) {
      this.activeTimer = setTimeout(() => {
        this.$refs[ref].setCurrentRow(row)
      }, 100)
    },
    // 添加维度
    openAddDimension(row) {
      this.dimenVisible = true
      this.currentDim = row
    },
    closeDimen() {
      this.$refs.dimenForm.resetFields()
      this.dimenVisible = false
    },
    handleAddDimen() {
      this.$refs['dimenForm'].validate((valid) => {
        if (valid) {
          const { dimDirectoryName, dimType } = this.dimenForm
          const { id } = this.currentDim
          const params = {
            dimDirectoryName,
            dimDirectoryType: 'INSTANCE',
            dimType,
            parentId: id
          }
          addDim(params).then(res => {
            if (res.code === 200) {
              this.$message.success('新增成功')
              this.dimenVisible = false
              this.getDimensionList(res?.data?.id)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 删除维度
    delDimension(row) {
      this.$confirm('确定删除该维度吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delDim(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            const target = this.dimensionList.find(item => item.id === row.id)
            this.getDimensionList(target.parentId)
          }
        })
      })
    },
    // 打开添加成员
    openAddMember() {
      this.currentMember = null
      this.memberVisible = true
    },
    // 打开添加子成员
    openAddChildMember(row) {
      this.currentMember = row
      this.memberVisible = true
    },
    closeMember() {
      this.$refs.memberForm.resetFields()
      this.memberVisible = false
    },
    // 添加成员
    handleAddMember() {
      this.$refs['memberForm'].validate((valid) => {
        if (valid) {
          const params = {
            dimName: this.memberForm.name,
            dimDirectoryId: this.currentDim.id,
            parentId: this.currentMember ? this.currentMember.id : 0
          }
          addMember(params).then(res => {
            if (res.code === 200) {
              this.$message.success('添加成功')
              this.memberVisible = false
              this.getMemberList(res?.data.id)
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 删除成员
    delMember(row) {
      this.$confirm('确定删除该维度成员吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delMember(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            const target = this.memberList.find(item => item.id === row.id)
            this.getMemberList(target.parentId)
          }
        })
      })
    },
    // 后端移动维度
    moveItem(id, moveType) {
      moveDim({ id, moveType }).then(res => {
        if (res.code === 200) {
          this.$message.success('移动成功')
          this.getMemberList()
        }
      })
    },
    // 纯前端移动维度
    moveNode(arr, sourceId, direction) {
      const data = cloneDeep(arr)
      // 创建节点映射表
      const nodeMap = new Map()
      data.forEach(node => nodeMap.set(node.id, node))

      // 获取要移动的节点
      const sourceNode = nodeMap.get(sourceId)
      if (!sourceNode) return data

      // 获取父节点ID（根节点特殊处理）
      const parentId = sourceNode.parentId === '0' ? 'root' : sourceNode.parentId

      // 获取同级节点
      const siblings = data.filter(node =>
        node.parentId === (parentId === 'root' ? '0' : parentId)
      )

      // 获取当前节点在兄弟节点中的位置
      const sourceIndex = siblings.findIndex(node => node.id === sourceId)
      if (sourceIndex === -1) return data

      // 检查边界
      if (direction === 'up' && sourceIndex === 0) return data
      if (direction === 'down' && sourceIndex === siblings.length - 1) return data

      // 计算目标位置
      const targetIndex = direction === 'up' ? sourceIndex - 1 : sourceIndex + 1
      const targetNode = siblings[targetIndex]

      // 收集所有要移动的节点ID（当前节点及其后代）
      const moveIds = new Set()
      const collectIds = (id) => {
        moveIds.add(id)
        data.filter(node => node.parentId === id)
          .forEach(child => collectIds(child.id))
      }
      collectIds(sourceId)

      // 分离要移动的节点和其他节点
      const nodesToMove = []
      const otherNodes = []
      data.forEach(node => {
        if (moveIds.has(node.id)) nodesToMove.push(node)
        else otherNodes.push(node)
      })

      // 在otherNodes中找到目标节点的位置
      const targetNodeIndex = otherNodes.findIndex(node => node.id === targetNode.id)
      if (targetNodeIndex === -1) return data

      // 根据移动方向插入节点
      const result = []
      if (direction === 'up') {
        // 上移：插入到目标节点之前
        result.push(
          ...otherNodes.slice(0, targetNodeIndex),
          ...nodesToMove,
          ...otherNodes.slice(targetNodeIndex)
        )
      } else {
        // 下移：插入到目标节点之后
        result.push(
          ...otherNodes.slice(0, targetNodeIndex + 1),
          ...nodesToMove,
          ...otherNodes.slice(targetNodeIndex + 1)
        )
      }

      this.memberList = result
      this.expandAllMember(sourceId)
    },
    // 重命名
    openRename(row, type) {
      this.reNameVisible = true
      this.reNameType = type
      if (type === 'dim') {
        this.currentDim = row
        this.reNameForm.name = row.dimDirectoryName
      } else {
        this.currentMember = row
        this.reNameForm.name = row.dimName
      }
    },
    // 关闭重命名
    closeReName() {
      this.$refs.reNameForm.resetFields()
      this.reNameVisible = false
    },
    // 重命名
    handleReName() {
      this.$refs['reNameForm'].validate((valid) => {
        if (valid) {
          if (this.reNameType === 'dim') {
            const { id, parentId } = this.currentDim
            const params = {
              id,
              parentId,
              dimDirectoryName: this.reNameForm.name
            }
            updateDim(params).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.reNameVisible = false
                this.getDimensionList(res?.data?.id)
              }
            })
          } else {
            const { id, parentId } = this.currentMember
            const params = {
              id,
              parentId,
              dimDirectoryId: this.currentDim.id,
              dimName: this.reNameForm.name
            }
            updateMember(params).then(res => {
              if (res.code === 200) {
                this.$message.success('修改成功')
                this.reNameVisible = false
                this.getMemberList(res?.data?.id)
              }
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import  '@/styles/variables';
.dimension-set {
  display: flex;
  padding-top: 12px;
  .left {
    flex: 2;
    margin-right: 15px;
  }
  .right {
    flex: 3;
  }
}
.dimension-title {
  display: flex;
  height: 34px;
  align-items: center;
  font-size: 14px;
  color: #606266;
}
.table-node {
  color: $primary;
  cursor: pointer;
  i {
    margin-right: 2px;
  }
}
.custom-node {
  .vxe-icon-table {
    height: 22px;
    line-height: 24px;
  }
}

.dim-tree {
  ::v-deep .el-tree {
    height: 350px;
    overflow-y: auto;
  }
  .top-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f6f7f8;
    padding: 5px 10px;
    margin-bottom: 15px;
    ::v-deep .el-checkbox__label {
      font-size: 12px;
      padding-left: 3px;
      padding-right: 15px;
    }
  }
}
</style>
