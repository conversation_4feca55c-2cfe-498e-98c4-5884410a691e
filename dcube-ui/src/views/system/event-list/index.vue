<template>
  <div class="app-container event-list">
    <div class="main-area">
      <div class="main-btn">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="openEvent('add')"
        >新增</el-button>
      </div>
      <vxe-table
        ref="dimensionRef"
        height="auto"
        class="event-table"
        size="mini"
        :loading="loading"
        show-overflow
        :data="eventList"
        :row-config="{isCurrent: true, isHover: true}"
      >
        <vxe-column field="jobName" title="事件名称">
          <template #default="{ row }">
            <div class="custom-node" @click="openProcess(row)">{{ row.jobName }}</div>
          </template>
        </vxe-column>
        <vxe-column title="应用启动时触发">
          <template #default="{ row }">
            <div>{{ row.triggerFrequency === '0'?'否':'是' }}</div>
          </template>
        </vxe-column>
        <vxe-column field="executeStatus" title="执行状态" />
        <vxe-column field="lastTriggerTime" title="上一次触发时间" />
        <vxe-column field="nextTriggerTime" title="下一次触发时间" />
        <vxe-column title="操作" width="400">
          <template #default="{ row }">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-thumb"
              @click="execFun(row)"
            >手动执行</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-video-play"
              @click="changeStatus(row)"
            >{{ row.status === '0'?'暂停' :'恢复' }}</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="checkLog(row)"
            >查看日志</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="openEvent('update',row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="delEvent(row)"
            >删除</el-button>
          </template>
        </vxe-column>
        <template #empty>
          <table-empty />
        </template>
      </vxe-table>

      <el-pagination
        :current-page="pageInfo.pageNum"
        :page-sizes="[20,50,100]"
        :page-size="pageInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 新增事件 -->
    <el-dialog :title="eventTitle" :visible.sync="dialogVisible" width="700px" :close-on-click-modal="false" @close="closeEvent">
      <el-form ref="eventForm" class="event-form" label-position="right" label-width="120px" size="small" :model="eventForm" :rules="eventRules">
        <el-form-item label="事件名称" prop="jobName">
          <el-input v-model="eventForm.jobName" />
        </el-form-item>
        <el-form-item label="应用启动时触发" prop="triggerFrequency">
          <el-switch
            v-model="eventForm.triggerFrequency"
            active-value="1"
            inactive-value="0"
            size="mini"
          />
        </el-form-item>
        <template v-if="eventForm.triggerFrequency==='0'">
          <el-form-item label="触发时间" prop="cronExpression">
            <vcrontab :expression="eventForm.cronExpression" @fill="saveEvent" />
          </el-form-item>
          <div class="cancel-btn">
            <el-button size="mini" @click="closeEvent">取消</el-button>
          </div>
        </template>
        <template v-else>
          <div class="save-btn">
            <el-button type="primary" size="mini" @click="saveEvent">确定</el-button>
          </div>
        </template>
      </el-form>
    </el-dialog>
    <!-- 进程列表 -->
    <el-dialog title="进程列表" :visible.sync="processDialog" width="1100px" :close-on-click-modal="false" @close="closeProcess">
      <div class="main-btn">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="openAddProcess('add')"
        >新增</el-button>
      </div>
      <vxe-table
        height="500px"
        class="process-table"
        size="mini"
        show-overflow
        :data="processList"
        :row-config="{isCurrent: true, isHover: true}"
      >
        <vxe-column field="processName" title="进程名称" />
        <vxe-column title="进程类型">
          <template #default="{ row }">
            <div>{{ row.jobType === 'TABLE'?'表格进程':'规则进程' }}</div>
          </template>
        </vxe-column>
        <vxe-column field="tableName" title="表名" width="180" />
        <vxe-column field="jobOperateType" title="表操作">
          <template #default="{ row }">
            <div>{{ formatType(row.jobOperateType) }}</div>
          </template>
        </vxe-column>
        <vxe-column field="progress" title="进度" width="130">
          <template #default="{ row }">
            <el-progress v-if="row.progress" :percentage="formatProgress(row.progress)" size="mini" />
            <div v-else />
          </template>
        </vxe-column>
        <vxe-column field="dataVersionName" title="数据版本" />
        <vxe-column field="dataSource" title="源数据视图">
          <template #default="{ row }">
            <div>{{ formatDataSource(row.dataSource) }}</div>
          </template>
        </vxe-column>
        <vxe-column title="操作" width="150">
          <template #default="{ row }">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit-outline"
              @click="openAddProcess('update',row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="delProcess(row)"
            >删除</el-button>
          </template>
        </vxe-column>
        <template #empty>
          <table-empty />
        </template>
      </vxe-table>
      <el-dialog
        width="600px"
        :title="processTitle"
        :visible.sync="addProcessDialog"
        append-to-body
      >
        <el-form ref="processForm" class="event-form" label-position="right" label-width="80px" size="small" :model="processForm" :rules="processRules">
          <el-form-item label="进程名称" prop="processName">
            <el-input v-model="processForm.processName" />
          </el-form-item>
          <el-form-item label="进程类型" prop="jobType">
            <el-select v-model="processForm.jobType">
              <el-option
                v-for="item in jobTypeArr"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <template v-if="processForm.jobType==='TABLE'">
            <el-form-item label="表名" prop="tableId">
              <el-cascader
                v-model="processForm.tableId"
                size="small"
                placeholder="请选择二维表"
                :options="tableTree"
                :props="formatProps"
                :show-all-levels="true"
              />
            </el-form-item>
            <el-form-item label="表操作" prop="jobOperateType">
              <el-select v-model="processForm.jobOperateType" @change="changeJobOpe">
                <el-option
                  v-for="item in jobOperateTypeArr"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="processForm.jobOperateType==='LOAD_DATA_SOURCE'" label="数据源" prop="dataSource">
              <el-select v-model="processForm.dataSource">
                <el-option
                  v-for="item in originDataArr"
                  :key="item.id"
                  :label="item.viewName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item v-if="processForm.jobOperateType==='BACKUP'" label="数据版本" prop="dataVersionName">
              <el-input v-model="dataVersionName" type="text" />
            </el-form-item> -->
            <div v-if="processForm.jobOperateType==='BACKUP'" class="version-name-area">
              <el-form-item label="版本名称" prop="dataVersionName">
                <el-input ref="backUpInput" v-model="dataVersionName" />
              </el-form-item>
              <el-form-item label="" prop="suffixType">
                <el-select v-model="suffixType" class="w-full" placeholder="请选择">
                  <el-option
                    v-for="item in suffixList"
                    :key="item.code"
                    :label="item.desc"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="isOverwrite">
                <el-checkbox v-model="isOverwrite" true-label="Y" false-label="N" label="自动覆盖同名备份" border />
              </el-form-item>
            </div>
            <el-form-item v-if="processForm.jobOperateType==='LOAD_DATA_VERSION'||processForm.jobOperateType==='DELETE_BACKUP'" label="数据版本" prop="dataVersion">
              <el-select v-model="processForm.dataVersion" class="del-select" placeholder="请选择" @change="selectVersion">
                <el-option
                  v-for="item in versionList"
                  :key="item.id"
                  :label="item.version"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </template>
          <template v-else>
            <vxe-table
              height="300px"
              size="mini"
              :loading="loadRuleList"
              :show-overflow="true"
              :data="ruleList"
              :row-config="{isCurrent: true, isHover: true}"
            >
              <vxe-column :show-overflow="true" field="batchCount" title="批次号" />
              <!-- <vxe-column :show-overflow="true" field="sequence" title="顺序号" /> -->
              <vxe-column :show-overflow="true" field="tableName" title="表名" />
              <vxe-column :show-overflow="true" field="columnName" width="280" title="列名">
                <template #default="{ row }">
                  <el-tooltip class="item" effect="dark" :content="row.columnName" placement="top-start">
                    <div class="custom-ellipsis">{{ row.columnName }}</div>
                  </el-tooltip>
                </template>
              </vxe-column>
              <template #empty>
                <table-empty />
              </template>
            </vxe-table>
          </template>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="closeAddProcess">取 消</el-button>
          <el-button size="mini" type="primary" @click="handleAddProcess">确 定</el-button>
        </div>
      </el-dialog>
    </el-dialog>
    <el-dialog
      width="1000px"
      title="事件执行日志"
      :visible.sync="logDialogVisible"
    >
      <div class="search-area">
        <el-form ref="queryForm" :model="queryLog" size="small" :inline="true">
          <el-form-item label="开始时间" prop="beginTime">
            <el-date-picker
              v-model="queryLog.beginTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="请选择开始时间"
            />
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="queryLog.endTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="请选择结束时间"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <vxe-table
        height="400px"
        size="mini"
        show-overflow="title"
        :data="logList"
        :row-config="{isCurrent: true, isHover: true}"
      >
        <vxe-column field="jobName" title="任务名称" />
        <vxe-column field="jobMessage" width="200px" title="日志信息" />
        <vxe-column field="status" title="执行状态">
          <template #default="{ row }">
            <div>{{ row.status === '0'?'成功':'失败' }}</div>
          </template>
        </vxe-column>
        <vxe-column field="exceptionInfo" title="异常信息" />
        <vxe-column field="startTime" title="开始时间" />
        <vxe-column field="stopTime" title="结束时间" />
        <template #empty>
          <table-empty />
        </template>
      </vxe-table>
      <div class="table-footer">
        <el-pagination
          :current-page="logPageInfo.pageNum"
          :page-sizes="[20,50,100]"
          :page-size="logPageInfo.pageSize"
          layout="total"
          :total="totalLogs"
          @size-change="logSizeChange"
          @current-change="logCurrentChange"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="logDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import vcrontab from 'vcrontab'
import { getViewList } from '@/api/system/data-view'
import { execFun, checkLog, changeStatus, getEventList, getRuleList, addEvent, updateEvent, delEvent, getProcessDetail, addProcess, updateProcess, delProcess } from '@/api/system/event-list'
import { getList, getVersionList, getSuffixType } from '@/api/base-table'

export default {
  components: {
    vcrontab
  },
  data() {
    const dateArr = new Array(31).fill(1).map((item, index) => {
      return {
        code: index,
        name: `${index + 1}日`
      }
    })
    return {
      processTitle: '新增进程',
      queryLog: {
        beginTime: '',
        endTime: ''
      },
      dateArr,
      loading: false,
      pageInfo: {
        pageNum: 1,
        pageSize: 20
      },
      total: 0,
      eventList: [],
      eventTitle: '新增事件',
      dialogVisible: false,
      eventForm: {
        jobName: '',
        triggerFrequency: '0',
        cronExpression: ''
      },
      eventRules: {
        jobName: [
          { required: true, message: '请输入事件名称', trigger: 'blur' }
        ],
        triggerFrequency: [
          { required: true, message: '请选择', trigger: ['blur', 'change'] }
        ]
      },
      processDialog: false,
      processList: [],
      addProcessDialog: false,
      processForm: {
        processName: '',
        jobType: 'TABLE',
        tableId: '',
        jobOperateType: 'LOAD_DATA_SOURCE',
        dataVersion: ''
      },
      processRules: {
        processName: [
          { required: true, message: '请输入进程名称', trigger: 'blur' }
        ],
        jobType: [
          { required: true, message: '请选择进程类型', trigger: ['blur', 'change'] }
        ],
        tableId: [
          { required: true, message: '请选择二维表', trigger: ['blur', 'change'] }
        ],
        jobOperateType: [
          { required: true, message: '请选择表操作类型', trigger: ['blur', 'change'] }
        ]
        // dataVersion: [
        //   { required: true, message: '请选择数据版本', trigger: ['blur', 'change'] }
        // ]
      },
      jobTypeArr: [{
        code: 'TABLE',
        name: '表格进程'
      }, {
        code: 'RULE',
        name: '规则进程'
      }],
      tableTree: [],
      tableArr: [],
      formatProps: { checkStrictly: true, emitPath: false, expandTrigger: 'hover', value: 'id', label: 'tableName' },
      jobOperateTypeArr: [{
        code: 'LOAD_DATA_SOURCE',
        name: '载入源数据'
      }, {
        code: 'LOAD_DATA_VERSION',
        name: '载入备份数据'
      }, {
        code: 'RELEASE_MEMORY',
        name: '释放内存'
      }, {
        code: 'BACKUP',
        name: '备份至数据库'
      }, {
        code: 'GENERATE_DATA',
        name: '生成数据'
      }, {
        code: 'DELETE_BACKUP',
        name: '删除备份'
      }],
      originDataArr: [],
      currentJobId: '',
      currentEventId: '',
      ruleList: [],
      loadRuleList: false,
      logDialogVisible: false,
      logList: [],
      logPageInfo: {
        pageNum: 1,
        pageSize: 20
      },
      totalLogs: 0,
      currentLogId: '',
      versionList: [],
      dataVersionName: '',
      suffixType: null,
      isOverwrite: 'N',
      suffixList: []
    }
  },
  watch: {
    'processForm.jobType'(newVal) {
      if (newVal === 'TABLE') {
        this.processRules = {
          processName: [
            { required: true, message: '请输入进程名称', trigger: 'blur' }
          ],
          jobType: [
            { required: true, message: '请选择进程类型', trigger: ['blur', 'change'] }
          ],
          tableId: [
            { required: true, message: '请选择二维表', trigger: ['blur', 'change'] }
          ],
          jobOperateType: [
            { required: true, message: '请选择表操作类型', trigger: ['blur', 'change'] }
          ]
        }
      } else {
        this.processRules = {
          processName: [
            { required: true, message: '请输入进程名称', trigger: 'blur' }
          ],
          jobType: [
            { required: true, message: '请选择进程类型', trigger: ['blur', 'change'] }
          ]
        }
        this.getRuleList()
      }
    },
    'processForm.tableId'(newVal) {
      if (newVal) {
        this.getVersionList(newVal)
      }
    }
  },
  created() { },
  mounted() {
    this.getEventList()
  },
  methods: {
    changeJobOpe(val) {
      this.processForm.dataVersion = ''
      this.dataVersionName = ''
    },
    selectVersion(val) {
      const target = this.versionList.find(item => item.id === val)
      this.dataVersionName = target.version
    },
    formatProgress(progress) {
      let progressCount = 0
      if (progress && progress.includes('%')) {
        progressCount = Number(progress.split('%')[0])
      }
      return progressCount
    },
    changeStatus(row) {
      const { status, jobId } = row
      changeStatus({ jobId, status: status === '0' ? '1' : '0' }).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.getEventList()
        }
      })
    },
    execFun(row) {
      execFun({ jobId: row.jobId }).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.getEventList()
        }
      })
    },
    handleQuery() {
      this.checkLogList()
    },
    resetQuery() {
      this.queryLog = {
        beginTime: '',
        endTime: ''
      }
      this.checkLogList()
    },
    checkLog(row) {
      this.currentLogId = row.jobId
      this.checkLogList()
    },
    checkLogList() {
      checkLog({ jobId: this.currentLogId, ...this.logPageInfo, ...this.queryLog }).then(res => {
        if (res.code === 200) {
          this.logDialogVisible = true
          this.logList = res.rows
          this.totalLogs = res.total
        }
      })
    },
    getRuleList() {
      this.loadRuleList = true
      getRuleList({ jobId: this.currentJobId }).then(res => {
        const { code, data } = res || {}
        if (code === 200) {
          this.ruleList = data
        }
      }).finally(() => {
        this.loadRuleList = false
      })
    },
    delEvent(row) {
      const { jobId } = row
      this.$confirm('确定删除该事件吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delEvent(jobId).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getEventList()
          }
        })
      })
    },
    handleAddProcess() {
      this.$refs['processForm'].validate((valid) => {
        if (valid) {
          const operateFunc = this.processForm.id ? updateProcess : addProcess
          const tableName = this.tableArr.find(item => item.id === this.processForm.tableId)?.tableName || ''
          const { jobType, processName, jobOperateType } = this.processForm
          const params = jobType === 'RULE' ? { jobId: this.currentJobId, processName, jobType } : { ...this.processForm, tableName, jobId: this.currentJobId, dataVersionName: this.dataVersionName, suffixType: jobOperateType === 'BACKUP' ? this.suffixType : null, isOverwrite: jobOperateType === 'BACKUP' ? this.isOverwrite : null }
          operateFunc(params).then(res => {
            if (res.code === 200) {
              this.$message.success('保存成功')
              this.addProcessDialog = false
              this.getProcessList()
            }
          })
        } else {
          console.log('error submit!!')
        }
      })
    },
    closeAddProcess() {
      this.addProcessDialog = false
    },
    initProcessForm() {
      this.processForm = {
        processName: '',
        jobType: 'TABLE',
        tableId: '',
        jobOperateType: 'LOAD_DATA_SOURCE',
        dataVersion: ''
      }
      this.dataVersionName = ''
      this.suffixType = null
      this.isOverwrite = 'N'
      if (this.$refs.processForm) {
        this.$refs.processForm.clearValidate()
      }
    },
    // 获取数据源
    getOriginData() {
      getViewList({ pageNum: 1, pageSize: 9999 }).then(res => {
        if (res.code === 200) {
          this.originDataArr = res.rows
        }
      })
    },
    // 获取新增进程时二维表
    getTableArr() {
      getList({}).then(res => {
        if (res.code === 200) {
          this.tableArr = res.data.filter(item => item.type === 'TABLE')
          res.data.forEach(item => {
            item.disabled = item.type === 'GROUP'
          })
          this.tableTree = this.listToTree(res.data)
        }
      })
    },
    async  openAddProcess(type, row) {
      this.initProcessForm()
      this.processTitle = type === 'add' ? '新增进程' : '修改进程'
      if (type === 'update') {
        this.processForm = { ...row }
        const { dataVersionName, suffixType, isOverwrite } = row || null
        this.suffixType = suffixType
        this.isOverwrite = isOverwrite
        this.dataVersionName = dataVersionName
      }
      this.addProcessDialog = true
      const res = await getSuffixType()
      if (res.code === 200) {
        this.suffixList = res.data
      }
    },
    openProcess(row) {
      const { jobId } = row
      this.currentJobId = jobId
      this.getOriginData()
      this.getTableArr()
      this.getProcessList()
    },
    getProcessList() {
      getProcessDetail({ jobId: this.currentJobId }).then(res => {
        if (res.code === 200) {
          this.processList = res.data
          this.processDialog = true
        }
      })
    },
    closeProcess() {
      this.processDialog = false
    },
    formatType(type) {
      const target = this.jobOperateTypeArr.find(item => item.code === type)
      return target?.name || ''
    },
    formatDataSource(dataSource) {
      const target = dataSource ? this.originDataArr.find(item => item.id === dataSource) : {}
      return target?.viewName || ''
    },
    // 递归树列表
    listToTree(list) {
      const obj = {}
      list.forEach(item => { obj[item.id] = item })
      const res = []
      list.forEach(item => {
        const parent = obj[item.parentId]
        // 如果parent存在则item是叶子节点，否则就是根节点
        if (parent) {
          parent.children = parent.children || []
          parent.children.push(item)
        } else {
          res.push(item)
        }
      })
      return res
    },
    delProcess(row) {
      this.$confirm('确定删除该进程吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delProcess(row.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getProcessList()
          }
        })
      })
    },
    saveEvent(val) {
      this.$refs['eventForm'].validate((valid) => {
        if (valid) {
          this.eventForm.cronExpression = this.eventForm.triggerFrequency === '1' ? '' : val
          const handleFunc = this.eventForm.jobId ? updateEvent : addEvent
          handleFunc({ ...this.eventForm }).then(res => {
            if (res.code === 200) {
              this.$message.success('保存成功')
              this.dialogVisible = false
              this.getEventList()
            }
          })
        } else {
          console.log('error submit!!')
        }
      })
    },
    getVersionList(id) {
      getVersionList(id).then((res) => {
        if (res.code === 200) {
          this.versionList = res.data
        }
      })
    },
    /** 查询事件列表 */
    getEventList() {
      this.loading = true
      getEventList(this.pageInfo).then(response => {
        const { rows, total } = response
        this.eventList = rows
        this.total = total
      }).finally(() => {
        this.loading = false
      })
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.getEventList()
    },
    handleCurrentChange(val) {
      this.pageInfo.pageNum = val
      this.getEventList()
    },
    logSizeChange(val) {
      this.logPageInfo.pageSize = val
      this.checkLogList()
    },
    logCurrentChange(val) {
      this.logPageInfo.pageNum = val
      this.checkLogList()
    },
    initEventForm() {
      if (this.$refs.eventForm) {
        this.$refs.eventForm.resetFields()
      }
    },
    closeEvent() {
      this.$refs.eventForm.resetFields()
      this.dialogVisible = false
    },
    openEvent(type, row) {
      this.initEventForm()
      // this.currentEventId = type === 'add' ? '' : row.jobId
      this.eventTitle = type === 'add' ? '新增事件' : '修改事件'
      if (type === 'update') {
        const { jobName, cronExpression, triggerFrequency, jobId } = row
        this.eventForm = { jobName, cronExpression, triggerFrequency, jobId }
      }
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.event-list {
  padding-top: 12px;
  padding-bottom: 0 !important;
  .main-area {
    width: 100%;
  }
}
.main-btn {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
.event-table {
  ::v-deep .vxe-table--render-wrapper {
    height: calc(100vh - 200px);
  }
}
.event-form {
  position: relative;
  .el-select,.el-date-editor,.el-cascader {
    width: 100%;
  }
  .vxe-input {
    width: 100%;
    height: 32px;
    line-height: 32px;
  }
  ::v-deep.popup-main {
    // .popup-result:nth-child(2) {
    //   display: none;
    // }
    .pop_btn {
      text-align: right;
      padding-right: 75px;
      .el-button--warning,.el-button--default {
        display: none;
      }
    }
  }
}
.cancel-btn {
  position: absolute;
  bottom: 10px;
  right: 0px;
}
.custom-node {
  cursor: pointer;
  color:#004DE7;
}
.save-btn {
  text-align: right;
}
.table-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.version-name-area {
  display: flex;
  ::v-deep .el-form-item__content {
    margin-left: 3px !important;
  }
  ::v-deep .el-form-item:first-of-type {
    .el-form-item__content {
      margin-left: 80px !important;
    }
  }
}
</style>
