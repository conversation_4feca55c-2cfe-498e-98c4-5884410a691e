package com.dcube.web.controller.common;

import cn.hutool.core.io.FileUtil;
import com.dcube.common.config.DCubeConfig;
import com.dcube.common.constant.Constants;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.exception.file.InvalidExtensionException;
import com.dcube.common.utils.StringUtils;
import com.dcube.common.utils.file.FileUploadUtils;
import com.dcube.common.utils.file.FileUtils;
import com.dcube.framework.config.ServerConfig;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@Tag(name = "通用功能")
@RestController
@RequestMapping("/common")
public class CommonController {
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    private static final String FILE_DELIMETER = ",";

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @Operation(summary = "文件下载")
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.checkAllowDownload(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = DCubeConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求（单个）
     */
    @Operation(summary = "文件上传")
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            // 上传文件路径
            String filePath = DCubeConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

//    /**
//     * 通用上传请求（多个）
//     */
//    @Operation(summary = "文件批量上传")
//    @PostMapping("/uploads")
//    public AjaxResult uploadFiles(List<MultipartFile> files) {
//        try {
//            // 上传文件路径
//            String filePath = DCubeConfig.getUploadPath();
//            List<String> urls = new ArrayList<String>();
//            List<String> fileNames = new ArrayList<String>();
//            List<String> newFileNames = new ArrayList<String>();
//            List<String> originalFilenames = new ArrayList<String>();
//            for (MultipartFile file : files) {
//                // 上传并返回新文件名称
//                String fileName = FileUploadUtils.upload(filePath, file);
//                String url = serverConfig.getUrl() + fileName;
//                urls.add(url);
//                fileNames.add(fileName);
//                newFileNames.add(FileUtils.getName(fileName));
//                originalFilenames.add(file.getOriginalFilename());
//            }
//            AjaxResult ajax = AjaxResult.success();
//            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
//            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
//            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
//            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
//            return ajax;
//        } catch (Exception e) {
//            return AjaxResult.error(e.getMessage());
//        }
//    }

    /**
     * 本地资源通用下载
     */
    @Operation(summary = "本地资源通用下载")
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response) {
        try {
            if (!FileUtils.checkAllowDownload(resource)) {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = DCubeConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }


    @Operation(summary = "文件分片上传")
    @PostMapping("/uploadSliceFile")
    public AjaxResult uploadSliceFile(@RequestParam("sFile") MultipartFile sFile,
                                      @RequestParam("realFilename") String realFilename,
                                      @RequestParam("index") Integer index,
                                      @RequestParam("timestamp") String timestamp) throws IOException, InvalidExtensionException {
        FileUploadUtils.validateUploadFile(sFile, false);
        // 校验文件合法
        FileUploadUtils.validateUploadFile(sFile, FileUploadUtils.FileType.ZIP);

        String md5 = DigestUtils.md5Hex(realFilename);
        log.info(realFilename);
        log.info(md5);
        log.info("分片名: {}", sFile.getOriginalFilename());

        String filePath = DCubeConfig.getUploadPath() + "/" + timestamp + "/" + md5;
        Path targetDir = Paths.get(filePath);
        if (!Files.exists(targetDir)) {
            Files.createDirectories(targetDir);
        }
        Path absPath = targetDir.resolve(String.valueOf(index));
        // 写入absPath
        sFile.transferTo(absPath);
        return AjaxResult.success();
    }

    @Operation(summary = "合并文件")
    @PostMapping("/mergeFile")
    public AjaxResult mergeFile(@RequestParam("realFilename") String realFilename, @RequestParam("timestamp") String timestamp) throws IOException, InterruptedException {
        log.info("-------开始合并文件");
        // 合并的文件
        String fileName = DCubeConfig.getUploadPath() + "/" + timestamp + "/" + realFilename;
        try (RandomAccessFile raf = new RandomAccessFile(fileName, "rw")) {
            // 获取分片所在文件夹
            String md5 = DigestUtils.md5Hex(realFilename);
            log.info(realFilename);
            log.info(md5);
            File file = new File(DCubeConfig.getUploadPath() + "/" + timestamp + "/" + md5);
            File[] files = file.listFiles();
            int num = files.length;
            log.info("" + num);
            byte[] bytes = new byte[5 * 1024];
            // 合并分片
            for (int i = 0; i < num; i++) {
                File iFile = new File(file, String.valueOf(i));
                // 将每一个分片文件包装为缓冲流
                try (FileInputStream fis = new FileInputStream(iFile);
                     BufferedInputStream bis = new BufferedInputStream(fis)) {
                    int len = 0;
                    // 将分片文件包装的流写入RandomAccessFile
                    while ((len = bis.read(bytes)) != -1) {
                        raf.write(bytes, 0, len);
                    }
                }
            }
            // 删除分片所在文件夹的分片文件
            for (File tmpFile : files) {
                tmpFile.delete();
            }
            //删除分片所在文件夹
            file.delete();
        }
        return AjaxResult.success("文件合并成功", fileName);
    }

    @Operation(summary = "分片下载")
    @GetMapping("/spiltDownload")
    public void spiltDownload(String filePath, HttpServletRequest request, HttpServletResponse response) {
        //解决前端接受自定义heards
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition,Accept-Range,fSize,fName,Content-Range,Content-Lenght,responseType");
        File file = new File(filePath);
        //设置编码
        response.setCharacterEncoding("utf-8");
        InputStream is = null;
        OutputStream os = null;
        FileInputStream fis = null;
        try {
            //分片下载
            long fSize = file.length();
            response.setHeader("responseType", "blob");
            //前段识别下载
            response.setContentType("application/x-download");
            //response.setContentType("application/octet-stream");
            //文件名
            String fileName = URLEncoder.encode(FileUtil.getName(filePath), "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" +
                    fileName);
            //http Range 告诉前段支持分片下载
            response.setHeader("Accept-Range", "bytes");
            //告诉前段文件大小 文件名字
            response.setHeader("fSize", String.valueOf(fSize));
            response.setHeader("fName", fileName);
            //起始位置 结束位置 读取了多少
            long pos = 0, last = fSize - 1, sum = 0;
            //需不需要分片下载
            if (null != request.getHeader("Range")) {
                //支持分片下载 206
                response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
                //bytes=10-100
                String numRange = request.getHeader("Range").replaceAll("bytes=", "");
                String[] strRange = numRange.split("-");
                //取起始位置
                if (strRange.length >= 2) {
                    pos = Long.parseLong(strRange[0].trim());
                    last = Long.parseLong(strRange[1].trim());
                    if (last > fSize - 1) {
                        last = fSize - 1;
                    }
                } else {
                    pos = Long.parseLong(numRange.replaceAll("-", "").trim());
                }
            }
            //读多少
            long rangeLenght = last - pos + 1;
            //告诉前端有多少分片
            String contentRange = "bytes " + pos + "-" + last + "/" + fSize;
            //规范告诉文件大小
            response.setHeader("Content-Range", contentRange);
            response.setHeader("Content-Lenght", String.valueOf(rangeLenght));
            os = new BufferedOutputStream(response.getOutputStream());
            fis = new FileInputStream(file);
            is = new BufferedInputStream(fis);
            //跳过已读
            is.skip(pos);
            byte[] buffer = new byte[1024 * 1024 * 100];
            int lenght = 0;
            while (sum < rangeLenght) {
                lenght = is.read(buffer, 0, (rangeLenght - sum) <= buffer.length ? ((int) (rangeLenght - sum)) : buffer.length);
                sum = sum + lenght;
                os.write(buffer, 0, lenght);
            }
        } catch (Exception e) {
            log.info("", e);
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(os);
            IOUtils.closeQuietly(fis);
        }
    }

}
