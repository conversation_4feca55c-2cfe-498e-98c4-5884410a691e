package com.dcube.web.controller.system;

import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.domain.entity.PermissionDimInstance;
import com.dcube.common.enums.DataEditionEnum;
import com.dcube.common.enums.MasterDataScopeEnum;
import com.dcube.system.service.IPermissionDimService;
import com.dcube.system.vo.RolePermissionDimInstanceVo;
import com.dcube.system.vo.RolePermissionDimVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("permission_dim")
@Tag(name = "DCUBE-数方权限", description = "DCUBE-数方权限")
public class PermissionDimController extends BaseController {

    @Autowired
    private IPermissionDimService permissionDimService;

    @GetMapping("role_dim_list")
    @Operation(summary = "全部数方权限列表")
    public AjaxResult roleDimList(@RequestParam("roleId") String roleId) {
        return AjaxResult.success(permissionDimService.selectRoleDimList(roleId));
    }

    @GetMapping("role_dim_instance_list")
    @Operation(summary = "数方权限关联的维度列表(包含主数据维度的数据范围)")
    public AjaxResult roleDimInstanceList(@RequestParam("roleId") String roleId, @RequestParam("tableId") String tableId) {
        return AjaxResult.success(permissionDimService.selectRoleDimInstanceList(roleId, tableId));
    }

    @GetMapping("role_dim_has_instance_list")
    @Operation(summary = "已选的维度成员列表")
    public AjaxResult roleDimHasInstanceList(@RequestParam("roleId") String roleId, @RequestParam("tableId") String tableId, @RequestParam("dimDirectoryId") int dimDirectoryId) {
        return AjaxResult.success(permissionDimService.selectRoleDimHasInstanceList(roleId, tableId, dimDirectoryId));
    }

    @GetMapping("predicate_option")
    @Operation(summary = "断言下拉选项（有|无）")
    public AjaxResult predicateOption() {
        return AjaxResult.success(DataEditionEnum.toList());
    }

    @GetMapping("master_option")
    @Operation(summary = "主数据权限下拉选项")
    public AjaxResult masterOption() {
        return AjaxResult.success(MasterDataScopeEnum.toList());
    }

    @PostMapping("save")
    @Operation(summary = "保存数方权限（包含修改）")
    public AjaxResult save(@RequestBody RolePermissionDimVo rolePermissionDimVo) {
        permissionDimService.saveOrUpdate(rolePermissionDimVo);
        return AjaxResult.success();
    }

    @PostMapping("save_master_data_scope")
    @Operation(summary = "保存主数据维度数据范围（包含修改）")
    public AjaxResult masterDataScope(@RequestBody PermissionDimInstance permissionDimInstance) {
        permissionDimService.saveMasterDataScope(permissionDimInstance);
        return AjaxResult.success();
    }

    @PostMapping("save_dim_instance")
    @Operation(summary = "保存已选的维度成员列表（包含修改）")
    public AjaxResult saveDimInstance(@RequestBody RolePermissionDimInstanceVo rolePermissionDimInstanceVo) {
        permissionDimService.saveDimInstance(rolePermissionDimInstanceVo.getRoleId(), rolePermissionDimInstanceVo.getTableId(), rolePermissionDimInstanceVo.getDimDirectoryId(), rolePermissionDimInstanceVo.getInstanceIdList());
        return AjaxResult.success();
    }
}
