package com.dcube.web.controller.system;

import com.dcube.common.annotation.Log;
import com.dcube.common.constant.UserConstants;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.page.TableDataInfo;
import com.dcube.common.enums.BusinessType;
import com.dcube.common.utils.poi.ExcelUtil;
import com.dcube.system.domain.SysConfig;
import com.dcube.system.service.ISysConfigService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 参数配置 信息操作处理
 * 
 * <AUTHOR>
 */
@Tag(name = "系统设置")
@RestController
@RequestMapping("/system/config")
public class SysConfigController extends BaseController
{
    @Autowired
    private ISysConfigService configService;

    /**
     * 获取参数配置列表
     */
    @Operation(summary = "系统设置列表")
    @PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysConfig config)
    {
        startPage();
        List<SysConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    @Operation(summary = "导出系统设置")
    @Log(title = "系统设置", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:config:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysConfig config)
    {
        List<SysConfig> list = configService.selectConfigList(config);
        ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
        util.exportExcel(response, list, "参数数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @Operation(summary = "查看系统设置")
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable Long configId)
    {
        return success(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @Operation(summary = "根据设置key获取系统设置")
    @GetMapping(value = "/configKey/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey)
    {
        return success(configService.selectConfigByKey(configKey));
    }

    /**
     * 新增参数配置
     */
    @Operation(summary = "新增系统设置")
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "系统设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysConfig config)
    {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config)))
        {
            return error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setCreateBy(getUsername());
        return toAjax(configService.insertConfig(config));
    }

    /**
     * 修改参数配置
     */
    @Operation(summary = "修改系统设置")
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "系统设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysConfig config)
    {
        if (UserConstants.NOT_UNIQUE.equals(configService.checkConfigKeyUnique(config)))
        {
            return error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setUpdateBy(getUsername());
        return toAjax(configService.updateConfig(config));
    }

    /**
     * 删除参数配置
     */
    @Operation(summary = "删除系统设置")
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "系统设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        configService.deleteConfigByIds(configIds);
        return success();
    }

    /**
     * 刷新参数缓存
     */
    @Operation(summary = "刷新系统设置")
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "系统设置", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        configService.resetConfigCache();
        return success();
    }

    /**
     * 更新系统设置
     */
    @Operation(summary = "更新系统设置")
//    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "系统设置", businessType = BusinessType.UPDATE)
    @PostMapping("/updateConfigValueByKey")
    public AjaxResult updateConfigValueByKey(@Parameter(description = "配置key") @RequestParam("key") String key, @Parameter(description = "配置val") @RequestParam("val") String val) {
        configService.updateConfigValueByKey(key, val);
        return success();
    }


}
