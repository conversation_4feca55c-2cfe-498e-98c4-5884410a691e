package com.dcube.web.controller.system;

import com.dcube.common.annotation.Anonymous;
import com.dcube.common.config.DCubeConfig;
import com.dcube.common.core.domain.AjaxResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "系统信息")
@RestController
@RequestMapping("/system/info")
@Anonymous
public class SysInfoController {
    /** 系统基础配置 */
    @Autowired
    private DCubeConfig dCubeConfig;

    /**
     * 系统版本
     */
    @Operation(summary = "系统版本")
    @GetMapping("/version")
    public AjaxResult index() {
        return AjaxResult.success(dCubeConfig.getVersion());
    }
}
