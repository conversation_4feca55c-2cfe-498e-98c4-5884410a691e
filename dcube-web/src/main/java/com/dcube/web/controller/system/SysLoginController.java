package com.dcube.web.controller.system;

import cn.hutool.crypto.asymmetric.KeyType;
import com.dcube.common.constant.Constants;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.core.domain.entity.Permission;
import com.dcube.common.core.domain.entity.SysMenu;
import com.dcube.common.core.domain.entity.SysUser;
import com.dcube.common.core.domain.model.LoginBody;
import com.dcube.common.utils.SecurityUtils;
import com.dcube.common.utils.CryptoUtil;
import com.dcube.framework.web.service.SysLoginService;
import com.dcube.framework.web.service.SysPermissionService;
import com.dcube.system.service.IPermissionService;
import com.dcube.system.service.ISysMenuService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Tag(name = "登录验证")
@RestController
public class SysLoginController {


    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService sysPermissionService;

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private CryptoUtil cryptoUtil;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        //license认证
        loginService.licenseCheck();
        // 生成令牌
        String token = loginService.login(cryptoUtil.decryptStr(loginBody.getUsername(), KeyType.PrivateKey), cryptoUtil.decryptStr(loginBody.getPassword(), KeyType.PrivateKey), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @Operation(summary = "获取用户信息")
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = sysPermissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = sysPermissionService.getMenuPermission(user);
        // 表格权限列表
        List<Permission> tablePermissions = permissionService.selectUserTableList(user.getUserId(), user.getUserName());
        AjaxResult ajax = AjaxResult.success(user, roles, permissions, tablePermissions);
//        ajax.put("user", user);
//        ajax.put("roles", roles);
//        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @Operation(summary = "获取路由信息")
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
