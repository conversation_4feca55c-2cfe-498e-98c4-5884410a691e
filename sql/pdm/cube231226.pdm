<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ID="{88086B01-C9E1-11D4-9552-0090277716A9}" Label="" LastModificationDate="1703234671" Name="Physical Data Model 1" Objects="288" Symbols="28" Target="MySQL 5.0" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.3.0.3248"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>88086B01-C9E1-11D4-9552-0090277716A9</a:ObjectID>
<a:Name>Physical Data Model 1</a:Name>
<a:Code>PHYSICAL_DATA_MODEL_1</a:Code>
<a:CreationDate>0</a:CreationDate>
<a:Creator/>
<a:ModificationDate>1703234627</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas.sql
GenScriptName0=crebas.sql
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=C:\Users\<USER>\Desktop\
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=No
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=No
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=No
Comment=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=No
Drop=No
Comment=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=No
Foreign key=Yes
Alternate key=No
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
EstimationYears=0
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes

[FolderOptions\CheckModel]

[FolderOptions\CheckModel\Package]

[FolderOptions\CheckModel\Package\CircularReference]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ConstraintName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CnstMaxLen]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CircularDependency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ShortcutUniqCode]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Table]

[FolderOptions\CheckModel\Table\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqIndex]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - INDXCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - KEYCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyCollYesYes]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\TableIndexes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Table\CheckTablePartitionKey]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableStartDate]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableRefNoLifecycle]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableSourceMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTablePartialColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableKeyColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableNotOnLifecycleTablespace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MYSQL50_Table_Table_storage_type]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column]

[FolderOptions\CheckModel\Table.Column\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DomainDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnMandatory]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyDttpDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyCheckDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncNoKey]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncDttp]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\SerialColumnFK]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnCompExpr]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnOneToOneMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnDataTypeMapping]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnNoMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Auto_increment_key]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Datatype_attributes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index]

[FolderOptions\CheckModel\Table.Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UndefIndexType]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IQIndxHNGUniq]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MYSQL50_Index_Fulltext_indexes_validity]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key]

[FolderOptions\CheckModel\Table.Key\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MultiKeySqnc]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger]

[FolderOptions\CheckModel\Table.Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index]

[FolderOptions\CheckModel\Join Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View]

[FolderOptions\CheckModel\View\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\View.View Index]

[FolderOptions\CheckModel\View.View Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference]

[FolderOptions\CheckModel\Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\Reflexive]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\EmptyColl - RFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\IncompleteJoin]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\JoinOrder]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference]

[FolderOptions\CheckModel\View Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\EmptyColl - VRFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain]

[FolderOptions\CheckModel\Domain\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default]

[FolderOptions\CheckModel\Default\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltValeEmpty]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltSameVale]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User]

[FolderOptions\CheckModel\User\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Group]

[FolderOptions\CheckModel\Group\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Role]

[FolderOptions\CheckModel\Role\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure]

[FolderOptions\CheckModel\Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\ProcBodyEmpty]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\DBMS Trigger]

[FolderOptions\CheckModel\DBMS Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DbmsTriggerEvent]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source]

[FolderOptions\CheckModel\Data Source\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\EmptyColl - MODLSRC]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DtscTargets]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckDataSourceModels]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning]

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning]

[FolderOptions\CheckModel\Vertical Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing]

[FolderOptions\CheckModel\Table Collapsing\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\EmptyColl - TargetTable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube]

[FolderOptions\CheckModel\Cube\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\EmptyColl - ALLOLINKCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\EmptyColl - Facts]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\CubeDupAssociation]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact]

[FolderOptions\CheckModel\Fact\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\EmptyColl - MEASCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension]

[FolderOptions\CheckModel\Dimension\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - HIERCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDupHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDefHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association]

[FolderOptions\CheckModel\Association\EmptyColl - Hierarchy]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute]

[FolderOptions\CheckModel\Dimension.Attribute\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure]

[FolderOptions\CheckModel\Fact.Measure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy]

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym]

[FolderOptions\CheckModel\Synonym\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\EmptyColl - BASEOBJ]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type]

[FolderOptions\CheckModel\Abstract Data Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtInstantiable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtAbstractUsed]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure]

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\AdtProcUniqName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package]

[FolderOptions\CheckModel\Database Package\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - PROCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - CURCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - VARCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - TYPCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - EXCCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Procedure]

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence]

[FolderOptions\CheckModel\Sequence\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor]

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Variable]

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type]

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception]

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace]

[FolderOptions\CheckModel\Tablespace\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage]

[FolderOptions\CheckModel\Storage\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database]

[FolderOptions\CheckModel\Database\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service]

[FolderOptions\CheckModel\Web Service\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation]

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle]

[FolderOptions\CheckModel\Lifecycle\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecyclePhase]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecycleRetention]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckPartitionRange]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase]

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIQTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDuplicateTbspace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspaceCurrency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseRetention]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIdlePeriod]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDataSource]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseExternalOnFirst]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Replication]

[FolderOptions\CheckModel\Replication\PartialReplication]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule]

[FolderOptions\CheckModel\Business Rule\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\EmptyColl - OBJCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object]

[FolderOptions\CheckModel\Extended Object\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link]

[FolderOptions\CheckModel\Extended Link\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File]

[FolderOptions\CheckModel\File\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckPathExists]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format]

[FolderOptions\CheckModel\Data Format\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\CheckDataFormatNullExpression]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=&lt;Undefined&gt;
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=1
RefrUpdateConstraint=1
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<a:ExtendedAttributesText>{B2B54B83-1563-48B2-8C35-1C370B4B3DB3},SYASA12,75={F651E222-A195-471F-959B-1F216FBE9D98},AutoFixMaterializedViewDone,4=true

</a:ExtendedAttributesText>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>7868D172-C7ED-4133-B98D-0AF0A37C6D68</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1670899889</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1670899889</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>D2EE60C5-B778-449A-9005-E63439863306</a:ObjectID>
<a:Name>Diagram 1</a:Name>
<a:Code>DIAGRAM_1</a:Code>
<a:CreationDate>0</a:CreationDate>
<a:Creator/>
<a:ModificationDate>1703234627</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Show Icon=No
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;#$%&amp;&#39;)*+,-./:;=&gt;?@\]^_`|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject.TextStyle=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.TextStyle=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Package.Stereotype=Yes
Package.Comment=No
Package.IconPicture=No
Package.TextStyle=No
Package_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table.TextStyle=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View.TextStyle=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure.TextStyle=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:ReferenceSymbol Id="o5">
<a:CreationDate>1668479322</a:CreationDate>
<a:ModificationDate>1675758178</a:ModificationDate>
<a:Rect>((-40524,-10359), (-40074,7322))</a:Rect>
<a:ListOfPoints>((-40299,-10359),(-40299,7322))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o6"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o7"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o8"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o9">
<a:CreationDate>1675758486</a:CreationDate>
<a:ModificationDate>1675758486</a:ModificationDate>
<a:Rect>((-39108,-7093), (-19420,-6643))</a:Rect>
<a:ListOfPoints>((-19420,-6868),(-39108,-6868))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o10"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o6"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o11"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o12">
<a:CreationDate>1676603559</a:CreationDate>
<a:ModificationDate>1676603646</a:ModificationDate>
<a:Rect>((-64694,-14174), (-64244,-2850))</a:Rect>
<a:ListOfPoints>((-64469,-14174),(-64469,-2850))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o13"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o14"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o15"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o16">
<a:CreationDate>1676603574</a:CreationDate>
<a:ModificationDate>1676603648</a:ModificationDate>
<a:Rect>((-64484,-26830), (-64034,-17626))</a:Rect>
<a:ListOfPoints>((-64259,-26830),(-64259,-17626))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o17"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o13"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o18"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o19">
<a:CreationDate>1676603627</a:CreationDate>
<a:ModificationDate>1676603627</a:ModificationDate>
<a:Rect>((-89699,-3036), (-64676,-2586))</a:Rect>
<a:ListOfPoints>((-64676,-2811),(-89699,-2811))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o14"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o20"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o21"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o22">
<a:CreationDate>1676603646</a:CreationDate>
<a:ModificationDate>1676603646</a:ModificationDate>
<a:Rect>((-87040,-19177), (-65758,-18727))</a:Rect>
<a:ListOfPoints>((-65758,-18952),(-87040,-18952))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o13"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o23"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o24"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o25">
<a:CreationDate>1676603648</a:CreationDate>
<a:ModificationDate>1676603648</a:ModificationDate>
<a:Rect>((-85471,-26887), (-67871,-26437))</a:Rect>
<a:ListOfPoints>((-67871,-26662),(-85471,-26662))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o17"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o23"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o26"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o27">
<a:CreationDate>1703219073</a:CreationDate>
<a:ModificationDate>1703234582</a:ModificationDate>
<a:Rect>((-42876,-47700), (-42426,-34425))</a:Rect>
<a:ListOfPoints>((-42651,-47700),(-42651,-34425))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o28"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o29"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o30"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o31">
<a:CreationDate>1703234587</a:CreationDate>
<a:ModificationDate>1703234627</a:ModificationDate>
<a:Rect>((-19280,-48450), (-18828,-35925))</a:Rect>
<a:ListOfPoints>((-19065,-35925),(-19044,-48450))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o32"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o33"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o34"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o35">
<a:CreationDate>1703234625</a:CreationDate>
<a:ModificationDate>1703234627</a:ModificationDate>
<a:Rect>((-39000,-35963), (-19613,-35513))</a:Rect>
<a:ListOfPoints>((-19613,-35738),(-39000,-35738))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o32"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o29"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o36"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o37">
<a:CreationDate>1703234627</a:CreationDate>
<a:ModificationDate>1703234627</a:ModificationDate>
<a:Rect>((-16240,-36127), (-3225,-35677))</a:Rect>
<a:ListOfPoints>((-16240,-35902),(-3225,-35902))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o32"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o38"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o39"/>
</c:Object>
</o:ReferenceSymbol>
<o:TableSymbol Id="o7">
<a:CreationDate>1668409596</a:CreationDate>
<a:ModificationDate>1675757525</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-45465,4313), (-34935,12337))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o40"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o6">
<a:CreationDate>1668409821</a:CreationDate>
<a:ModificationDate>1675758178</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-46097,-15995), (-33635,1103))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o41"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o10">
<a:CreationDate>1675758315</a:CreationDate>
<a:ModificationDate>1675758486</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-22351,-11235), (-11821,-2387))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o42"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o14">
<a:CreationDate>1676600835</a:CreationDate>
<a:ModificationDate>1676603627</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-69251,-7162), (-57949,862))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o43"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o13">
<a:CreationDate>1676601116</a:CreationDate>
<a:ModificationDate>1676603646</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-69669,-20550), (-57981,-10050))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o44"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o17">
<a:CreationDate>1676601636</a:CreationDate>
<a:ModificationDate>1676603648</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-69937,-36337), (-57863,-21713))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o45"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o20">
<a:CreationDate>1676602099</a:CreationDate>
<a:ModificationDate>1676603700</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-93313,-6225), (-85487,-675))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>8421631</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o46"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o23">
<a:CreationDate>1676602485</a:CreationDate>
<a:ModificationDate>1676603707</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-94421,-28696), (-83505,-16548))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>8421631</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o47"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o48">
<a:CreationDate>1676603778</a:CreationDate>
<a:ModificationDate>1676604144</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-69941,-45909), (-57093,-39535))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o49"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o50">
<a:CreationDate>1687930940</a:CreationDate>
<a:ModificationDate>1687930940</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-51390,-29287), (-40860,-22913))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o51"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o28">
<a:CreationDate>1689925981</a:CreationDate>
<a:ModificationDate>1703219073</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-50342,-51375), (-37108,-44175))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o52"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o29">
<a:CreationDate>1689926468</a:CreationDate>
<a:ModificationDate>1703234582</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-50460,-38512), (-36840,-32138))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o53"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o38">
<a:CreationDate>1703234180</a:CreationDate>
<a:ModificationDate>1703234583</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-6124,-38100), (6724,-32550))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o54"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o33">
<a:CreationDate>1703234202</a:CreationDate>
<a:ModificationDate>1703234210</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-24295,-58162), (-12605,-46838))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o55"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o32">
<a:CreationDate>1703234240</a:CreationDate>
<a:ModificationDate>1703234627</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-24606,-37987), (-12144,-33263))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o56"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o40">
<a:ObjectID>AB41C4CE-29DF-4524-B2C2-B41CE6CFFBD1</a:ObjectID>
<a:Name>分组表</a:Name>
<a:Code>cube_group</a:Code>
<a:CreationDate>1668409596</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758587</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>分组表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o57">
<a:ObjectID>F0FBABE6-C3E2-4205-8DD5-18F6037A0A75</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1675758208</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758219</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o58">
<a:ObjectID>E76A4881-1E31-4FC2-8735-A4AAFB14778D</a:ObjectID>
<a:Name>分组代码</a:Name>
<a:Code>group_code</a:Code>
<a:CreationDate>1668411642</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>分组代码</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>64EFC210-72EE-40D7-8D72-9A3DDC7B43A7</a:ObjectID>
<a:Name>分组名称</a:Name>
<a:Code>group_name</a:Code>
<a:CreationDate>1668411642</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>分组名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>EA979B8C-A386-49F4-A956-E549E8DB57A1</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1668411642</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668479670</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>状态</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>AC1C46D1-119B-4EA1-9016-633CF42D1142</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_by</a:Code>
<a:CreationDate>1668409596</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668409596</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>3B2FC7F5-47F7-4279-B51E-7221F618CEFA</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1668409596</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668409596</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>24C4A37B-6D22-4EC3-BCA1-DB7A077AE176</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_by</a:Code>
<a:CreationDate>1668409596</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668409596</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o64">
<a:ObjectID>177C26E9-0C83-461B-AB2B-C487435C0FA6</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1668409596</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668409596</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o65">
<a:ObjectID>0CC4080D-3C41-4C48-9319-848CD41C6EDD</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1668409596</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758219</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o57"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o65"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o41">
<a:ObjectID>6ADB6937-544A-4A91-B204-952252D82B2E</a:ObjectID>
<a:Name>分组实例表</a:Name>
<a:Code>cube_group_instance</a:Code>
<a:CreationDate>1668409821</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1681366999</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>分组实例表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o66">
<a:ObjectID>1989CE24-57F2-422D-B74D-FE835C601B12</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1668409821</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758291</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>9825CA7F-4C39-40CC-A6A8-A3B706A3F650</a:ObjectID>
<a:Name>分组ID</a:Name>
<a:Code>group_id</a:Code>
<a:CreationDate>1675758248</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>分组ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>BDD6AD2B-3D2E-43FF-BDC6-A27189071D2F</a:ObjectID>
<a:Name>分组实例名称</a:Name>
<a:Code>group_instance_name</a:Code>
<a:CreationDate>1668411679</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>分组实例名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>8AA523C1-DF22-4236-91DE-3F00CC380DC2</a:ObjectID>
<a:Name>分组实例说明</a:Name>
<a:Code>group_instance_desc</a:Code>
<a:CreationDate>1670899371</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>分组实例说明</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>C9D1D5A8-92DE-4017-AC3C-187C162EAD47</a:ObjectID>
<a:Name>是否字典</a:Name>
<a:Code>dic_flag</a:Code>
<a:CreationDate>1675757591</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>是否字典</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o71">
<a:ObjectID>9EDDC511-A016-4699-8044-CECED132632A</a:ObjectID>
<a:Name>存储类型</a:Name>
<a:Code>storage_type</a:Code>
<a:CreationDate>1675757591</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>存储类型</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o72">
<a:ObjectID>BEACD8AF-7445-4794-9E2A-DA9FF2316C4C</a:ObjectID>
<a:Name>运算规则</a:Name>
<a:Code>operational_rule</a:Code>
<a:CreationDate>1675757591</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>运算规则</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o73">
<a:ObjectID>C4309D07-65CD-435E-A5B5-7394AF219B8C</a:ObjectID>
<a:Name>保留小数</a:Name>
<a:Code>decimal_places</a:Code>
<a:CreationDate>1675757591</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>保留小数</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o74">
<a:ObjectID>5E144781-D4C3-4F13-817B-CAF066B26CDD</a:ObjectID>
<a:Name>显示格式</a:Name>
<a:Code>show_format</a:Code>
<a:CreationDate>1675757591</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1681366999</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>显示格式</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o75">
<a:ObjectID>CB11FA32-1FF6-4293-BD2C-C37B172E86E1</a:ObjectID>
<a:Name>前缀字符</a:Name>
<a:Code>prefix_char</a:Code>
<a:CreationDate>1675757591</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1681366999</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>前缀字符</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>9914E11D-6A7E-4471-9278-C24071119D05</a:ObjectID>
<a:Name>后缀字符</a:Name>
<a:Code>suffix_char</a:Code>
<a:CreationDate>1675757591</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1681366999</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>后缀字符</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>8721B3CB-E5F3-4E8E-9106-EA7D6C1D0759</a:ObjectID>
<a:Name>原数据示例</a:Name>
<a:Code>raw_sample</a:Code>
<a:CreationDate>1681366426</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1681367001</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>原数据示例</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o78">
<a:ObjectID>92C50168-93E3-498A-92AC-EAB4B50D4A46</a:ObjectID>
<a:Name>显示示例</a:Name>
<a:Code>show_sample</a:Code>
<a:CreationDate>1681366426</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1681367005</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>显示示例</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>E280DE7F-C0CE-4017-AA9D-CB458C7DEBCC</a:ObjectID>
<a:Name>对齐方式</a:Name>
<a:Code>content_align</a:Code>
<a:CreationDate>1681366426</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1682220574</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>对齐方式</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o80">
<a:ObjectID>ECCDA57F-8675-4AC6-95C9-CB941184017A</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1668411679</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758144</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>状态</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o81">
<a:ObjectID>442734D9-17AD-4340-95AE-9213D3890BB6</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_by</a:Code>
<a:CreationDate>1668409821</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668409821</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o82">
<a:ObjectID>F0F020CD-F79A-43FD-8619-9031A029F09F</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1668409821</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668409821</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o83">
<a:ObjectID>15CA3DF5-A637-4D6A-BEC9-6895501AB49D</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_by</a:Code>
<a:CreationDate>1668409821</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668409821</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>BF7F1AE7-BC0C-478B-8CE6-DC7678871275</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1668409821</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668409821</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o85">
<a:ObjectID>81758C1D-8D58-41EC-86B4-7EE1E27C36BD</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1668409821</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1668409821</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o66"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o85"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o42">
<a:ObjectID>0ED0BD86-0928-4DC9-B847-81692E94AB61</a:ObjectID>
<a:Name>字典值表</a:Name>
<a:Code>cube_dic_item</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>字典值表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o86">
<a:ObjectID>07E00A8B-D9CA-4A64-BE5E-21B40FFEA86C</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758493</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>0381EA7A-E920-40F4-A491-60D70242A14A</a:ObjectID>
<a:Name>字典ID</a:Name>
<a:Code>dic_id</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>字典ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>2138DD2D-30D2-4A41-B957-1A15B2DD2363</a:ObjectID>
<a:Name>存储编码</a:Name>
<a:Code>dic_code</a:Code>
<a:CreationDate>1675758340</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>存储编码</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>B54037A8-8F85-412A-904A-F6A0F7B9CE24</a:ObjectID>
<a:Name>显示文字</a:Name>
<a:Code>dic_label</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758536</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>显示文字</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>761C778F-FDA6-4FAA-9B93-B6B0C55B8DA1</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758315</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>状态</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>62BE64A7-B7F8-4BCF-9480-C2F30D3D1AD4</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_by</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758315</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>5AEBF2E7-2CDF-4977-8CF6-3DAD2CF8BAF0</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758315</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o93">
<a:ObjectID>6674833B-9A7C-413C-9E98-7D836785A805</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_by</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758315</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o94">
<a:ObjectID>FA22B260-CBE7-4E27-87ED-84DFEDDF115F</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758315</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o95">
<a:ObjectID>4ED1BF62-C225-4E5B-9E9F-272449F09422</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1675758315</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758315</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o86"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o95"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o43">
<a:ObjectID>FD2C629B-7535-4FF5-AF1D-84A7E2663FD1</a:ObjectID>
<a:Name>数据源表</a:Name>
<a:Code>cube_source</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据源表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o96">
<a:ObjectID>5CECB0F7-2456-4638-AA48-4F7B45C561BE</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676600835</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>5DE4E68C-C9AA-4005-B83E-7AF088BACB10</a:ObjectID>
<a:Name>数据源名称</a:Name>
<a:Code>source_name</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据源名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>FA88B057-141B-4E87-A7F3-4E328E3DB354</a:ObjectID>
<a:Name>数据源类型</a:Name>
<a:Code>source_type</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据源类型</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>CFEA7A04-2371-4D7C-A247-F9B4056AE180</a:ObjectID>
<a:Name>数据源配置</a:Name>
<a:Code>source_config</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据源配置</a:Comment>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>D4836B5B-CA27-4685-A91C-EF42078C7B2C</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_by</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676600835</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o101">
<a:ObjectID>7B982975-1584-4553-829F-DC4A241E5AC3</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676600835</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o102">
<a:ObjectID>AA80EC5A-945B-47E4-9129-5F4DF8B8DC73</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_by</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676600835</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o103">
<a:ObjectID>DDD0BAF3-3A62-43BC-BF88-E1BBF92F7948</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676600835</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o104">
<a:ObjectID>F5F21522-E731-40DE-879C-87FEF32DF1BF</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1676600835</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676600835</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o96"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o104"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o44">
<a:ObjectID>AB25E0A3-E074-464B-A9D9-9A76EBD51613</a:ObjectID>
<a:Name>数据视图表</a:Name>
<a:Code>cube_view</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据视图表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o105">
<a:ObjectID>B203EF57-582F-428D-8179-21B3B9D98B67</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603567</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>D0749AA3-F405-4063-B72D-92656E9779F5</a:ObjectID>
<a:Name>目录ID</a:Name>
<a:Code>directory_id</a:Code>
<a:CreationDate>1676601145</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>目录ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>63AD52C3-9B54-4198-9E0E-1EE8759C5382</a:ObjectID>
<a:Name>数据源ID</a:Name>
<a:Code>source_id</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据源ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o108">
<a:ObjectID>075A977E-1602-49BD-83A5-75D21AB9412E</a:ObjectID>
<a:Name>视图名称</a:Name>
<a:Code>view_name</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>视图名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>825B3AB5-C0D3-4F47-A48C-0110E8ACC708</a:ObjectID>
<a:Name>SQL脚本</a:Name>
<a:Code>view_script</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>SQL脚本</a:Comment>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>C5D42787-37A4-4C06-B0D8-02182A947B49</a:ObjectID>
<a:Name>视图元数据</a:Name>
<a:Code>view_meta</a:Code>
<a:CreationDate>1676601145</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>视图元数据</a:Comment>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>AE55A54F-8E75-432A-9EE5-5BC75A28A481</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1676601145</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>状态</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>E1E969AE-6FFA-4149-A91D-CF010A1B6042</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_by</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601116</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>E9EBA863-7F75-48AE-A980-3014D0BB05EE</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601116</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o114">
<a:ObjectID>36EA73A4-0496-41D1-BBF7-05E662BF2221</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_by</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601116</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>E1CADB6E-B457-4FF0-8C68-26DF0B83FF92</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601116</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o116">
<a:ObjectID>21DDBFB7-FFD6-419D-8A6B-A63582571C7A</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1676601116</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601116</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o105"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o116"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o45">
<a:ObjectID>3292F2F5-37A2-4C99-A7C3-851BEA50DB8C</a:ObjectID>
<a:Name>二维数据表</a:Name>
<a:Code>cube_2d_table</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1684132942</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>二维数据表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o117">
<a:ObjectID>F171E2F3-9F68-4224-A52E-C0BB449849BE</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1678871378</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o118">
<a:ObjectID>9A57B04C-A9DA-4C96-A06B-BFC67437A2C4</a:ObjectID>
<a:Name>目录ID</a:Name>
<a:Code>directory_id</a:Code>
<a:CreationDate>1676601843</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>目录ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>C4A6429B-4EE1-4C0C-9DE2-69C8C4FD1DF6</a:ObjectID>
<a:Name>表格名称</a:Name>
<a:Code>table_name</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>表格名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>1B4721C7-D1D4-4865-A523-045DB5DCEF77</a:ObjectID>
<a:Name>建表方式</a:Name>
<a:Code>create_mode</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>建表方式</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>A53D5178-F437-46C2-841B-31F0FBD90E99</a:ObjectID>
<a:Name>数据视图ID</a:Name>
<a:Code>view_id</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据视图ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>F0B99CCF-0B3D-4F32-BF9C-F4A4D89391A8</a:ObjectID>
<a:Name>表格元数据</a:Name>
<a:Code>table_meta</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>表格元数据</a:Comment>
<a:DataType>text</a:DataType>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>7D20810D-C904-4CF2-867A-C5BE44FE9142</a:ObjectID>
<a:Name>子表标记</a:Name>
<a:Code>child_flag</a:Code>
<a:CreationDate>1676604146</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>子表标记</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>37949FD5-78C4-445E-8443-96BACA2BF4B8</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>状态</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>57492561-4F29-433A-946D-1342AEBDE9D8</a:ObjectID>
<a:Name>父ID</a:Name>
<a:Code>parent_id</a:Code>
<a:CreationDate>1684132734</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687931468</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>父ID</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>960E2CC0-2E11-4BB8-BA75-D7847ECDC91A</a:ObjectID>
<a:Name>类型</a:Name>
<a:Code>type</a:Code>
<a:CreationDate>1684132734</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687931468</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>类型</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>81776B77-85E8-4946-A16A-6CA80C62E85F</a:ObjectID>
<a:Name>路径</a:Name>
<a:Code>ancestors</a:Code>
<a:CreationDate>1684132734</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687931468</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>路径</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>5985D67A-3AAA-49BE-9A0D-1A04A84395E0</a:ObjectID>
<a:Name>内存表名</a:Name>
<a:Code>mem_table_name</a:Code>
<a:CreationDate>1684132734</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687931468</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>内存表名</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o129">
<a:ObjectID>87AC9137-DBAB-4DDC-93CC-806A431A54C6</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_by</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601636</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o130">
<a:ObjectID>87C657BA-20C8-45C9-A4F5-35E9961330AA</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601636</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o131">
<a:ObjectID>07CB8810-4262-4F12-9734-76F1EB035C74</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_by</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601636</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o132">
<a:ObjectID>C417913E-7A74-4610-9A4D-77E5C3F1C82C</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601636</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o133">
<a:ObjectID>D0544930-08A9-4FB3-919B-031965F2F8DB</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1676601636</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676601636</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o117"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o133"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o46">
<a:ObjectID>18DEC2D4-141C-450B-AE97-653142757237</a:ObjectID>
<a:Name>数据源配置详情</a:Name>
<a:Code>t_source_config</a:Code>
<a:CreationDate>1676602099</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据源配置详情</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o134">
<a:ObjectID>111F083E-F6AD-47D4-8C5E-E813D56E10E1</a:ObjectID>
<a:Name>连接串</a:Name>
<a:Code>url</a:Code>
<a:CreationDate>1676602099</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>连接串</a:Comment>
<a:DataType>char</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o135">
<a:ObjectID>921A0204-5B03-4874-B9EA-27DE4CB7F456</a:ObjectID>
<a:Name>用户</a:Name>
<a:Code>username</a:Code>
<a:CreationDate>1676602099</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1677221738</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>用户</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o136">
<a:ObjectID>D8D8AFD3-123C-4FDB-AD54-EFAF7F1BA896</a:ObjectID>
<a:Name>密码</a:Name>
<a:Code>password</a:Code>
<a:CreationDate>1676602099</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>密码</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o137">
<a:ObjectID>9B9E52C2-F2E7-4B98-B58D-3AF15EA24266</a:ObjectID>
<a:Name>驱动</a:Name>
<a:Code>driverClass</a:Code>
<a:CreationDate>1676602099</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>驱动</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o138">
<a:ObjectID>863B6752-F416-4B16-9FC1-1C9D984CFE42</a:ObjectID>
<a:Name>属性集合</a:Name>
<a:Code>properties</a:Code>
<a:CreationDate>1676602099</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>属性集合</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o139">
<a:ObjectID>ECB38005-2E8C-46B6-9B08-83D110BC83C5</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1676602099</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603598</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o134"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o139"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o47">
<a:ObjectID>E4449E09-0045-4E29-B923-6D54B3688132</a:ObjectID>
<a:Name>数据视图元数据</a:Name>
<a:Code>t_view_meta</a:Code>
<a:CreationDate>1676602485</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据视图元数据</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o140">
<a:ObjectID>757DBB81-995D-4950-9E46-9D8B454F5DA0</a:ObjectID>
<a:Name>列序号</a:Name>
<a:Code>no</a:Code>
<a:CreationDate>1676602517</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>列序号</a:Comment>
<a:DataType>char</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o141">
<a:ObjectID>1138F2EF-0A5E-4D50-B9F2-BA4A379CFBA1</a:ObjectID>
<a:Name>列编码</a:Name>
<a:Code>code</a:Code>
<a:CreationDate>1676602485</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>列编码</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o142">
<a:ObjectID>E7F6D15F-E723-4DD2-896E-3199FC20802B</a:ObjectID>
<a:Name>列名称</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>1676602485</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>列名称</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o143">
<a:ObjectID>367DBB72-8A46-4B22-A76C-7F978D34D952</a:ObjectID>
<a:Name>列数据格式（原）</a:Name>
<a:Code>type_from</a:Code>
<a:CreationDate>1676602485</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>列数据格式（原）</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o144">
<a:ObjectID>2C0E4C52-1B6A-451A-BB9C-DDB6CFD8E2C8</a:ObjectID>
<a:Name>列数据格式（改）</a:Name>
<a:Code>type_to</a:Code>
<a:CreationDate>1676602517</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>列数据格式（改）</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o145">
<a:ObjectID>8BBD9F15-A40C-4D67-9874-9D4BED3E0668</a:ObjectID>
<a:Name>唯一编号</a:Name>
<a:Code>sid</a:Code>
<a:CreationDate>1676602485</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>唯一编号</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o146">
<a:ObjectID>A43B2441-F614-4387-86E3-DD16E737922D</a:ObjectID>
<a:Name>筛选</a:Name>
<a:Code>condition</a:Code>
<a:CreationDate>1676602485</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>筛选</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>CDE9351E-A3E2-43F8-9FD2-2F12F4496BED</a:ObjectID>
<a:Name>筛选+是否可空</a:Name>
<a:Code>condition.empty</a:Code>
<a:CreationDate>1676602517</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>筛选+是否可空</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>D178ACD1-9A8F-46D5-BF42-97A978E35866</a:ObjectID>
<a:Name>筛选+模糊查询</a:Name>
<a:Code>condition.like</a:Code>
<a:CreationDate>1676602517</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>筛选+模糊查询</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o149">
<a:ObjectID>3CCAD252-33E2-40B5-966D-8FAFED1F5463</a:ObjectID>
<a:Name>筛选+数据范围？</a:Name>
<a:Code>condition.range</a:Code>
<a:CreationDate>1676602517</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>筛选+数据范围？</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o150">
<a:ObjectID>391B6B27-E28C-44BE-99BB-654C0BCA8222</a:ObjectID>
<a:Name>系统参数</a:Name>
<a:Code>system</a:Code>
<a:CreationDate>1676602517</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>系统参数</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o151">
<a:ObjectID>DE76C497-3361-4148-B51B-6061FED00E55</a:ObjectID>
<a:Name>系统参数+编码</a:Name>
<a:Code>system.key</a:Code>
<a:CreationDate>1676602517</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>系统参数+编码</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
<o:Column Id="o152">
<a:ObjectID>F14DF2B4-4536-482F-AC97-B498F1C00D8D</a:ObjectID>
<a:Name>系统参数+值</a:Name>
<a:Code>system.value</a:Code>
<a:CreationDate>1676602517</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>系统参数+值</a:Comment>
<a:DataType>char</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o153">
<a:ObjectID>084BAFA4-8255-4DC9-BB94-9E3D3B0B78A2</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1676602485</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603604</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o140"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o153"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o49">
<a:ObjectID>2BFE6D79-FBF1-4A54-AA0B-1AFF5DD41139</a:ObjectID>
<a:Name>二维父子关联表</a:Name>
<a:Code>cube_2d_table_rel</a:Code>
<a:CreationDate>1676603778</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>二维父子关联表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o154">
<a:ObjectID>F11612EE-0B34-4FFA-953B-19CB92FB4252</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1676603778</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603778</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>62A983A9-7623-4CB5-A263-5AB7212F0531</a:ObjectID>
<a:Name>父表ID</a:Name>
<a:Code>parent_table_id</a:Code>
<a:CreationDate>1676603778</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1678871438</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>父表ID</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>19857A7C-A251-48C8-B7F1-B13B6472AF2C</a:ObjectID>
<a:Name>子表ID</a:Name>
<a:Code>child_table_id</a:Code>
<a:CreationDate>1676603778</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1678871438</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>子表ID</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o157">
<a:ObjectID>EC6C2C7A-7F0C-47A6-B386-FED5064B60C5</a:ObjectID>
<a:Name>关联父表列序号</a:Name>
<a:Code>rel_table_no</a:Code>
<a:CreationDate>1676603778</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>关联父表列序号</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>12825C95-B664-4768-BD46-9336F4D72CEA</a:ObjectID>
<a:Name>关联父表列编码</a:Name>
<a:Code>rel_table_code</a:Code>
<a:CreationDate>1676603778</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>关联父表列编码</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>98933149-808F-4DC4-804C-4F403A4E611E</a:ObjectID>
<a:Name>关联父表列名称</a:Name>
<a:Code>rel_table_name</a:Code>
<a:CreationDate>1676603822</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676604257</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>关联父表列名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o160">
<a:ObjectID>AB0A506F-129A-4B61-9FBD-BB488F508D84</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1676603778</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603778</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o154"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o160"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o51">
<a:ObjectID>6D6E2EAB-484E-4F76-A79B-FFE2919F07F9</a:ObjectID>
<a:Name>数据权限配置表</a:Name>
<a:Code>cube_permission</a:Code>
<a:CreationDate>1687930940</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687933588</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据权限配置表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o161">
<a:ObjectID>32F24E7D-D64B-415B-A519-F113D9E14E5A</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1687930940</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687930940</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>FF8D9E25-AA0A-47F4-B790-BB6801A4DC3F</a:ObjectID>
<a:Name>角色ID</a:Name>
<a:Code>role_id</a:Code>
<a:CreationDate>1687930940</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687931468</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>角色ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>00F0561C-EFB5-4A5B-B4DB-D03BFBB84D77</a:ObjectID>
<a:Name>二维表ID</a:Name>
<a:Code>table_id</a:Code>
<a:CreationDate>1687930940</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687931468</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>二维表ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>7C355590-9B4E-42B1-893D-126C8EA051F3</a:ObjectID>
<a:Name>数据范围</a:Name>
<a:Code>data_scope</a:Code>
<a:CreationDate>1687930940</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687933645</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据范围</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o165">
<a:ObjectID>BA2CC0AD-8FF1-40E8-A9C9-9E702C33053A</a:ObjectID>
<a:Name>表格定义</a:Name>
<a:Code>table_definition</a:Code>
<a:CreationDate>1687930940</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687933645</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>表格定义</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o166">
<a:ObjectID>7C2B12E7-3714-4147-BB4E-F15C37DFC630</a:ObjectID>
<a:Name>数据编辑</a:Name>
<a:Code>data_edition</a:Code>
<a:CreationDate>1687930940</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687933645</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据编辑</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o167">
<a:ObjectID>A704DE60-FDCA-470B-A264-13FC1E5DAD6E</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1687930940</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1687930940</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o161"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o167"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o52">
<a:ObjectID>5BDB34C3-B3F7-4202-AE76-C8E7DEF91CA5</a:ObjectID>
<a:Name>维度成员表</a:Name>
<a:Code>cube_dim_instance</a:Code>
<a:CreationDate>1689925981</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703219086</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>维度成员表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o168">
<a:ObjectID>D0AA7FF8-A255-446F-83BF-DF600E15B017</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1689925981</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703220821</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>F65C1F5C-C2B8-4ED1-8064-44FF8A38419C</a:ObjectID>
<a:Name>维度目录ID</a:Name>
<a:Code>dim_directory_id</a:Code>
<a:CreationDate>1689925984</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703219086</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>维度目录ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o170">
<a:ObjectID>A578DF85-180F-4EDF-8309-868E874DB54A</a:ObjectID>
<a:Name>维度成员名称</a:Name>
<a:Code>dim_name</a:Code>
<a:CreationDate>1689925981</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689926748</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>维度成员名称</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o171">
<a:ObjectID>5322C69F-52C6-4A11-BC2B-2C60D96B0582</a:ObjectID>
<a:Name>维度成员代码</a:Name>
<a:Code>dim_code</a:Code>
<a:CreationDate>1689925981</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689926748</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>维度成员代码</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o172">
<a:ObjectID>B4F03E7A-FFE3-4804-A91D-80CC18641397</a:ObjectID>
<a:Name>显示序号</a:Name>
<a:Code>index_no</a:Code>
<a:CreationDate>1689925981</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689925981</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>显示序号</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o173">
<a:ObjectID>C35296CA-6FA1-453F-BB41-6D24E6263FB3</a:ObjectID>
<a:Name>上级维度成员ID</a:Name>
<a:Code>parent_id</a:Code>
<a:CreationDate>1689925981</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703220821</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>上级维度成员ID</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o174">
<a:ObjectID>C310BDF1-BD92-4630-8634-E2B5C389F34D</a:ObjectID>
<a:Name>节点路径</a:Name>
<a:Code>ancestors</a:Code>
<a:CreationDate>1689925981</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689925981</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>节点路径</a:Comment>
<a:DataType>varchar(512)</a:DataType>
<a:Length>512</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o175">
<a:ObjectID>414B8A7B-4E17-4124-A9B5-600291E622F7</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1689925981</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689925981</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o168"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o175"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o53">
<a:ObjectID>EDB6956D-CD07-40C6-9259-D0A794229CFF</a:ObjectID>
<a:Name>维度目录表</a:Name>
<a:Code>cube_dim_directory</a:Code>
<a:CreationDate>1689926468</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234642</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>维度目录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o176">
<a:ObjectID>D9C0489C-C1B6-41D6-858E-932BDAAF550D</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1689926468</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703220808</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>3BC7B2D2-EE49-48C8-B218-E3AAE9806753</a:ObjectID>
<a:Name>维度目录名称</a:Name>
<a:Code>dim_directory_name</a:Code>
<a:CreationDate>1689926468</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689926748</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>维度目录名称</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>8E1E900D-24CA-4D0A-A7A1-BEEDAD92627C</a:ObjectID>
<a:Name>维度目录类型</a:Name>
<a:Code>dim_directory_type</a:Code>
<a:CreationDate>1689926474</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689926748</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>维度目录类型</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>270A9270-9804-49FF-9ABB-230BC00E9BE6</a:ObjectID>
<a:Name>维度类型</a:Name>
<a:Code>dim_type</a:Code>
<a:CreationDate>1689926468</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689926748</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>维度类型</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>DE4DFF5B-9CEB-4511-8E27-46AD6D3C3135</a:ObjectID>
<a:Name>显示序号</a:Name>
<a:Code>index_no</a:Code>
<a:CreationDate>1689926468</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689926468</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>显示序号</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>F2E3B8AE-E029-4349-8D53-C4FA330A323A</a:ObjectID>
<a:Name>上级目录ID</a:Name>
<a:Code>parent_id</a:Code>
<a:CreationDate>1689926468</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703220808</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>上级目录ID</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o182">
<a:ObjectID>1F04617F-D6DE-4962-A6E9-F56A987869B6</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1689926468</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1689926468</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o176"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o182"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o54">
<a:ObjectID>01CBFD61-8DB0-49EE-8BA2-AF75D43A6804</a:ObjectID>
<a:Name>指标表</a:Name>
<a:Code>cube_ind</a:Code>
<a:CreationDate>1703234180</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234671</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>指标表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o183">
<a:ObjectID>87B2BE6A-0766-4210-ABA9-A28DFE387C0C</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1703234180</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234180</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>D0F39DF7-6E51-48D8-8FC3-AF3D38D4CCC5</a:ObjectID>
<a:Name>指标名称</a:Name>
<a:Code>ind_name</a:Code>
<a:CreationDate>1703234180</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234671</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>指标名称</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>CA24579A-168E-4256-AE4F-4C0D35AA1EC9</a:ObjectID>
<a:Name>指标类型</a:Name>
<a:Code>ind_type</a:Code>
<a:CreationDate>1703234180</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234671</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>指标类型</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o186">
<a:ObjectID>769BB7A5-2E01-4BE8-9B5F-6074C08E4170</a:ObjectID>
<a:Name>显示序号</a:Name>
<a:Code>index_no</a:Code>
<a:CreationDate>1703234180</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234180</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>显示序号</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>E19D941A-6348-433F-8986-3785A346B8A7</a:ObjectID>
<a:Name>上级目录ID</a:Name>
<a:Code>parent_id</a:Code>
<a:CreationDate>1703234180</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234180</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>上级目录ID</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o188">
<a:ObjectID>80C79BCA-9C92-4772-B039-608AF1A55E2F</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1703234180</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234180</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o183"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o188"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o55">
<a:ObjectID>B132FFB3-F93F-4275-9920-4886605F7F89</a:ObjectID>
<a:Name>多维数据表</a:Name>
<a:Code>cube_dim_table</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234671</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>多维数据表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o189">
<a:ObjectID>74F0ED1B-CA16-48DC-B7AF-0BE61BDE9628</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o190">
<a:ObjectID>1C84C201-34A2-44CC-98AC-D9AD1C72F1C1</a:ObjectID>
<a:Name>表格名称</a:Name>
<a:Code>table_name</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>表格名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o191">
<a:ObjectID>05BB271A-19DB-4096-B5AE-48CD7A0AC960</a:ObjectID>
<a:Name>建表方式</a:Name>
<a:Code>create_mode</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>建表方式</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o192">
<a:ObjectID>3ED296AA-4ADF-4438-ABC1-BA7E1496BEEE</a:ObjectID>
<a:Name>数据视图ID</a:Name>
<a:Code>view_id</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>数据视图ID</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o193">
<a:ObjectID>09A2D2C2-436C-44A3-B2DE-BECA6F7FF071</a:ObjectID>
<a:Name>父ID</a:Name>
<a:Code>parent_id</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>父ID</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o194">
<a:ObjectID>7E05BB58-091B-4815-84F3-832F4B39771C</a:ObjectID>
<a:Name>类型</a:Name>
<a:Code>type</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>类型</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o195">
<a:ObjectID>DD25E343-01B0-49B2-A8FA-4DE4A9FFA17B</a:ObjectID>
<a:Name>路径</a:Name>
<a:Code>ancestors</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>路径</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o196">
<a:ObjectID>7832CF55-A06E-491B-A5F2-CBF3AD4530EB</a:ObjectID>
<a:Name>内存表名</a:Name>
<a:Code>mem_table_name</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>内存表名</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>D67C3D86-FD11-4185-94F2-4901EAE14462</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>create_by</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>D2C97BA8-4E14-407D-80ED-569725FD69F4</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>create_time</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>D35D071A-4031-49A7-9390-BF9983F2A919</a:ObjectID>
<a:Name>更新人</a:Name>
<a:Code>update_by</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新人</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>CA8CDD4C-DD93-4041-95D5-7138D809F35F</a:ObjectID>
<a:Name>更新时间</a:Name>
<a:Code>update_time</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o201">
<a:ObjectID>91ECA230-9280-45EB-85DD-5BB7AEA7A707</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1703234202</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234202</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o189"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o201"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o56">
<a:ObjectID>B97D05E7-2AF9-4652-B3FD-AA126CC2F756</a:ObjectID>
<a:Name>多维表关联表</a:Name>
<a:Code>cube_dim_table_rel</a:Code>
<a:CreationDate>1703234240</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234671</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>多维表关联表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o202">
<a:ObjectID>D9FD5C05-42A9-4D33-A0B6-3BB2B592D534</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>id</a:Code>
<a:CreationDate>1703234240</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234642</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o203">
<a:ObjectID>894A3F92-D122-4775-8AD0-737CBC9686B3</a:ObjectID>
<a:Name>维度表ID</a:Name>
<a:Code>table_id</a:Code>
<a:CreationDate>1703234240</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234671</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>维度表ID</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o204">
<a:ObjectID>BBA4739F-0BBB-4447-A396-555E7BD1DB7D</a:ObjectID>
<a:Name>关联ID</a:Name>
<a:Code>rel_id</a:Code>
<a:CreationDate>1703234240</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234671</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>关联ID</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o205">
<a:ObjectID>6C37CBF3-62D7-47C6-A68C-64C23376507B</a:ObjectID>
<a:Name>关联类型</a:Name>
<a:Code>rel_type</a:Code>
<a:CreationDate>1703234240</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234671</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Comment>关联类型</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o206">
<a:ObjectID>487A6C6D-1488-4DB0-9F61-87894ED2AC0F</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1703234240</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234240</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o202"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o206"/>
</c:PrimaryKey>
</o:Table>
</c:Tables>
<c:References>
<o:Reference Id="o8">
<a:ObjectID>ED839BEC-AEF1-47AD-B0F3-087DE16B314F</a:ObjectID>
<a:Name>Reference_4</a:Name>
<a:Code>Reference_4</a:Code>
<a:CreationDate>1668479322</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758291</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o40"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o41"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o65"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o207">
<a:ObjectID>9E2F5CBD-0CFE-4628-AAE7-11BACB0915C9</a:ObjectID>
<a:CreationDate>1675758216</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758291</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o57"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o67"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o11">
<a:ObjectID>A0BAB957-F973-4905-BF03-E02D959F27D4</a:ObjectID>
<a:Name>Reference_2</a:Name>
<a:Code>Reference_2</a:Code>
<a:CreationDate>1675758486</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758493</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o41"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o42"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o85"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o208">
<a:ObjectID>DE222BEB-DBF8-4CA4-AB08-7011BDAC1636</a:ObjectID>
<a:CreationDate>1675758486</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1675758493</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o66"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o87"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o15">
<a:ObjectID>5242B843-5658-4476-BC3A-F15C1EFFEB5B</a:ObjectID>
<a:Name>Reference_3</a:Name>
<a:Code>Reference_3</a:Code>
<a:CreationDate>1676603559</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603567</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o43"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o44"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o104"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o209">
<a:ObjectID>FA923031-EB0F-4FD8-A56B-01AC1E843BF8</a:ObjectID>
<a:CreationDate>1676603559</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603567</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o96"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o107"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o18">
<a:ObjectID>28DA443E-8CE5-4439-BB4C-F9ED29B4C24B</a:ObjectID>
<a:Name>Reference_5</a:Name>
<a:Code>Reference_5</a:Code>
<a:CreationDate>1676603574</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603580</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o44"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o45"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o116"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o210">
<a:ObjectID>442D5D2B-56E9-4E7E-B17F-5C249FA82EDC</a:ObjectID>
<a:CreationDate>1676603574</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603580</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o105"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o121"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o21">
<a:ObjectID>C80EBB4F-6FC4-4CE6-9514-7DF8918F5B16</a:ObjectID>
<a:Name>Reference_6</a:Name>
<a:Code>Reference_6</a:Code>
<a:CreationDate>1676603627</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603635</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o46"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o43"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o139"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o211">
<a:ObjectID>8113121D-DC68-42B7-8E63-829A4B9814FB</a:ObjectID>
<a:CreationDate>1676603627</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603635</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o134"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o99"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o24">
<a:ObjectID>2EE69331-4E39-4F7F-B0BC-E5BD02D3A9DB</a:ObjectID>
<a:Name>Reference_7</a:Name>
<a:Code>Reference_7</a:Code>
<a:CreationDate>1676603646</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603661</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o47"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o44"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o153"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o212">
<a:ObjectID>B104C2C6-BC62-4853-A486-590AF443A527</a:ObjectID>
<a:CreationDate>1676603646</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603661</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o140"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o110"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o26">
<a:ObjectID>13D609FE-D69D-431F-B33D-7C9BDAA6E2E3</a:ObjectID>
<a:Name>Reference_8</a:Name>
<a:Code>Reference_8</a:Code>
<a:CreationDate>1676603648</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603686</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o47"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o45"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o153"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o213">
<a:ObjectID>04596570-F339-4F21-9926-61EEBE4262C3</a:ObjectID>
<a:CreationDate>1676603648</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1676603686</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o140"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o122"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o30">
<a:ObjectID>F0AFEF90-D1AE-49EE-BAE0-E39708461645</a:ObjectID>
<a:Name>Reference_9</a:Name>
<a:Code>Reference_9</a:Code>
<a:CreationDate>1703219073</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703219086</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o53"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o52"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o182"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o214">
<a:ObjectID>7FBB81C1-6175-4DC0-BEF2-2ACA4097A93A</a:ObjectID>
<a:CreationDate>1703219073</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703219086</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o176"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o169"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o215">
<a:ObjectID>EAB699A7-63E0-4EF1-B2F9-82A277823EEA</a:ObjectID>
<a:Name>Reference_10</a:Name>
<a:Code>Reference_10</a:Code>
<a:CreationDate>1703234582</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234582</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o56"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o53"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o206"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o216">
<a:ObjectID>CFE16239-D93D-4220-BD6F-CF7F5B27E1A3</a:ObjectID>
<a:CreationDate>1703234582</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234582</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o202"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o176"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o217">
<a:ObjectID>AB87FE75-322A-4020-8314-E9BAEAF03903</a:ObjectID>
<a:Name>Reference_11</a:Name>
<a:Code>Reference_11</a:Code>
<a:CreationDate>1703234583</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234583</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o56"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o54"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o206"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o218">
<a:ObjectID>9E9890E6-9A19-4DD4-9E23-C8507DE98B96</a:ObjectID>
<a:CreationDate>1703234583</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234583</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o202"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o183"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o34">
<a:ObjectID>63E2A4DB-4B42-40E4-A452-3D94DC437604</a:ObjectID>
<a:Name>Reference_12</a:Name>
<a:Code>Reference_12</a:Code>
<a:CreationDate>1703234587</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234594</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o55"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o56"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o201"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o219">
<a:ObjectID>8EC0B61C-7B1C-4E62-8D4C-C4A26C93EFAD</a:ObjectID>
<a:CreationDate>1703234587</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234594</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o189"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o203"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o36">
<a:ObjectID>4CFB4D41-8111-4B71-9B9A-2638D940B562</a:ObjectID>
<a:Name>Reference_13</a:Name>
<a:Code>Reference_13</a:Code>
<a:CreationDate>1703234625</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234642</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o53"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o56"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o182"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o220">
<a:ObjectID>79A996B1-8761-407E-934F-9CF6C8647FA8</a:ObjectID>
<a:CreationDate>1703234625</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234642</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o176"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o204"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o39">
<a:ObjectID>5E0DA40D-E328-4B08-968C-4965667C8A9C</a:ObjectID>
<a:Name>Reference_14</a:Name>
<a:Code>Reference_14</a:Code>
<a:CreationDate>1703234627</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234636</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o54"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o56"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o188"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o221">
<a:ObjectID>5F7383C1-0956-4031-87C1-B3D560D97ED2</a:ObjectID>
<a:CreationDate>1703234627</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1703234636</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<c:Object1>
<o:Column Ref="o183"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o204"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
</c:References>
<c:DefaultGroups>
<o:Group Id="o222">
<a:ObjectID>46EC3E2A-6CBF-421A-9DA8-6BCCEDEC7DF5</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>0</a:CreationDate>
<a:Creator/>
<a:ModificationDate>0</a:ModificationDate>
<a:Modifier/>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o223">
<a:ObjectID>C4227B3E-242E-4F97-AA24-4498CD1E3AAF</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1670899889</a:CreationDate>
<a:Creator>howen</a:Creator>
<a:ModificationDate>1670899889</a:ModificationDate>
<a:Modifier>howen</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/mysql50.xdb</a:TargetModelURL>
<a:TargetModelID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>